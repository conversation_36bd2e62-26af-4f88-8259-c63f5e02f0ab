#include <QApplication>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QWidget>
#include "NotificationWindow.h"
#include "ProgressWidget.h"

int main(int argc, char* argv[])
{
    QApplication a(argc, argv);

    // 创建主窗口，包含通知和进度两个标签页
    QWidget mainWindow;
    mainWindow.setWindowTitle("BaseWidget - Components Demo");
    mainWindow.resize(1200, 800);

    QVBoxLayout* layout = new QVBoxLayout(&mainWindow);
    QTabWidget* tabWidget = new QTabWidget(&mainWindow);

    // 创建通知窗口标签页
    NotificationWindow* notificationWindow = new NotificationWindow();
    tabWidget->addTab(notificationWindow, "Notifications");

    // 创建进度组件标签页
    ProgressWidget* progressWidget = new ProgressWidget();
    tabWidget->addTab(progressWidget, "Progress Manager");

    layout->addWidget(tabWidget);
    mainWindow.show();

    return a.exec();
}