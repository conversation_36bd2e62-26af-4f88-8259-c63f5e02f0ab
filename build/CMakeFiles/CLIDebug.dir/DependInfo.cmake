
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/CLionProjects/BaseWidget/build/CLIDebug_autogen/mocs_compilation.cpp" "CMakeFiles/CLIDebug.dir/CLIDebug_autogen/mocs_compilation.cpp.o" "gcc" "CMakeFiles/CLIDebug.dir/CLIDebug_autogen/mocs_compilation.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressItem.cpp.o" "gcc" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressItem.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressItemWidget.cpp.o" "gcc" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressItemWidget.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressListWidget.cpp.o" "gcc" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressListWidget.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressManager.cpp.o" "gcc" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressManager.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressWidget.cpp.o" "gcc" "CMakeFiles/CLIDebug.dir/src/componets/progress/ProgressWidget.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/cli_debug.cpp" "CMakeFiles/CLIDebug.dir/src/componets/progress/tests/cli_debug.cpp.o" "gcc" "CMakeFiles/CLIDebug.dir/src/componets/progress/tests/cli_debug.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
