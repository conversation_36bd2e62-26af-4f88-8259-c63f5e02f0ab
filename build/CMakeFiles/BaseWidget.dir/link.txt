/usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o CMakeFiles/BaseWidget.dir/main.cpp.o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o CMakeFiles/BaseWidget.dir/src/componets/notification/BalloonNotification.cpp.o CMakeFiles/BaseWidget.dir/src/componets/notification/BalloonNotificationManager.cpp.o CMakeFiles/BaseWidget.dir/src/componets/progress/ProgressItem.cpp.o CMakeFiles/BaseWidget.dir/src/componets/progress/ProgressItemWidget.cpp.o CMakeFiles/BaseWidget.dir/src/componets/progress/ProgressListWidget.cpp.o CMakeFiles/BaseWidget.dir/src/componets/progress/ProgressManager.cpp.o CMakeFiles/BaseWidget.dir/src/componets/progress/ProgressWidget.cpp.o -o BaseWidget -F/opt/homebrew/Cellar/qt@5/5.15.17/lib  /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework/QtWidgets /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework/QtGui /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework/QtCore
