/usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/DebugPauseAll.dir/DebugPauseAll_autogen/mocs_compilation.cpp.o CMakeFiles/DebugPauseAll.dir/src/componets/progress/tests/debug_pause_all.cpp.o CMakeFiles/DebugPauseAll.dir/src/componets/progress/ProgressItem.cpp.o CMakeFiles/DebugPauseAll.dir/src/componets/progress/ProgressItemWidget.cpp.o CMakeFiles/DebugPauseAll.dir/src/componets/progress/ProgressListWidget.cpp.o CMakeFiles/DebugPauseAll.dir/src/componets/progress/ProgressManager.cpp.o CMakeFiles/DebugPauseAll.dir/src/componets/progress/ProgressWidget.cpp.o -o DebugPauseAll -F/opt/homebrew/Cellar/qt@5/5.15.17/lib  /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework/QtWidgets /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework/QtGui /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework/QtCore
