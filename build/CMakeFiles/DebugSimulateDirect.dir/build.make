# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/build

# Include any dependencies generated for this target.
include CMakeFiles/DebugSimulateDirect.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/DebugSimulateDirect.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/DebugSimulateDirect.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/DebugSimulateDirect.dir/flags.make

CMakeFiles/DebugSimulateDirect.dir/codegen:
.PHONY : CMakeFiles/DebugSimulateDirect.dir/codegen

CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o: CMakeFiles/DebugSimulateDirect.dir/flags.make
CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o: DebugSimulateDirect_autogen/mocs_compilation.cpp
CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o: CMakeFiles/DebugSimulateDirect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o -MF CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/build/DebugSimulateDirect_autogen/mocs_compilation.cpp

CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/build/DebugSimulateDirect_autogen/mocs_compilation.cpp > CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.i

CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/build/DebugSimulateDirect_autogen/mocs_compilation.cpp -o CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.s

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o: CMakeFiles/DebugSimulateDirect.dir/flags.make
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/debug_simulate_direct.cpp
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o: CMakeFiles/DebugSimulateDirect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o -MF CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o.d -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/debug_simulate_direct.cpp

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/debug_simulate_direct.cpp > CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.i

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/debug_simulate_direct.cpp -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.s

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o: CMakeFiles/DebugSimulateDirect.dir/flags.make
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o: CMakeFiles/DebugSimulateDirect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o -MF CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o.d -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp > CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.i

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.s

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o: CMakeFiles/DebugSimulateDirect.dir/flags.make
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o: CMakeFiles/DebugSimulateDirect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o -MF CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o.d -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp > CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.i

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.s

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o: CMakeFiles/DebugSimulateDirect.dir/flags.make
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o: CMakeFiles/DebugSimulateDirect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o -MF CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o.d -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp > CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.i

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.s

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o: CMakeFiles/DebugSimulateDirect.dir/flags.make
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o: CMakeFiles/DebugSimulateDirect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o -MF CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o.d -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp > CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.i

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.s

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o: CMakeFiles/DebugSimulateDirect.dir/flags.make
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp
CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o: CMakeFiles/DebugSimulateDirect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o -MF CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o.d -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp > CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.i

CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp -o CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.s

# Object files for target DebugSimulateDirect
DebugSimulateDirect_OBJECTS = \
"CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o" \
"CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o" \
"CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o" \
"CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o" \
"CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o" \
"CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o"

# External object files for target DebugSimulateDirect
DebugSimulateDirect_EXTERNAL_OBJECTS =

DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/DebugSimulateDirect_autogen/mocs_compilation.cpp.o
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/tests/debug_simulate_direct.cpp.o
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItem.cpp.o
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressItemWidget.cpp.o
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressListWidget.cpp.o
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressManager.cpp.o
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/src/componets/progress/ProgressWidget.cpp.o
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/build.make
DebugSimulateDirect: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework/QtWidgets
DebugSimulateDirect: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework/QtGui
DebugSimulateDirect: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework/QtCore
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX executable DebugSimulateDirect"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/DebugSimulateDirect.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/DebugSimulateDirect.dir/build: DebugSimulateDirect
.PHONY : CMakeFiles/DebugSimulateDirect.dir/build

CMakeFiles/DebugSimulateDirect.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/DebugSimulateDirect.dir/cmake_clean.cmake
.PHONY : CMakeFiles/DebugSimulateDirect.dir/clean

CMakeFiles/DebugSimulateDirect.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget /Users/<USER>/CLionProjects/BaseWidget /Users/<USER>/CLionProjects/BaseWidget/build /Users/<USER>/CLionProjects/BaseWidget/build /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/DebugSimulateDirect.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/DebugSimulateDirect.dir/depend

