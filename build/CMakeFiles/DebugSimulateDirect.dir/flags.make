# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB

CXX_INCLUDES = -I/Users/<USER>/CLionProjects/BaseWidget/src/componets/notification -I/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress -isystem /Users/<USER>/CLionProjects/BaseWidget/build/DebugSimulateDirect_autogen/include -iframework /opt/homebrew/Cellar/qt@5/5.15.17/lib -isystem /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework/Headers -isystem /opt/homebrew/Cellar/qt@5/5.15.17/./mkspecs/macx-clang -isystem /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework/Headers -isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/OpenGL.framework/Headers -isystem /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework/Headers

CXX_FLAGSarm64 = -std=gnu++17 -arch arm64 -fPIC

CXX_FLAGS = -std=gnu++17 -arch arm64 -fPIC

