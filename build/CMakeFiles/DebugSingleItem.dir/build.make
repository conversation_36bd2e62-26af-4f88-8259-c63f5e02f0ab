# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/build

# Include any dependencies generated for this target.
include CMakeFiles/DebugSingleItem.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/DebugSingleItem.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/DebugSingleItem.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/DebugSingleItem.dir/flags.make

CMakeFiles/DebugSingleItem.dir/codegen:
.PHONY : CMakeFiles/DebugSingleItem.dir/codegen

CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o: CMakeFiles/DebugSingleItem.dir/flags.make
CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o: DebugSingleItem_autogen/mocs_compilation.cpp
CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o: CMakeFiles/DebugSingleItem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o -MF CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/build/DebugSingleItem_autogen/mocs_compilation.cpp

CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/build/DebugSingleItem_autogen/mocs_compilation.cpp > CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.i

CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/build/DebugSingleItem_autogen/mocs_compilation.cpp -o CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.s

CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o: CMakeFiles/DebugSingleItem.dir/flags.make
CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/debug_single_item.cpp
CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o: CMakeFiles/DebugSingleItem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o -MF CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o.d -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/debug_single_item.cpp

CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/debug_single_item.cpp > CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.i

CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/debug_single_item.cpp -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.s

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o: CMakeFiles/DebugSingleItem.dir/flags.make
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o: CMakeFiles/DebugSingleItem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o -MF CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o.d -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp > CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.i

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.s

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o: CMakeFiles/DebugSingleItem.dir/flags.make
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o: CMakeFiles/DebugSingleItem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o -MF CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o.d -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp > CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.i

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.s

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o: CMakeFiles/DebugSingleItem.dir/flags.make
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o: CMakeFiles/DebugSingleItem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o -MF CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o.d -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp > CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.i

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.s

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o: CMakeFiles/DebugSingleItem.dir/flags.make
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o: CMakeFiles/DebugSingleItem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o -MF CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o.d -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp > CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.i

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.s

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o: CMakeFiles/DebugSingleItem.dir/flags.make
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp
CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o: CMakeFiles/DebugSingleItem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o -MF CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o.d -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp > CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.i

CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp -o CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.s

# Object files for target DebugSingleItem
DebugSingleItem_OBJECTS = \
"CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o" \
"CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o" \
"CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o" \
"CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o" \
"CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o" \
"CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o"

# External object files for target DebugSingleItem
DebugSingleItem_EXTERNAL_OBJECTS =

DebugSingleItem: CMakeFiles/DebugSingleItem.dir/DebugSingleItem_autogen/mocs_compilation.cpp.o
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/src/componets/progress/tests/debug_single_item.cpp.o
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItem.cpp.o
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressItemWidget.cpp.o
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressListWidget.cpp.o
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressManager.cpp.o
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/src/componets/progress/ProgressWidget.cpp.o
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/build.make
DebugSingleItem: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework/QtWidgets
DebugSingleItem: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework/QtGui
DebugSingleItem: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework/QtCore
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX executable DebugSingleItem"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/DebugSingleItem.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/DebugSingleItem.dir/build: DebugSingleItem
.PHONY : CMakeFiles/DebugSingleItem.dir/build

CMakeFiles/DebugSingleItem.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/DebugSingleItem.dir/cmake_clean.cmake
.PHONY : CMakeFiles/DebugSingleItem.dir/clean

CMakeFiles/DebugSingleItem.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget /Users/<USER>/CLionProjects/BaseWidget /Users/<USER>/CLionProjects/BaseWidget/build /Users/<USER>/CLionProjects/BaseWidget/build /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/DebugSingleItem.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/DebugSingleItem.dir/depend

