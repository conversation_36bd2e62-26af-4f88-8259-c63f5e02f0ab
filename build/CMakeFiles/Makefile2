# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/BaseWidget.dir/all
all: CMakeFiles/ProgressTests.dir/all
all: CMakeFiles/ManualTest.dir/all
all: CMakeFiles/DebugAddTask.dir/all
all: CMakeFiles/DebugPauseAll.dir/all
all: CMakeFiles/DebugSimulate.dir/all
all: CMakeFiles/EmptyTaskDeadlockTest.dir/all
all: CMakeFiles/SimpleEmptyTaskTest.dir/all
all: CMakeFiles/DebugSimulateDirect.dir/all
all: CMakeFiles/DebugStepByStep.dir/all
all: CMakeFiles/DebugSingleItem.dir/all
all: CMakeFiles/FinalSimulateTest.dir/all
all: CMakeFiles/CLIDebug.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/BaseWidget.dir/codegen
codegen: CMakeFiles/ProgressTests.dir/codegen
codegen: CMakeFiles/ManualTest.dir/codegen
codegen: CMakeFiles/DebugAddTask.dir/codegen
codegen: CMakeFiles/DebugPauseAll.dir/codegen
codegen: CMakeFiles/DebugSimulate.dir/codegen
codegen: CMakeFiles/EmptyTaskDeadlockTest.dir/codegen
codegen: CMakeFiles/SimpleEmptyTaskTest.dir/codegen
codegen: CMakeFiles/DebugSimulateDirect.dir/codegen
codegen: CMakeFiles/DebugStepByStep.dir/codegen
codegen: CMakeFiles/DebugSingleItem.dir/codegen
codegen: CMakeFiles/FinalSimulateTest.dir/codegen
codegen: CMakeFiles/CLIDebug.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/BaseWidget.dir/clean
clean: CMakeFiles/ProgressTests.dir/clean
clean: CMakeFiles/ManualTest.dir/clean
clean: CMakeFiles/DebugAddTask.dir/clean
clean: CMakeFiles/DebugPauseAll.dir/clean
clean: CMakeFiles/DebugSimulate.dir/clean
clean: CMakeFiles/EmptyTaskDeadlockTest.dir/clean
clean: CMakeFiles/SimpleEmptyTaskTest.dir/clean
clean: CMakeFiles/DebugSimulateDirect.dir/clean
clean: CMakeFiles/DebugStepByStep.dir/clean
clean: CMakeFiles/DebugSingleItem.dir/clean
clean: CMakeFiles/FinalSimulateTest.dir/clean
clean: CMakeFiles/CLIDebug.dir/clean
clean: CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/BaseWidget_autogen.dir/clean
clean: CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/ProgressTests_autogen.dir/clean
clean: CMakeFiles/ManualTest_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/ManualTest_autogen.dir/clean
clean: CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/DebugAddTask_autogen.dir/clean
clean: CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/DebugPauseAll_autogen.dir/clean
clean: CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/DebugSimulate_autogen.dir/clean
clean: CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/clean
clean: CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/SimpleEmptyTaskTest_autogen.dir/clean
clean: CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/DebugSimulateDirect_autogen.dir/clean
clean: CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/DebugStepByStep_autogen.dir/clean
clean: CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/DebugSingleItem_autogen.dir/clean
clean: CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/FinalSimulateTest_autogen.dir/clean
clean: CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/CLIDebug_autogen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/BaseWidget.dir

# All Build rule for target.
CMakeFiles/BaseWidget.dir/all: CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/all
CMakeFiles/BaseWidget.dir/all: CMakeFiles/BaseWidget_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11 "Built target BaseWidget"
.PHONY : CMakeFiles/BaseWidget.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/BaseWidget.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 12
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/BaseWidget.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/BaseWidget.dir/rule

# Convenience name for target.
BaseWidget: CMakeFiles/BaseWidget.dir/rule
.PHONY : BaseWidget

# codegen rule for target.
CMakeFiles/BaseWidget.dir/codegen: CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11 "Finished codegen for target BaseWidget"
.PHONY : CMakeFiles/BaseWidget.dir/codegen

# clean rule for target.
CMakeFiles/BaseWidget.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/clean
.PHONY : CMakeFiles/BaseWidget.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ProgressTests.dir

# All Build rule for target.
CMakeFiles/ProgressTests.dir/all: CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/all
CMakeFiles/ProgressTests.dir/all: CMakeFiles/ProgressTests_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests.dir/build.make CMakeFiles/ProgressTests.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests.dir/build.make CMakeFiles/ProgressTests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=85,86,87,88,89,90,91 "Built target ProgressTests"
.PHONY : CMakeFiles/ProgressTests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ProgressTests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ProgressTests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/ProgressTests.dir/rule

# Convenience name for target.
ProgressTests: CMakeFiles/ProgressTests.dir/rule
.PHONY : ProgressTests

# codegen rule for target.
CMakeFiles/ProgressTests.dir/codegen: CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests.dir/build.make CMakeFiles/ProgressTests.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=85,86,87,88,89,90,91 "Finished codegen for target ProgressTests"
.PHONY : CMakeFiles/ProgressTests.dir/codegen

# clean rule for target.
CMakeFiles/ProgressTests.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests.dir/build.make CMakeFiles/ProgressTests.dir/clean
.PHONY : CMakeFiles/ProgressTests.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ManualTest.dir

# All Build rule for target.
CMakeFiles/ManualTest.dir/all: CMakeFiles/ManualTest_autogen_timestamp_deps.dir/all
CMakeFiles/ManualTest.dir/all: CMakeFiles/ManualTest_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest.dir/build.make CMakeFiles/ManualTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest.dir/build.make CMakeFiles/ManualTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=78,79,80,81,82,83 "Built target ManualTest"
.PHONY : CMakeFiles/ManualTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ManualTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ManualTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/ManualTest.dir/rule

# Convenience name for target.
ManualTest: CMakeFiles/ManualTest.dir/rule
.PHONY : ManualTest

# codegen rule for target.
CMakeFiles/ManualTest.dir/codegen: CMakeFiles/ManualTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest.dir/build.make CMakeFiles/ManualTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=78,79,80,81,82,83 "Finished codegen for target ManualTest"
.PHONY : CMakeFiles/ManualTest.dir/codegen

# clean rule for target.
CMakeFiles/ManualTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest.dir/build.make CMakeFiles/ManualTest.dir/clean
.PHONY : CMakeFiles/ManualTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugAddTask.dir

# All Build rule for target.
CMakeFiles/DebugAddTask.dir/all: CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/all
CMakeFiles/DebugAddTask.dir/all: CMakeFiles/DebugAddTask_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask.dir/build.make CMakeFiles/DebugAddTask.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask.dir/build.make CMakeFiles/DebugAddTask.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=20,21,22,23,24,25 "Built target DebugAddTask"
.PHONY : CMakeFiles/DebugAddTask.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugAddTask.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugAddTask.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugAddTask.dir/rule

# Convenience name for target.
DebugAddTask: CMakeFiles/DebugAddTask.dir/rule
.PHONY : DebugAddTask

# codegen rule for target.
CMakeFiles/DebugAddTask.dir/codegen: CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask.dir/build.make CMakeFiles/DebugAddTask.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=20,21,22,23,24,25 "Finished codegen for target DebugAddTask"
.PHONY : CMakeFiles/DebugAddTask.dir/codegen

# clean rule for target.
CMakeFiles/DebugAddTask.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask.dir/build.make CMakeFiles/DebugAddTask.dir/clean
.PHONY : CMakeFiles/DebugAddTask.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugPauseAll.dir

# All Build rule for target.
CMakeFiles/DebugPauseAll.dir/all: CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/all
CMakeFiles/DebugPauseAll.dir/all: CMakeFiles/DebugPauseAll_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll.dir/build.make CMakeFiles/DebugPauseAll.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll.dir/build.make CMakeFiles/DebugPauseAll.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=27,28,29,30,31,32,33 "Built target DebugPauseAll"
.PHONY : CMakeFiles/DebugPauseAll.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugPauseAll.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugPauseAll.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugPauseAll.dir/rule

# Convenience name for target.
DebugPauseAll: CMakeFiles/DebugPauseAll.dir/rule
.PHONY : DebugPauseAll

# codegen rule for target.
CMakeFiles/DebugPauseAll.dir/codegen: CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll.dir/build.make CMakeFiles/DebugPauseAll.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=27,28,29,30,31,32,33 "Finished codegen for target DebugPauseAll"
.PHONY : CMakeFiles/DebugPauseAll.dir/codegen

# clean rule for target.
CMakeFiles/DebugPauseAll.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll.dir/build.make CMakeFiles/DebugPauseAll.dir/clean
.PHONY : CMakeFiles/DebugPauseAll.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSimulate.dir

# All Build rule for target.
CMakeFiles/DebugSimulate.dir/all: CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/all
CMakeFiles/DebugSimulate.dir/all: CMakeFiles/DebugSimulate_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate.dir/build.make CMakeFiles/DebugSimulate.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate.dir/build.make CMakeFiles/DebugSimulate.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=34,35,36,37,38,39,40 "Built target DebugSimulate"
.PHONY : CMakeFiles/DebugSimulate.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSimulate.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSimulate.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSimulate.dir/rule

# Convenience name for target.
DebugSimulate: CMakeFiles/DebugSimulate.dir/rule
.PHONY : DebugSimulate

# codegen rule for target.
CMakeFiles/DebugSimulate.dir/codegen: CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate.dir/build.make CMakeFiles/DebugSimulate.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=34,35,36,37,38,39,40 "Finished codegen for target DebugSimulate"
.PHONY : CMakeFiles/DebugSimulate.dir/codegen

# clean rule for target.
CMakeFiles/DebugSimulate.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate.dir/build.make CMakeFiles/DebugSimulate.dir/clean
.PHONY : CMakeFiles/DebugSimulate.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/EmptyTaskDeadlockTest.dir

# All Build rule for target.
CMakeFiles/EmptyTaskDeadlockTest.dir/all: CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/all
CMakeFiles/EmptyTaskDeadlockTest.dir/all: CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest.dir/build.make CMakeFiles/EmptyTaskDeadlockTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest.dir/build.make CMakeFiles/EmptyTaskDeadlockTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=63,64,65,66,67,68,69 "Built target EmptyTaskDeadlockTest"
.PHONY : CMakeFiles/EmptyTaskDeadlockTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/EmptyTaskDeadlockTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/EmptyTaskDeadlockTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/EmptyTaskDeadlockTest.dir/rule

# Convenience name for target.
EmptyTaskDeadlockTest: CMakeFiles/EmptyTaskDeadlockTest.dir/rule
.PHONY : EmptyTaskDeadlockTest

# codegen rule for target.
CMakeFiles/EmptyTaskDeadlockTest.dir/codegen: CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest.dir/build.make CMakeFiles/EmptyTaskDeadlockTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=63,64,65,66,67,68,69 "Finished codegen for target EmptyTaskDeadlockTest"
.PHONY : CMakeFiles/EmptyTaskDeadlockTest.dir/codegen

# clean rule for target.
CMakeFiles/EmptyTaskDeadlockTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest.dir/build.make CMakeFiles/EmptyTaskDeadlockTest.dir/clean
.PHONY : CMakeFiles/EmptyTaskDeadlockTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/SimpleEmptyTaskTest.dir

# All Build rule for target.
CMakeFiles/SimpleEmptyTaskTest.dir/all: CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/all
CMakeFiles/SimpleEmptyTaskTest.dir/all: CMakeFiles/SimpleEmptyTaskTest_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest.dir/build.make CMakeFiles/SimpleEmptyTaskTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest.dir/build.make CMakeFiles/SimpleEmptyTaskTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=93,94,95,96,97,98,99 "Built target SimpleEmptyTaskTest"
.PHONY : CMakeFiles/SimpleEmptyTaskTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SimpleEmptyTaskTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/SimpleEmptyTaskTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/SimpleEmptyTaskTest.dir/rule

# Convenience name for target.
SimpleEmptyTaskTest: CMakeFiles/SimpleEmptyTaskTest.dir/rule
.PHONY : SimpleEmptyTaskTest

# codegen rule for target.
CMakeFiles/SimpleEmptyTaskTest.dir/codegen: CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest.dir/build.make CMakeFiles/SimpleEmptyTaskTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=93,94,95,96,97,98,99 "Finished codegen for target SimpleEmptyTaskTest"
.PHONY : CMakeFiles/SimpleEmptyTaskTest.dir/codegen

# clean rule for target.
CMakeFiles/SimpleEmptyTaskTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest.dir/build.make CMakeFiles/SimpleEmptyTaskTest.dir/clean
.PHONY : CMakeFiles/SimpleEmptyTaskTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSimulateDirect.dir

# All Build rule for target.
CMakeFiles/DebugSimulateDirect.dir/all: CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/all
CMakeFiles/DebugSimulateDirect.dir/all: CMakeFiles/DebugSimulateDirect_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect.dir/build.make CMakeFiles/DebugSimulateDirect.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect.dir/build.make CMakeFiles/DebugSimulateDirect.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=41,42,43,44,45,46 "Built target DebugSimulateDirect"
.PHONY : CMakeFiles/DebugSimulateDirect.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSimulateDirect.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSimulateDirect.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSimulateDirect.dir/rule

# Convenience name for target.
DebugSimulateDirect: CMakeFiles/DebugSimulateDirect.dir/rule
.PHONY : DebugSimulateDirect

# codegen rule for target.
CMakeFiles/DebugSimulateDirect.dir/codegen: CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect.dir/build.make CMakeFiles/DebugSimulateDirect.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=41,42,43,44,45,46 "Finished codegen for target DebugSimulateDirect"
.PHONY : CMakeFiles/DebugSimulateDirect.dir/codegen

# clean rule for target.
CMakeFiles/DebugSimulateDirect.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect.dir/build.make CMakeFiles/DebugSimulateDirect.dir/clean
.PHONY : CMakeFiles/DebugSimulateDirect.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugStepByStep.dir

# All Build rule for target.
CMakeFiles/DebugStepByStep.dir/all: CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/all
CMakeFiles/DebugStepByStep.dir/all: CMakeFiles/DebugStepByStep_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep.dir/build.make CMakeFiles/DebugStepByStep.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep.dir/build.make CMakeFiles/DebugStepByStep.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=56,57,58,59,60,61,62 "Built target DebugStepByStep"
.PHONY : CMakeFiles/DebugStepByStep.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugStepByStep.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugStepByStep.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugStepByStep.dir/rule

# Convenience name for target.
DebugStepByStep: CMakeFiles/DebugStepByStep.dir/rule
.PHONY : DebugStepByStep

# codegen rule for target.
CMakeFiles/DebugStepByStep.dir/codegen: CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep.dir/build.make CMakeFiles/DebugStepByStep.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=56,57,58,59,60,61,62 "Finished codegen for target DebugStepByStep"
.PHONY : CMakeFiles/DebugStepByStep.dir/codegen

# clean rule for target.
CMakeFiles/DebugStepByStep.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep.dir/build.make CMakeFiles/DebugStepByStep.dir/clean
.PHONY : CMakeFiles/DebugStepByStep.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSingleItem.dir

# All Build rule for target.
CMakeFiles/DebugSingleItem.dir/all: CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/all
CMakeFiles/DebugSingleItem.dir/all: CMakeFiles/DebugSingleItem_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem.dir/build.make CMakeFiles/DebugSingleItem.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem.dir/build.make CMakeFiles/DebugSingleItem.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=49,50,51,52,53,54 "Built target DebugSingleItem"
.PHONY : CMakeFiles/DebugSingleItem.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSingleItem.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSingleItem.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSingleItem.dir/rule

# Convenience name for target.
DebugSingleItem: CMakeFiles/DebugSingleItem.dir/rule
.PHONY : DebugSingleItem

# codegen rule for target.
CMakeFiles/DebugSingleItem.dir/codegen: CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem.dir/build.make CMakeFiles/DebugSingleItem.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=49,50,51,52,53,54 "Finished codegen for target DebugSingleItem"
.PHONY : CMakeFiles/DebugSingleItem.dir/codegen

# clean rule for target.
CMakeFiles/DebugSingleItem.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem.dir/build.make CMakeFiles/DebugSingleItem.dir/clean
.PHONY : CMakeFiles/DebugSingleItem.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/FinalSimulateTest.dir

# All Build rule for target.
CMakeFiles/FinalSimulateTest.dir/all: CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/all
CMakeFiles/FinalSimulateTest.dir/all: CMakeFiles/FinalSimulateTest_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest.dir/build.make CMakeFiles/FinalSimulateTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest.dir/build.make CMakeFiles/FinalSimulateTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=71,72,73,74,75,76 "Built target FinalSimulateTest"
.PHONY : CMakeFiles/FinalSimulateTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/FinalSimulateTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/FinalSimulateTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/FinalSimulateTest.dir/rule

# Convenience name for target.
FinalSimulateTest: CMakeFiles/FinalSimulateTest.dir/rule
.PHONY : FinalSimulateTest

# codegen rule for target.
CMakeFiles/FinalSimulateTest.dir/codegen: CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest.dir/build.make CMakeFiles/FinalSimulateTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=71,72,73,74,75,76 "Finished codegen for target FinalSimulateTest"
.PHONY : CMakeFiles/FinalSimulateTest.dir/codegen

# clean rule for target.
CMakeFiles/FinalSimulateTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest.dir/build.make CMakeFiles/FinalSimulateTest.dir/clean
.PHONY : CMakeFiles/FinalSimulateTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CLIDebug.dir

# All Build rule for target.
CMakeFiles/CLIDebug.dir/all: CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/all
CMakeFiles/CLIDebug.dir/all: CMakeFiles/CLIDebug_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug.dir/build.make CMakeFiles/CLIDebug.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug.dir/build.make CMakeFiles/CLIDebug.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=13,14,15,16,17,18 "Built target CLIDebug"
.PHONY : CMakeFiles/CLIDebug.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CLIDebug.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/CLIDebug.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/CLIDebug.dir/rule

# Convenience name for target.
CLIDebug: CMakeFiles/CLIDebug.dir/rule
.PHONY : CLIDebug

# codegen rule for target.
CMakeFiles/CLIDebug.dir/codegen: CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug.dir/build.make CMakeFiles/CLIDebug.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=13,14,15,16,17,18 "Finished codegen for target CLIDebug"
.PHONY : CMakeFiles/CLIDebug.dir/codegen

# clean rule for target.
CMakeFiles/CLIDebug.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug.dir/build.make CMakeFiles/CLIDebug.dir/clean
.PHONY : CMakeFiles/CLIDebug.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/BaseWidget_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/build.make CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/build.make CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target BaseWidget_autogen_timestamp_deps"
.PHONY : CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/rule

# Convenience name for target.
BaseWidget_autogen_timestamp_deps: CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/rule
.PHONY : BaseWidget_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/build.make CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target BaseWidget_autogen_timestamp_deps"
.PHONY : CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/build.make CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/BaseWidget_autogen.dir

# All Build rule for target.
CMakeFiles/BaseWidget_autogen.dir/all: CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen.dir/build.make CMakeFiles/BaseWidget_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen.dir/build.make CMakeFiles/BaseWidget_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=12 "Built target BaseWidget_autogen"
.PHONY : CMakeFiles/BaseWidget_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/BaseWidget_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/BaseWidget_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/BaseWidget_autogen.dir/rule

# Convenience name for target.
BaseWidget_autogen: CMakeFiles/BaseWidget_autogen.dir/rule
.PHONY : BaseWidget_autogen

# codegen rule for target.
CMakeFiles/BaseWidget_autogen.dir/codegen: CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen.dir/build.make CMakeFiles/BaseWidget_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=12 "Finished codegen for target BaseWidget_autogen"
.PHONY : CMakeFiles/BaseWidget_autogen.dir/codegen

# clean rule for target.
CMakeFiles/BaseWidget_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen.dir/build.make CMakeFiles/BaseWidget_autogen.dir/clean
.PHONY : CMakeFiles/BaseWidget_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ProgressTests_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/build.make CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/build.make CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target ProgressTests_autogen_timestamp_deps"
.PHONY : CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/rule

# Convenience name for target.
ProgressTests_autogen_timestamp_deps: CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/rule
.PHONY : ProgressTests_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/build.make CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target ProgressTests_autogen_timestamp_deps"
.PHONY : CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/build.make CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ProgressTests_autogen.dir

# All Build rule for target.
CMakeFiles/ProgressTests_autogen.dir/all: CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests_autogen.dir/build.make CMakeFiles/ProgressTests_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests_autogen.dir/build.make CMakeFiles/ProgressTests_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=92 "Built target ProgressTests_autogen"
.PHONY : CMakeFiles/ProgressTests_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ProgressTests_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ProgressTests_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/ProgressTests_autogen.dir/rule

# Convenience name for target.
ProgressTests_autogen: CMakeFiles/ProgressTests_autogen.dir/rule
.PHONY : ProgressTests_autogen

# codegen rule for target.
CMakeFiles/ProgressTests_autogen.dir/codegen: CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests_autogen.dir/build.make CMakeFiles/ProgressTests_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=92 "Finished codegen for target ProgressTests_autogen"
.PHONY : CMakeFiles/ProgressTests_autogen.dir/codegen

# clean rule for target.
CMakeFiles/ProgressTests_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ProgressTests_autogen.dir/build.make CMakeFiles/ProgressTests_autogen.dir/clean
.PHONY : CMakeFiles/ProgressTests_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ManualTest_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/ManualTest_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest_autogen_timestamp_deps.dir/build.make CMakeFiles/ManualTest_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest_autogen_timestamp_deps.dir/build.make CMakeFiles/ManualTest_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target ManualTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/ManualTest_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ManualTest_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ManualTest_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/ManualTest_autogen_timestamp_deps.dir/rule

# Convenience name for target.
ManualTest_autogen_timestamp_deps: CMakeFiles/ManualTest_autogen_timestamp_deps.dir/rule
.PHONY : ManualTest_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/ManualTest_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest_autogen_timestamp_deps.dir/build.make CMakeFiles/ManualTest_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target ManualTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/ManualTest_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/ManualTest_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest_autogen_timestamp_deps.dir/build.make CMakeFiles/ManualTest_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/ManualTest_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ManualTest_autogen.dir

# All Build rule for target.
CMakeFiles/ManualTest_autogen.dir/all: CMakeFiles/ManualTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest_autogen.dir/build.make CMakeFiles/ManualTest_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest_autogen.dir/build.make CMakeFiles/ManualTest_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=84 "Built target ManualTest_autogen"
.PHONY : CMakeFiles/ManualTest_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ManualTest_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ManualTest_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/ManualTest_autogen.dir/rule

# Convenience name for target.
ManualTest_autogen: CMakeFiles/ManualTest_autogen.dir/rule
.PHONY : ManualTest_autogen

# codegen rule for target.
CMakeFiles/ManualTest_autogen.dir/codegen: CMakeFiles/ManualTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest_autogen.dir/build.make CMakeFiles/ManualTest_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=84 "Finished codegen for target ManualTest_autogen"
.PHONY : CMakeFiles/ManualTest_autogen.dir/codegen

# clean rule for target.
CMakeFiles/ManualTest_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ManualTest_autogen.dir/build.make CMakeFiles/ManualTest_autogen.dir/clean
.PHONY : CMakeFiles/ManualTest_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target DebugAddTask_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/rule

# Convenience name for target.
DebugAddTask_autogen_timestamp_deps: CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/rule
.PHONY : DebugAddTask_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target DebugAddTask_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugAddTask_autogen.dir

# All Build rule for target.
CMakeFiles/DebugAddTask_autogen.dir/all: CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask_autogen.dir/build.make CMakeFiles/DebugAddTask_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask_autogen.dir/build.make CMakeFiles/DebugAddTask_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=26 "Built target DebugAddTask_autogen"
.PHONY : CMakeFiles/DebugAddTask_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugAddTask_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugAddTask_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugAddTask_autogen.dir/rule

# Convenience name for target.
DebugAddTask_autogen: CMakeFiles/DebugAddTask_autogen.dir/rule
.PHONY : DebugAddTask_autogen

# codegen rule for target.
CMakeFiles/DebugAddTask_autogen.dir/codegen: CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask_autogen.dir/build.make CMakeFiles/DebugAddTask_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=26 "Finished codegen for target DebugAddTask_autogen"
.PHONY : CMakeFiles/DebugAddTask_autogen.dir/codegen

# clean rule for target.
CMakeFiles/DebugAddTask_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugAddTask_autogen.dir/build.make CMakeFiles/DebugAddTask_autogen.dir/clean
.PHONY : CMakeFiles/DebugAddTask_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target DebugPauseAll_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/rule

# Convenience name for target.
DebugPauseAll_autogen_timestamp_deps: CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/rule
.PHONY : DebugPauseAll_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target DebugPauseAll_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugPauseAll_autogen.dir

# All Build rule for target.
CMakeFiles/DebugPauseAll_autogen.dir/all: CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll_autogen.dir/build.make CMakeFiles/DebugPauseAll_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll_autogen.dir/build.make CMakeFiles/DebugPauseAll_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target DebugPauseAll_autogen"
.PHONY : CMakeFiles/DebugPauseAll_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugPauseAll_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugPauseAll_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugPauseAll_autogen.dir/rule

# Convenience name for target.
DebugPauseAll_autogen: CMakeFiles/DebugPauseAll_autogen.dir/rule
.PHONY : DebugPauseAll_autogen

# codegen rule for target.
CMakeFiles/DebugPauseAll_autogen.dir/codegen: CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll_autogen.dir/build.make CMakeFiles/DebugPauseAll_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target DebugPauseAll_autogen"
.PHONY : CMakeFiles/DebugPauseAll_autogen.dir/codegen

# clean rule for target.
CMakeFiles/DebugPauseAll_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugPauseAll_autogen.dir/build.make CMakeFiles/DebugPauseAll_autogen.dir/clean
.PHONY : CMakeFiles/DebugPauseAll_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target DebugSimulate_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/rule

# Convenience name for target.
DebugSimulate_autogen_timestamp_deps: CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/rule
.PHONY : DebugSimulate_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target DebugSimulate_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSimulate_autogen.dir

# All Build rule for target.
CMakeFiles/DebugSimulate_autogen.dir/all: CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate_autogen.dir/build.make CMakeFiles/DebugSimulate_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate_autogen.dir/build.make CMakeFiles/DebugSimulate_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=48 "Built target DebugSimulate_autogen"
.PHONY : CMakeFiles/DebugSimulate_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSimulate_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSimulate_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSimulate_autogen.dir/rule

# Convenience name for target.
DebugSimulate_autogen: CMakeFiles/DebugSimulate_autogen.dir/rule
.PHONY : DebugSimulate_autogen

# codegen rule for target.
CMakeFiles/DebugSimulate_autogen.dir/codegen: CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate_autogen.dir/build.make CMakeFiles/DebugSimulate_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=48 "Finished codegen for target DebugSimulate_autogen"
.PHONY : CMakeFiles/DebugSimulate_autogen.dir/codegen

# clean rule for target.
CMakeFiles/DebugSimulate_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulate_autogen.dir/build.make CMakeFiles/DebugSimulate_autogen.dir/clean
.PHONY : CMakeFiles/DebugSimulate_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/build.make CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/build.make CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target EmptyTaskDeadlockTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/rule

# Convenience name for target.
EmptyTaskDeadlockTest_autogen_timestamp_deps: CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/rule
.PHONY : EmptyTaskDeadlockTest_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/build.make CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target EmptyTaskDeadlockTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/build.make CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/EmptyTaskDeadlockTest_autogen.dir

# All Build rule for target.
CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/all: CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/build.make CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/build.make CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=70 "Built target EmptyTaskDeadlockTest_autogen"
.PHONY : CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/rule

# Convenience name for target.
EmptyTaskDeadlockTest_autogen: CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/rule
.PHONY : EmptyTaskDeadlockTest_autogen

# codegen rule for target.
CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/codegen: CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/build.make CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=70 "Finished codegen for target EmptyTaskDeadlockTest_autogen"
.PHONY : CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/codegen

# clean rule for target.
CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/build.make CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/clean
.PHONY : CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/build.make CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/build.make CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target SimpleEmptyTaskTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/rule

# Convenience name for target.
SimpleEmptyTaskTest_autogen_timestamp_deps: CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/rule
.PHONY : SimpleEmptyTaskTest_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/build.make CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target SimpleEmptyTaskTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/build.make CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/SimpleEmptyTaskTest_autogen.dir

# All Build rule for target.
CMakeFiles/SimpleEmptyTaskTest_autogen.dir/all: CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest_autogen.dir/build.make CMakeFiles/SimpleEmptyTaskTest_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest_autogen.dir/build.make CMakeFiles/SimpleEmptyTaskTest_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=100 "Built target SimpleEmptyTaskTest_autogen"
.PHONY : CMakeFiles/SimpleEmptyTaskTest_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SimpleEmptyTaskTest_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/SimpleEmptyTaskTest_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/SimpleEmptyTaskTest_autogen.dir/rule

# Convenience name for target.
SimpleEmptyTaskTest_autogen: CMakeFiles/SimpleEmptyTaskTest_autogen.dir/rule
.PHONY : SimpleEmptyTaskTest_autogen

# codegen rule for target.
CMakeFiles/SimpleEmptyTaskTest_autogen.dir/codegen: CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest_autogen.dir/build.make CMakeFiles/SimpleEmptyTaskTest_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=100 "Finished codegen for target SimpleEmptyTaskTest_autogen"
.PHONY : CMakeFiles/SimpleEmptyTaskTest_autogen.dir/codegen

# clean rule for target.
CMakeFiles/SimpleEmptyTaskTest_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleEmptyTaskTest_autogen.dir/build.make CMakeFiles/SimpleEmptyTaskTest_autogen.dir/clean
.PHONY : CMakeFiles/SimpleEmptyTaskTest_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target DebugSimulateDirect_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/rule

# Convenience name for target.
DebugSimulateDirect_autogen_timestamp_deps: CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/rule
.PHONY : DebugSimulateDirect_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target DebugSimulateDirect_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSimulateDirect_autogen.dir

# All Build rule for target.
CMakeFiles/DebugSimulateDirect_autogen.dir/all: CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect_autogen.dir/build.make CMakeFiles/DebugSimulateDirect_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect_autogen.dir/build.make CMakeFiles/DebugSimulateDirect_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=47 "Built target DebugSimulateDirect_autogen"
.PHONY : CMakeFiles/DebugSimulateDirect_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSimulateDirect_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSimulateDirect_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSimulateDirect_autogen.dir/rule

# Convenience name for target.
DebugSimulateDirect_autogen: CMakeFiles/DebugSimulateDirect_autogen.dir/rule
.PHONY : DebugSimulateDirect_autogen

# codegen rule for target.
CMakeFiles/DebugSimulateDirect_autogen.dir/codegen: CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect_autogen.dir/build.make CMakeFiles/DebugSimulateDirect_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=47 "Finished codegen for target DebugSimulateDirect_autogen"
.PHONY : CMakeFiles/DebugSimulateDirect_autogen.dir/codegen

# clean rule for target.
CMakeFiles/DebugSimulateDirect_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSimulateDirect_autogen.dir/build.make CMakeFiles/DebugSimulateDirect_autogen.dir/clean
.PHONY : CMakeFiles/DebugSimulateDirect_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target DebugStepByStep_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/rule

# Convenience name for target.
DebugStepByStep_autogen_timestamp_deps: CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/rule
.PHONY : DebugStepByStep_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target DebugStepByStep_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugStepByStep_autogen.dir

# All Build rule for target.
CMakeFiles/DebugStepByStep_autogen.dir/all: CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep_autogen.dir/build.make CMakeFiles/DebugStepByStep_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep_autogen.dir/build.make CMakeFiles/DebugStepByStep_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target DebugStepByStep_autogen"
.PHONY : CMakeFiles/DebugStepByStep_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugStepByStep_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugStepByStep_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugStepByStep_autogen.dir/rule

# Convenience name for target.
DebugStepByStep_autogen: CMakeFiles/DebugStepByStep_autogen.dir/rule
.PHONY : DebugStepByStep_autogen

# codegen rule for target.
CMakeFiles/DebugStepByStep_autogen.dir/codegen: CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep_autogen.dir/build.make CMakeFiles/DebugStepByStep_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target DebugStepByStep_autogen"
.PHONY : CMakeFiles/DebugStepByStep_autogen.dir/codegen

# clean rule for target.
CMakeFiles/DebugStepByStep_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugStepByStep_autogen.dir/build.make CMakeFiles/DebugStepByStep_autogen.dir/clean
.PHONY : CMakeFiles/DebugStepByStep_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target DebugSingleItem_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/rule

# Convenience name for target.
DebugSingleItem_autogen_timestamp_deps: CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/rule
.PHONY : DebugSingleItem_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target DebugSingleItem_autogen_timestamp_deps"
.PHONY : CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/build.make CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DebugSingleItem_autogen.dir

# All Build rule for target.
CMakeFiles/DebugSingleItem_autogen.dir/all: CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem_autogen.dir/build.make CMakeFiles/DebugSingleItem_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem_autogen.dir/build.make CMakeFiles/DebugSingleItem_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=55 "Built target DebugSingleItem_autogen"
.PHONY : CMakeFiles/DebugSingleItem_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DebugSingleItem_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DebugSingleItem_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/DebugSingleItem_autogen.dir/rule

# Convenience name for target.
DebugSingleItem_autogen: CMakeFiles/DebugSingleItem_autogen.dir/rule
.PHONY : DebugSingleItem_autogen

# codegen rule for target.
CMakeFiles/DebugSingleItem_autogen.dir/codegen: CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem_autogen.dir/build.make CMakeFiles/DebugSingleItem_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=55 "Finished codegen for target DebugSingleItem_autogen"
.PHONY : CMakeFiles/DebugSingleItem_autogen.dir/codegen

# clean rule for target.
CMakeFiles/DebugSingleItem_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DebugSingleItem_autogen.dir/build.make CMakeFiles/DebugSingleItem_autogen.dir/clean
.PHONY : CMakeFiles/DebugSingleItem_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/build.make CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/build.make CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target FinalSimulateTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/rule

# Convenience name for target.
FinalSimulateTest_autogen_timestamp_deps: CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/rule
.PHONY : FinalSimulateTest_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/build.make CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target FinalSimulateTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/build.make CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/FinalSimulateTest_autogen.dir

# All Build rule for target.
CMakeFiles/FinalSimulateTest_autogen.dir/all: CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest_autogen.dir/build.make CMakeFiles/FinalSimulateTest_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest_autogen.dir/build.make CMakeFiles/FinalSimulateTest_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=77 "Built target FinalSimulateTest_autogen"
.PHONY : CMakeFiles/FinalSimulateTest_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/FinalSimulateTest_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/FinalSimulateTest_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/FinalSimulateTest_autogen.dir/rule

# Convenience name for target.
FinalSimulateTest_autogen: CMakeFiles/FinalSimulateTest_autogen.dir/rule
.PHONY : FinalSimulateTest_autogen

# codegen rule for target.
CMakeFiles/FinalSimulateTest_autogen.dir/codegen: CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest_autogen.dir/build.make CMakeFiles/FinalSimulateTest_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=77 "Finished codegen for target FinalSimulateTest_autogen"
.PHONY : CMakeFiles/FinalSimulateTest_autogen.dir/codegen

# clean rule for target.
CMakeFiles/FinalSimulateTest_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FinalSimulateTest_autogen.dir/build.make CMakeFiles/FinalSimulateTest_autogen.dir/clean
.PHONY : CMakeFiles/FinalSimulateTest_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CLIDebug_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/build.make CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/build.make CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Built target CLIDebug_autogen_timestamp_deps"
.PHONY : CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/rule

# Convenience name for target.
CLIDebug_autogen_timestamp_deps: CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/rule
.PHONY : CLIDebug_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/build.make CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num= "Finished codegen for target CLIDebug_autogen_timestamp_deps"
.PHONY : CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/build.make CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CLIDebug_autogen.dir

# All Build rule for target.
CMakeFiles/CLIDebug_autogen.dir/all: CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug_autogen.dir/build.make CMakeFiles/CLIDebug_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug_autogen.dir/build.make CMakeFiles/CLIDebug_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=19 "Built target CLIDebug_autogen"
.PHONY : CMakeFiles/CLIDebug_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CLIDebug_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/CLIDebug_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : CMakeFiles/CLIDebug_autogen.dir/rule

# Convenience name for target.
CLIDebug_autogen: CMakeFiles/CLIDebug_autogen.dir/rule
.PHONY : CLIDebug_autogen

# codegen rule for target.
CMakeFiles/CLIDebug_autogen.dir/codegen: CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug_autogen.dir/build.make CMakeFiles/CLIDebug_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=19 "Finished codegen for target CLIDebug_autogen"
.PHONY : CMakeFiles/CLIDebug_autogen.dir/codegen

# clean rule for target.
CMakeFiles/CLIDebug_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CLIDebug_autogen.dir/build.make CMakeFiles/CLIDebug_autogen.dir/clean
.PHONY : CMakeFiles/CLIDebug_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

