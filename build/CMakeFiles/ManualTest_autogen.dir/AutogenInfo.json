{"BUILD_DIR": "/Users/<USER>/CLionProjects/BaseWidget/build/ManualTest_autogen", "CMAKE_BINARY_DIR": "/Users/<USER>/CLionProjects/BaseWidget/build", "CMAKE_CURRENT_BINARY_DIR": "/Users/<USER>/CLionProjects/BaseWidget/build", "CMAKE_CURRENT_SOURCE_DIR": "/Users/<USER>/CLionProjects/BaseWidget", "CMAKE_EXECUTABLE": "/opt/homebrew/bin/cmake", "CMAKE_LIST_FILES": ["/Users/<USER>/CLionProjects/BaseWidget/CMakeLists.txt", "/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/4.1.1/CMakeSystem.cmake", "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake", "/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/4.1.1/CMakeCCompiler.cmake", "/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/4.1.1/CMakeCXXCompiler.cmake", "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake", "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake", "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake", "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake", "/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake", "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake", "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-C.cmake", "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake", "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake", "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake", "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake", "/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake", "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake", "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake", "/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake", "/opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake", "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake", "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-CXX.cmake", "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake", "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake", "/opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake", "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake", "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-CXX.cmake", "/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake", "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5/Qt5Config.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QCocoaIntegrationPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QMacHeifPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QMacJp2Plugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QPdfPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5Widgets_QMacStylePlugin.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Test/Qt5TestConfigVersion.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Test/Qt5TestConfig.cmake", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Test/Qt5TestConfigExtras.cmake"], "CMAKE_SOURCE_DIR": "/Users/<USER>/CLionProjects/BaseWidget", "CROSS_CONFIG": false, "DEP_FILE": "/Users/<USER>/CLionProjects/BaseWidget/build/ManualTest_autogen/deps", "DEP_FILE_RULE_NAME": "ManualTest_autogen/timestamp", "HEADERS": [["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.h", "MU", "QM7AEFKFBK/moc_ProgressItem.cpp", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.h", "MU", "QM7AEFKFBK/moc_ProgressItemWidget.cpp", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.h", "MU", "QM7AEFKFBK/moc_ProgressListWidget.cpp", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.h", "MU", "QM7AEFKFBK/moc_ProgressManager.cpp", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.h", "MU", "QM7AEFKFBK/moc_ProgressWidget.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/Users/<USER>/CLionProjects/BaseWidget/build/ManualTest_autogen/include", "MOC_COMPILATION_FILE": "/Users/<USER>/CLionProjects/BaseWidget/build/ManualTest_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/notification", "/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework/Headers", "/opt/homebrew/Cellar/qt@5/5.15.17/mkspecs/macx-clang", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework/Headers", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework", "/opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework/Headers", "/usr/local/include", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-w", "-dM", "-E", "/opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/Users/<USER>/CLionProjects/BaseWidget/build/ManualTest_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": ["/Users/<USER>/CLionProjects/BaseWidget/build/BaseWidget_autogen/mocs_compilation.cpp", "/Users/<USER>/CLionProjects/BaseWidget/build/ProgressTests_autogen/mocs_compilation.cpp"], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/ManualTest_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/opt/homebrew/Cellar/qt@5/5.15.17/bin/moc", "QT_UIC_EXECUTABLE": "/opt/homebrew/Cellar/qt@5/5.15.17/bin/uic", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 15, "SETTINGS_FILE": "/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/ManualTest_autogen.dir/AutogenUsed.txt", "SOURCES": [["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItem.cpp", "MU", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressItemWidget.cpp", "MU", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressListWidget.cpp", "MU", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressManager.cpp", "MU", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/ProgressWidget.cpp", "MU", null], ["/Users/<USER>/CLionProjects/BaseWidget/src/componets/progress/tests/manual_test.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": ["/Users/<USER>/CLionProjects/BaseWidget/build/BaseWidget_autogen/mocs_compilation.cpp", "/Users/<USER>/CLionProjects/BaseWidget/build/ProgressTests_autogen/mocs_compilation.cpp"], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}