# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/build

# Utility rule file for ManualTest_autogen.

# Include any custom commands dependencies for this target.
include CMakeFiles/ManualTest_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ManualTest_autogen.dir/progress.make

CMakeFiles/ManualTest_autogen: ManualTest_autogen/timestamp

ManualTest_autogen/timestamp: /opt/homebrew/Cellar/qt@5/5.15.17/bin/moc
ManualTest_autogen/timestamp: /opt/homebrew/Cellar/qt@5/5.15.17/bin/uic
ManualTest_autogen/timestamp: CMakeFiles/ManualTest_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target ManualTest"
	/opt/homebrew/bin/cmake -E cmake_autogen /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/ManualTest_autogen.dir/AutogenInfo.json ""
	/opt/homebrew/bin/cmake -E touch /Users/<USER>/CLionProjects/BaseWidget/build/ManualTest_autogen/timestamp

CMakeFiles/ManualTest_autogen.dir/codegen:
.PHONY : CMakeFiles/ManualTest_autogen.dir/codegen

ManualTest_autogen: CMakeFiles/ManualTest_autogen
ManualTest_autogen: ManualTest_autogen/timestamp
ManualTest_autogen: CMakeFiles/ManualTest_autogen.dir/build.make
.PHONY : ManualTest_autogen

# Rule to build all files generated by this target.
CMakeFiles/ManualTest_autogen.dir/build: ManualTest_autogen
.PHONY : CMakeFiles/ManualTest_autogen.dir/build

CMakeFiles/ManualTest_autogen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ManualTest_autogen.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ManualTest_autogen.dir/clean

CMakeFiles/ManualTest_autogen.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget /Users/<USER>/CLionProjects/BaseWidget /Users/<USER>/CLionProjects/BaseWidget/build /Users/<USER>/CLionProjects/BaseWidget/build /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/ManualTest_autogen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/ManualTest_autogen.dir/depend

