/usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/EmptyTaskDeadlockTest.dir/EmptyTaskDeadlockTest_autogen/mocs_compilation.cpp.o CMakeFiles/EmptyTaskDeadlockTest.dir/src/componets/progress/tests/EmptyTaskDeadlockTest.cpp.o CMakeFiles/EmptyTaskDeadlockTest.dir/src/componets/progress/ProgressItem.cpp.o CMakeFiles/EmptyTaskDeadlockTest.dir/src/componets/progress/ProgressItemWidget.cpp.o CMakeFiles/EmptyTaskDeadlockTest.dir/src/componets/progress/ProgressListWidget.cpp.o CMakeFiles/EmptyTaskDeadlockTest.dir/src/componets/progress/ProgressManager.cpp.o CMakeFiles/EmptyTaskDeadlockTest.dir/src/componets/progress/ProgressWidget.cpp.o -o EmptyTaskDeadlockTest -F/opt/homebrew/Cellar/qt@5/5.15.17/lib  /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework/QtWidgets /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtTest.framework/QtTest /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework/QtGui /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework/QtCore
