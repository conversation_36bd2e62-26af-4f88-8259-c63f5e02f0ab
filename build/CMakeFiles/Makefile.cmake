# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/Users/<USER>/CLionProjects/BaseWidget/CMakeLists.txt"
  "CMakeFiles/4.1.1/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.1/CMakeSystem.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5/Qt5Config.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5/Qt5ConfigVersion.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5/Qt5ModuleLocation.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QCocoaIntegrationPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QMacHeifPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QMacJp2Plugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QPdfPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Test/Qt5TestConfig.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Test/Qt5TestConfigExtras.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Test/Qt5TestConfigVersion.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"
  "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5Widgets/Qt5Widgets_QMacStylePlugin.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/BaseWidget_autogen.dir/AutogenInfo.json"
  "CMakeFiles/ProgressTests_autogen.dir/AutogenInfo.json"
  "CMakeFiles/ManualTest_autogen.dir/AutogenInfo.json"
  "CMakeFiles/DebugAddTask_autogen.dir/AutogenInfo.json"
  "CMakeFiles/DebugPauseAll_autogen.dir/AutogenInfo.json"
  "CMakeFiles/DebugSimulate_autogen.dir/AutogenInfo.json"
  "CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/AutogenInfo.json"
  "CMakeFiles/SimpleEmptyTaskTest_autogen.dir/AutogenInfo.json"
  "CMakeFiles/DebugSimulateDirect_autogen.dir/AutogenInfo.json"
  "CMakeFiles/DebugStepByStep_autogen.dir/AutogenInfo.json"
  "CMakeFiles/DebugSingleItem_autogen.dir/AutogenInfo.json"
  "CMakeFiles/FinalSimulateTest_autogen.dir/AutogenInfo.json"
  "CMakeFiles/CLIDebug_autogen.dir/AutogenInfo.json"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/BaseWidget.dir/DependInfo.cmake"
  "CMakeFiles/ProgressTests.dir/DependInfo.cmake"
  "CMakeFiles/ManualTest.dir/DependInfo.cmake"
  "CMakeFiles/DebugAddTask.dir/DependInfo.cmake"
  "CMakeFiles/DebugPauseAll.dir/DependInfo.cmake"
  "CMakeFiles/DebugSimulate.dir/DependInfo.cmake"
  "CMakeFiles/EmptyTaskDeadlockTest.dir/DependInfo.cmake"
  "CMakeFiles/SimpleEmptyTaskTest.dir/DependInfo.cmake"
  "CMakeFiles/DebugSimulateDirect.dir/DependInfo.cmake"
  "CMakeFiles/DebugStepByStep.dir/DependInfo.cmake"
  "CMakeFiles/DebugSingleItem.dir/DependInfo.cmake"
  "CMakeFiles/FinalSimulateTest.dir/DependInfo.cmake"
  "CMakeFiles/CLIDebug.dir/DependInfo.cmake"
  "CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/BaseWidget_autogen.dir/DependInfo.cmake"
  "CMakeFiles/ProgressTests_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/ProgressTests_autogen.dir/DependInfo.cmake"
  "CMakeFiles/ManualTest_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/ManualTest_autogen.dir/DependInfo.cmake"
  "CMakeFiles/DebugAddTask_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/DebugAddTask_autogen.dir/DependInfo.cmake"
  "CMakeFiles/DebugPauseAll_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/DebugPauseAll_autogen.dir/DependInfo.cmake"
  "CMakeFiles/DebugSimulate_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/DebugSimulate_autogen.dir/DependInfo.cmake"
  "CMakeFiles/EmptyTaskDeadlockTest_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/EmptyTaskDeadlockTest_autogen.dir/DependInfo.cmake"
  "CMakeFiles/SimpleEmptyTaskTest_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/SimpleEmptyTaskTest_autogen.dir/DependInfo.cmake"
  "CMakeFiles/DebugSimulateDirect_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/DebugSimulateDirect_autogen.dir/DependInfo.cmake"
  "CMakeFiles/DebugStepByStep_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/DebugStepByStep_autogen.dir/DependInfo.cmake"
  "CMakeFiles/DebugSingleItem_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/DebugSingleItem_autogen.dir/DependInfo.cmake"
  "CMakeFiles/FinalSimulateTest_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/FinalSimulateTest_autogen.dir/DependInfo.cmake"
  "CMakeFiles/CLIDebug_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/CLIDebug_autogen.dir/DependInfo.cmake"
  )
