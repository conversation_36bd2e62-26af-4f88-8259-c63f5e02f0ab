/****************************************************************************
** Meta object code from reading C++ file 'ProgressListWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.17)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/componets/progress/ProgressListWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ProgressListWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.17. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ProgressListWidget_t {
    QByteArrayData data[32];
    char stringdata0[460];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ProgressListWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ProgressListWidget_t qt_meta_stringdata_ProgressListWidget = {
    {
QT_MOC_LITERAL(0, 0, 18), // "ProgressListWidget"
QT_MOC_LITERAL(1, 19, 14), // "startRequested"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 6), // "itemId"
QT_MOC_LITERAL(4, 42, 14), // "pauseRequested"
QT_MOC_LITERAL(5, 57, 15), // "resumeRequested"
QT_MOC_LITERAL(6, 73, 15), // "cancelRequested"
QT_MOC_LITERAL(7, 89, 15), // "removeRequested"
QT_MOC_LITERAL(8, 105, 17), // "itemDoubleClicked"
QT_MOC_LITERAL(9, 123, 20), // "itemSelectionChanged"
QT_MOC_LITERAL(10, 144, 12), // "countChanged"
QT_MOC_LITERAL(11, 157, 5), // "total"
QT_MOC_LITERAL(12, 163, 7), // "running"
QT_MOC_LITERAL(13, 171, 9), // "completed"
QT_MOC_LITERAL(14, 181, 5), // "error"
QT_MOC_LITERAL(15, 187, 16), // "onClearCompleted"
QT_MOC_LITERAL(16, 204, 10), // "onClearAll"
QT_MOC_LITERAL(17, 215, 11), // "onExpandAll"
QT_MOC_LITERAL(18, 227, 13), // "onCollapseAll"
QT_MOC_LITERAL(19, 241, 15), // "onFilterChanged"
QT_MOC_LITERAL(20, 257, 5), // "index"
QT_MOC_LITERAL(21, 263, 19), // "onSearchTextChanged"
QT_MOC_LITERAL(22, 283, 4), // "text"
QT_MOC_LITERAL(23, 288, 20), // "onItemStartRequested"
QT_MOC_LITERAL(24, 309, 20), // "onItemPauseRequested"
QT_MOC_LITERAL(25, 330, 21), // "onItemResumeRequested"
QT_MOC_LITERAL(26, 352, 21), // "onItemCancelRequested"
QT_MOC_LITERAL(27, 374, 21), // "onItemRemoveRequested"
QT_MOC_LITERAL(28, 396, 19), // "onItemExpandToggled"
QT_MOC_LITERAL(29, 416, 8), // "expanded"
QT_MOC_LITERAL(30, 425, 19), // "onItemDoubleClicked"
QT_MOC_LITERAL(31, 445, 14) // "onRefreshTimer"

    },
    "ProgressListWidget\0startRequested\0\0"
    "itemId\0pauseRequested\0resumeRequested\0"
    "cancelRequested\0removeRequested\0"
    "itemDoubleClicked\0itemSelectionChanged\0"
    "countChanged\0total\0running\0completed\0"
    "error\0onClearCompleted\0onClearAll\0"
    "onExpandAll\0onCollapseAll\0onFilterChanged\0"
    "index\0onSearchTextChanged\0text\0"
    "onItemStartRequested\0onItemPauseRequested\0"
    "onItemResumeRequested\0onItemCancelRequested\0"
    "onItemRemoveRequested\0onItemExpandToggled\0"
    "expanded\0onItemDoubleClicked\0"
    "onRefreshTimer"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ProgressListWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      22,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  124,    2, 0x06 /* Public */,
       4,    1,  127,    2, 0x06 /* Public */,
       5,    1,  130,    2, 0x06 /* Public */,
       6,    1,  133,    2, 0x06 /* Public */,
       7,    1,  136,    2, 0x06 /* Public */,
       8,    1,  139,    2, 0x06 /* Public */,
       9,    1,  142,    2, 0x06 /* Public */,
      10,    4,  145,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      15,    0,  154,    2, 0x0a /* Public */,
      16,    0,  155,    2, 0x0a /* Public */,
      17,    0,  156,    2, 0x0a /* Public */,
      18,    0,  157,    2, 0x0a /* Public */,
      19,    1,  158,    2, 0x0a /* Public */,
      21,    1,  161,    2, 0x0a /* Public */,
      23,    1,  164,    2, 0x08 /* Private */,
      24,    1,  167,    2, 0x08 /* Private */,
      25,    1,  170,    2, 0x08 /* Private */,
      26,    1,  173,    2, 0x08 /* Private */,
      27,    1,  176,    2, 0x08 /* Private */,
      28,    2,  179,    2, 0x08 /* Private */,
      30,    1,  184,    2, 0x08 /* Private */,
      31,    0,  187,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int,   11,   12,   13,   14,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   20,
    QMetaType::Void, QMetaType::QString,   22,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    3,   29,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,

       0        // eod
};

void ProgressListWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProgressListWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->startRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->pauseRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->resumeRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->cancelRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->removeRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->itemDoubleClicked((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 6: _t->itemSelectionChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->countChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4]))); break;
        case 8: _t->onClearCompleted(); break;
        case 9: _t->onClearAll(); break;
        case 10: _t->onExpandAll(); break;
        case 11: _t->onCollapseAll(); break;
        case 12: _t->onFilterChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 13: _t->onSearchTextChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 14: _t->onItemStartRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 15: _t->onItemPauseRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 16: _t->onItemResumeRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 17: _t->onItemCancelRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 18: _t->onItemRemoveRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 19: _t->onItemExpandToggled((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 20: _t->onItemDoubleClicked((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 21: _t->onRefreshTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ProgressListWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressListWidget::startRequested)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ProgressListWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressListWidget::pauseRequested)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ProgressListWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressListWidget::resumeRequested)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ProgressListWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressListWidget::cancelRequested)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ProgressListWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressListWidget::removeRequested)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ProgressListWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressListWidget::itemDoubleClicked)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ProgressListWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressListWidget::itemSelectionChanged)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ProgressListWidget::*)(int , int , int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressListWidget::countChanged)) {
                *result = 7;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ProgressListWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_ProgressListWidget.data,
    qt_meta_data_ProgressListWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ProgressListWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ProgressListWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ProgressListWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ProgressListWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 22)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 22;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 22)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 22;
    }
    return _id;
}

// SIGNAL 0
void ProgressListWidget::startRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ProgressListWidget::pauseRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ProgressListWidget::resumeRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ProgressListWidget::cancelRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ProgressListWidget::removeRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ProgressListWidget::itemDoubleClicked(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void ProgressListWidget::itemSelectionChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void ProgressListWidget::countChanged(int _t1, int _t2, int _t3, int _t4)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
