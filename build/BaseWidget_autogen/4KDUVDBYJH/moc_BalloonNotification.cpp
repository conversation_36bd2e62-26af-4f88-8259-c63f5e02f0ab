/****************************************************************************
** Meta object code from reading C++ file 'BalloonNotification.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.17)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/componets/notification/BalloonNotification.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'BalloonNotification.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.17. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_BalloonNotification_t {
    QByteArrayData data[12];
    char stringdata0[185];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_BalloonNotification_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_BalloonNotification_t qt_meta_stringdata_BalloonNotification = {
    {
QT_MOC_LITERAL(0, 0, 19), // "BalloonNotification"
QT_MOC_LITERAL(1, 20, 19), // "notificationClicked"
QT_MOC_LITERAL(2, 40, 0), // ""
QT_MOC_LITERAL(3, 41, 16), // "NotificationItem"
QT_MOC_LITERAL(4, 58, 4), // "item"
QT_MOC_LITERAL(5, 63, 18), // "notificationClosed"
QT_MOC_LITERAL(6, 82, 20), // "BalloonNotification*"
QT_MOC_LITERAL(7, 103, 7), // "balloon"
QT_MOC_LITERAL(8, 111, 20), // "onCloseButtonClicked"
QT_MOC_LITERAL(9, 132, 17), // "onAutoHideTimeout"
QT_MOC_LITERAL(10, 150, 16), // "onFadeInFinished"
QT_MOC_LITERAL(11, 167, 17) // "onFadeOutFinished"

    },
    "BalloonNotification\0notificationClicked\0"
    "\0NotificationItem\0item\0notificationClosed\0"
    "BalloonNotification*\0balloon\0"
    "onCloseButtonClicked\0onAutoHideTimeout\0"
    "onFadeInFinished\0onFadeOutFinished"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_BalloonNotification[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   44,    2, 0x06 /* Public */,
       5,    1,   47,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    0,   50,    2, 0x08 /* Private */,
       9,    0,   51,    2, 0x08 /* Private */,
      10,    0,   52,    2, 0x08 /* Private */,
      11,    0,   53,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 6,    7,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void BalloonNotification::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<BalloonNotification *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->notificationClicked((*reinterpret_cast< const NotificationItem(*)>(_a[1]))); break;
        case 1: _t->notificationClosed((*reinterpret_cast< BalloonNotification*(*)>(_a[1]))); break;
        case 2: _t->onCloseButtonClicked(); break;
        case 3: _t->onAutoHideTimeout(); break;
        case 4: _t->onFadeInFinished(); break;
        case 5: _t->onFadeOutFinished(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< BalloonNotification* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (BalloonNotification::*)(const NotificationItem & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&BalloonNotification::notificationClicked)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (BalloonNotification::*)(BalloonNotification * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&BalloonNotification::notificationClosed)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject BalloonNotification::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_BalloonNotification.data,
    qt_meta_data_BalloonNotification,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *BalloonNotification::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *BalloonNotification::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_BalloonNotification.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int BalloonNotification::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void BalloonNotification::notificationClicked(const NotificationItem & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void BalloonNotification::notificationClosed(BalloonNotification * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
