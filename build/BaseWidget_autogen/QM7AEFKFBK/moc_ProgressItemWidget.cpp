/****************************************************************************
** Meta object code from reading C++ file 'ProgressItemWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.17)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/componets/progress/ProgressItemWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ProgressItemWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.17. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ProgressItemWidget_t {
    QByteArrayData data[17];
    char stringdata0[252];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ProgressItemWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ProgressItemWidget_t qt_meta_stringdata_ProgressItemWidget = {
    {
QT_MOC_LITERAL(0, 0, 18), // "ProgressItemWidget"
QT_MOC_LITERAL(1, 19, 14), // "startRequested"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 6), // "itemId"
QT_MOC_LITERAL(4, 42, 14), // "pauseRequested"
QT_MOC_LITERAL(5, 57, 15), // "resumeRequested"
QT_MOC_LITERAL(6, 73, 15), // "cancelRequested"
QT_MOC_LITERAL(7, 89, 15), // "removeRequested"
QT_MOC_LITERAL(8, 105, 13), // "expandToggled"
QT_MOC_LITERAL(9, 119, 8), // "expanded"
QT_MOC_LITERAL(10, 128, 17), // "itemDoubleClicked"
QT_MOC_LITERAL(11, 146, 19), // "onStartPauseClicked"
QT_MOC_LITERAL(12, 166, 15), // "onCancelClicked"
QT_MOC_LITERAL(13, 182, 15), // "onMenuRequested"
QT_MOC_LITERAL(14, 198, 15), // "onExpandClicked"
QT_MOC_LITERAL(15, 214, 13), // "onUpdateTimer"
QT_MOC_LITERAL(16, 228, 23) // "onFadeAnimationFinished"

    },
    "ProgressItemWidget\0startRequested\0\0"
    "itemId\0pauseRequested\0resumeRequested\0"
    "cancelRequested\0removeRequested\0"
    "expandToggled\0expanded\0itemDoubleClicked\0"
    "onStartPauseClicked\0onCancelClicked\0"
    "onMenuRequested\0onExpandClicked\0"
    "onUpdateTimer\0onFadeAnimationFinished"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ProgressItemWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   79,    2, 0x06 /* Public */,
       4,    1,   82,    2, 0x06 /* Public */,
       5,    1,   85,    2, 0x06 /* Public */,
       6,    1,   88,    2, 0x06 /* Public */,
       7,    1,   91,    2, 0x06 /* Public */,
       8,    2,   94,    2, 0x06 /* Public */,
      10,    1,   99,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      11,    0,  102,    2, 0x08 /* Private */,
      12,    0,  103,    2, 0x08 /* Private */,
      13,    0,  104,    2, 0x08 /* Private */,
      14,    0,  105,    2, 0x08 /* Private */,
      15,    0,  106,    2, 0x08 /* Private */,
      16,    0,  107,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    3,    9,
    QMetaType::Void, QMetaType::QString,    3,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ProgressItemWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProgressItemWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->startRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->pauseRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->resumeRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->cancelRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->removeRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->expandToggled((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 6: _t->itemDoubleClicked((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->onStartPauseClicked(); break;
        case 8: _t->onCancelClicked(); break;
        case 9: _t->onMenuRequested(); break;
        case 10: _t->onExpandClicked(); break;
        case 11: _t->onUpdateTimer(); break;
        case 12: _t->onFadeAnimationFinished(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ProgressItemWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressItemWidget::startRequested)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ProgressItemWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressItemWidget::pauseRequested)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ProgressItemWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressItemWidget::resumeRequested)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ProgressItemWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressItemWidget::cancelRequested)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ProgressItemWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressItemWidget::removeRequested)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ProgressItemWidget::*)(const QString & , bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressItemWidget::expandToggled)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ProgressItemWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressItemWidget::itemDoubleClicked)) {
                *result = 6;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ProgressItemWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_ProgressItemWidget.data,
    qt_meta_data_ProgressItemWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ProgressItemWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ProgressItemWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ProgressItemWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ProgressItemWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void ProgressItemWidget::startRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ProgressItemWidget::pauseRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ProgressItemWidget::resumeRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ProgressItemWidget::cancelRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ProgressItemWidget::removeRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ProgressItemWidget::expandToggled(const QString & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void ProgressItemWidget::itemDoubleClicked(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
