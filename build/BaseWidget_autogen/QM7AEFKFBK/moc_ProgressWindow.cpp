/****************************************************************************
** Meta object code from reading C++ file 'ProgressWindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.17)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/componets/progress/ProgressWindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ProgressWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.17. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ProgressWindow_t {
    QByteArrayData data[39];
    char stringdata0[509];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ProgressWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ProgressWindow_t qt_meta_stringdata_ProgressWindow = {
    {
QT_MOC_LITERAL(0, 0, 14), // "ProgressWindow"
QT_MOC_LITERAL(1, 15, 17), // "onNewProgressItem"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 20), // "onRemoveSelectedItem"
QT_MOC_LITERAL(4, 55, 16), // "onClearCompleted"
QT_MOC_LITERAL(5, 72, 10), // "onClearAll"
QT_MOC_LITERAL(6, 83, 10), // "onStartAll"
QT_MOC_LITERAL(7, 94, 10), // "onPauseAll"
QT_MOC_LITERAL(8, 105, 11), // "onCancelAll"
QT_MOC_LITERAL(9, 117, 11), // "onExpandAll"
QT_MOC_LITERAL(10, 129, 13), // "onCollapseAll"
QT_MOC_LITERAL(11, 143, 9), // "onRefresh"
QT_MOC_LITERAL(12, 153, 7), // "onAbout"
QT_MOC_LITERAL(13, 161, 13), // "onItemCreated"
QT_MOC_LITERAL(14, 175, 6), // "itemId"
QT_MOC_LITERAL(15, 182, 13), // "onItemRemoved"
QT_MOC_LITERAL(16, 196, 13), // "onItemUpdated"
QT_MOC_LITERAL(17, 210, 17), // "onProgressUpdated"
QT_MOC_LITERAL(18, 228, 7), // "current"
QT_MOC_LITERAL(19, 236, 5), // "total"
QT_MOC_LITERAL(20, 242, 10), // "percentage"
QT_MOC_LITERAL(21, 253, 15), // "onStatusChanged"
QT_MOC_LITERAL(22, 269, 14), // "ProgressStatus"
QT_MOC_LITERAL(23, 284, 9), // "oldStatus"
QT_MOC_LITERAL(24, 294, 9), // "newStatus"
QT_MOC_LITERAL(25, 304, 19), // "onStatisticsChanged"
QT_MOC_LITERAL(26, 324, 7), // "running"
QT_MOC_LITERAL(27, 332, 6), // "paused"
QT_MOC_LITERAL(28, 339, 9), // "completed"
QT_MOC_LITERAL(29, 349, 5), // "error"
QT_MOC_LITERAL(30, 355, 9), // "cancelled"
QT_MOC_LITERAL(31, 365, 16), // "onStartRequested"
QT_MOC_LITERAL(32, 382, 16), // "onPauseRequested"
QT_MOC_LITERAL(33, 399, 17), // "onResumeRequested"
QT_MOC_LITERAL(34, 417, 17), // "onCancelRequested"
QT_MOC_LITERAL(35, 435, 17), // "onRemoveRequested"
QT_MOC_LITERAL(36, 453, 19), // "onItemDoubleClicked"
QT_MOC_LITERAL(37, 473, 17), // "onSimulationTimer"
QT_MOC_LITERAL(38, 491, 17) // "onUpdateStatusBar"

    },
    "ProgressWindow\0onNewProgressItem\0\0"
    "onRemoveSelectedItem\0onClearCompleted\0"
    "onClearAll\0onStartAll\0onPauseAll\0"
    "onCancelAll\0onExpandAll\0onCollapseAll\0"
    "onRefresh\0onAbout\0onItemCreated\0itemId\0"
    "onItemRemoved\0onItemUpdated\0"
    "onProgressUpdated\0current\0total\0"
    "percentage\0onStatusChanged\0ProgressStatus\0"
    "oldStatus\0newStatus\0onStatisticsChanged\0"
    "running\0paused\0completed\0error\0cancelled\0"
    "onStartRequested\0onPauseRequested\0"
    "onResumeRequested\0onCancelRequested\0"
    "onRemoveRequested\0onItemDoubleClicked\0"
    "onSimulationTimer\0onUpdateStatusBar"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ProgressWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      25,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  139,    2, 0x0a /* Public */,
       3,    0,  140,    2, 0x0a /* Public */,
       4,    0,  141,    2, 0x0a /* Public */,
       5,    0,  142,    2, 0x0a /* Public */,
       6,    0,  143,    2, 0x0a /* Public */,
       7,    0,  144,    2, 0x0a /* Public */,
       8,    0,  145,    2, 0x0a /* Public */,
       9,    0,  146,    2, 0x0a /* Public */,
      10,    0,  147,    2, 0x0a /* Public */,
      11,    0,  148,    2, 0x0a /* Public */,
      12,    0,  149,    2, 0x0a /* Public */,
      13,    1,  150,    2, 0x0a /* Public */,
      15,    1,  153,    2, 0x0a /* Public */,
      16,    1,  156,    2, 0x0a /* Public */,
      17,    4,  159,    2, 0x0a /* Public */,
      21,    3,  168,    2, 0x0a /* Public */,
      25,    6,  175,    2, 0x0a /* Public */,
      31,    1,  188,    2, 0x0a /* Public */,
      32,    1,  191,    2, 0x0a /* Public */,
      33,    1,  194,    2, 0x0a /* Public */,
      34,    1,  197,    2, 0x0a /* Public */,
      35,    1,  200,    2, 0x0a /* Public */,
      36,    1,  203,    2, 0x0a /* Public */,
      37,    0,  206,    2, 0x08 /* Private */,
      38,    0,  207,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::Int, QMetaType::Double,   14,   18,   19,   20,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 22, 0x80000000 | 22,   14,   23,   24,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int,   19,   26,   27,   28,   29,   30,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ProgressWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProgressWindow *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onNewProgressItem(); break;
        case 1: _t->onRemoveSelectedItem(); break;
        case 2: _t->onClearCompleted(); break;
        case 3: _t->onClearAll(); break;
        case 4: _t->onStartAll(); break;
        case 5: _t->onPauseAll(); break;
        case 6: _t->onCancelAll(); break;
        case 7: _t->onExpandAll(); break;
        case 8: _t->onCollapseAll(); break;
        case 9: _t->onRefresh(); break;
        case 10: _t->onAbout(); break;
        case 11: _t->onItemCreated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 12: _t->onItemRemoved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 13: _t->onItemUpdated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 14: _t->onProgressUpdated((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4]))); break;
        case 15: _t->onStatusChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< ProgressStatus(*)>(_a[2])),(*reinterpret_cast< ProgressStatus(*)>(_a[3]))); break;
        case 16: _t->onStatisticsChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4])),(*reinterpret_cast< int(*)>(_a[5])),(*reinterpret_cast< int(*)>(_a[6]))); break;
        case 17: _t->onStartRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 18: _t->onPauseRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 19: _t->onResumeRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 20: _t->onCancelRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 21: _t->onRemoveRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 22: _t->onItemDoubleClicked((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 23: _t->onSimulationTimer(); break;
        case 24: _t->onUpdateStatusBar(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ProgressWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_ProgressWindow.data,
    qt_meta_data_ProgressWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ProgressWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ProgressWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ProgressWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int ProgressWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 25)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 25;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 25)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 25;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
