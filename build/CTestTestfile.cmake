# CMake generated Testfile for 
# Source directory: /Users/<USER>/CLionProjects/BaseWidget
# Build directory: /Users/<USER>/CLionProjects/BaseWidget/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[ProgressManagerTests]=] "/Users/<USER>/CLionProjects/BaseWidget/build/ProgressTests")
set_tests_properties([=[ProgressManagerTests]=] PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/CLionProjects/BaseWidget/CMakeLists.txt;107;add_test;/Users/<USER>/CLionProjects/BaseWidget/CMakeLists.txt;0;")
