/****************************************************************************
** Meta object code from reading C++ file 'ProgressManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.17)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/componets/progress/ProgressManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ProgressManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.17. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ProgressManager_t {
    QByteArrayData data[40];
    char stringdata0[487];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ProgressManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ProgressManager_t qt_meta_stringdata_ProgressManager = {
    {
QT_MOC_LITERAL(0, 0, 15), // "ProgressManager"
QT_MOC_LITERAL(1, 16, 11), // "itemCreated"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 6), // "itemId"
QT_MOC_LITERAL(4, 36, 11), // "itemRemoved"
QT_MOC_LITERAL(5, 48, 11), // "itemUpdated"
QT_MOC_LITERAL(6, 60, 15), // "progressUpdated"
QT_MOC_LITERAL(7, 76, 7), // "current"
QT_MOC_LITERAL(8, 84, 5), // "total"
QT_MOC_LITERAL(9, 90, 10), // "percentage"
QT_MOC_LITERAL(10, 101, 14), // "messageUpdated"
QT_MOC_LITERAL(11, 116, 7), // "message"
QT_MOC_LITERAL(12, 124, 13), // "statusChanged"
QT_MOC_LITERAL(13, 138, 14), // "ProgressStatus"
QT_MOC_LITERAL(14, 153, 9), // "oldStatus"
QT_MOC_LITERAL(15, 163, 9), // "newStatus"
QT_MOC_LITERAL(16, 173, 11), // "itemStarted"
QT_MOC_LITERAL(17, 185, 10), // "itemPaused"
QT_MOC_LITERAL(18, 196, 11), // "itemResumed"
QT_MOC_LITERAL(19, 208, 13), // "itemCompleted"
QT_MOC_LITERAL(20, 222, 13), // "itemCancelled"
QT_MOC_LITERAL(21, 236, 9), // "itemError"
QT_MOC_LITERAL(22, 246, 12), // "errorMessage"
QT_MOC_LITERAL(23, 259, 10), // "childAdded"
QT_MOC_LITERAL(24, 270, 8), // "parentId"
QT_MOC_LITERAL(25, 279, 7), // "childId"
QT_MOC_LITERAL(26, 287, 12), // "childRemoved"
QT_MOC_LITERAL(27, 300, 17), // "statisticsChanged"
QT_MOC_LITERAL(28, 318, 7), // "running"
QT_MOC_LITERAL(29, 326, 6), // "paused"
QT_MOC_LITERAL(30, 333, 9), // "completed"
QT_MOC_LITERAL(31, 343, 5), // "error"
QT_MOC_LITERAL(32, 349, 9), // "cancelled"
QT_MOC_LITERAL(33, 359, 16), // "onStartRequested"
QT_MOC_LITERAL(34, 376, 16), // "onPauseRequested"
QT_MOC_LITERAL(35, 393, 17), // "onResumeRequested"
QT_MOC_LITERAL(36, 411, 17), // "onCancelRequested"
QT_MOC_LITERAL(37, 429, 17), // "onRemoveRequested"
QT_MOC_LITERAL(38, 447, 17), // "onStatisticsTimer"
QT_MOC_LITERAL(39, 465, 21) // "processProgressUpdate"

    },
    "ProgressManager\0itemCreated\0\0itemId\0"
    "itemRemoved\0itemUpdated\0progressUpdated\0"
    "current\0total\0percentage\0messageUpdated\0"
    "message\0statusChanged\0ProgressStatus\0"
    "oldStatus\0newStatus\0itemStarted\0"
    "itemPaused\0itemResumed\0itemCompleted\0"
    "itemCancelled\0itemError\0errorMessage\0"
    "childAdded\0parentId\0childId\0childRemoved\0"
    "statisticsChanged\0running\0paused\0"
    "completed\0error\0cancelled\0onStartRequested\0"
    "onPauseRequested\0onResumeRequested\0"
    "onCancelRequested\0onRemoveRequested\0"
    "onStatisticsTimer\0processProgressUpdate"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ProgressManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      22,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      15,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  124,    2, 0x06 /* Public */,
       4,    1,  127,    2, 0x06 /* Public */,
       5,    1,  130,    2, 0x06 /* Public */,
       6,    4,  133,    2, 0x06 /* Public */,
      10,    2,  142,    2, 0x06 /* Public */,
      12,    3,  147,    2, 0x06 /* Public */,
      16,    1,  154,    2, 0x06 /* Public */,
      17,    1,  157,    2, 0x06 /* Public */,
      18,    1,  160,    2, 0x06 /* Public */,
      19,    1,  163,    2, 0x06 /* Public */,
      20,    1,  166,    2, 0x06 /* Public */,
      21,    2,  169,    2, 0x06 /* Public */,
      23,    2,  174,    2, 0x06 /* Public */,
      26,    2,  179,    2, 0x06 /* Public */,
      27,    6,  184,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      33,    1,  197,    2, 0x0a /* Public */,
      34,    1,  200,    2, 0x0a /* Public */,
      35,    1,  203,    2, 0x0a /* Public */,
      36,    1,  206,    2, 0x0a /* Public */,
      37,    1,  209,    2, 0x0a /* Public */,
      38,    0,  212,    2, 0x08 /* Private */,
      39,    3,  213,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::Int, QMetaType::Double,    3,    7,    8,    9,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,   11,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 13, 0x80000000 | 13,    3,   14,   15,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,   22,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   24,   25,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   24,   25,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int,    8,   28,   29,   30,   31,   32,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::QString,    3,    7,   11,

       0        // eod
};

void ProgressManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProgressManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->itemCreated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->itemRemoved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->itemUpdated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->progressUpdated((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4]))); break;
        case 4: _t->messageUpdated((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 5: _t->statusChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< ProgressStatus(*)>(_a[2])),(*reinterpret_cast< ProgressStatus(*)>(_a[3]))); break;
        case 6: _t->itemStarted((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->itemPaused((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->itemResumed((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 9: _t->itemCompleted((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 10: _t->itemCancelled((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 11: _t->itemError((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 12: _t->childAdded((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 13: _t->childRemoved((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 14: _t->statisticsChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4])),(*reinterpret_cast< int(*)>(_a[5])),(*reinterpret_cast< int(*)>(_a[6]))); break;
        case 15: _t->onStartRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 16: _t->onPauseRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 17: _t->onResumeRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 18: _t->onCancelRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 19: _t->onRemoveRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 20: _t->onStatisticsTimer(); break;
        case 21: _t->processProgressUpdate((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ProgressManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemCreated)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemRemoved)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemUpdated)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & , int , int , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::progressUpdated)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::messageUpdated)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & , ProgressStatus , ProgressStatus );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::statusChanged)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemStarted)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemPaused)) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemResumed)) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemCompleted)) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemCancelled)) {
                *result = 10;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::itemError)) {
                *result = 11;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::childAdded)) {
                *result = 12;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::childRemoved)) {
                *result = 13;
                return;
            }
        }
        {
            using _t = void (ProgressManager::*)(int , int , int , int , int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProgressManager::statisticsChanged)) {
                *result = 14;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ProgressManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_ProgressManager.data,
    qt_meta_data_ProgressManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ProgressManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ProgressManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ProgressManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ProgressManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 22)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 22;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 22)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 22;
    }
    return _id;
}

// SIGNAL 0
void ProgressManager::itemCreated(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ProgressManager::itemRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ProgressManager::itemUpdated(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ProgressManager::progressUpdated(const QString & _t1, int _t2, int _t3, double _t4)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ProgressManager::messageUpdated(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ProgressManager::statusChanged(const QString & _t1, ProgressStatus _t2, ProgressStatus _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void ProgressManager::itemStarted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void ProgressManager::itemPaused(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void ProgressManager::itemResumed(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void ProgressManager::itemCompleted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}

// SIGNAL 10
void ProgressManager::itemCancelled(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 10, _a);
}

// SIGNAL 11
void ProgressManager::itemError(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 11, _a);
}

// SIGNAL 12
void ProgressManager::childAdded(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 12, _a);
}

// SIGNAL 13
void ProgressManager::childRemoved(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 13, _a);
}

// SIGNAL 14
void ProgressManager::statisticsChanged(int _t1, int _t2, int _t3, int _t4, int _t5, int _t6)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t5))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t6))) };
    QMetaObject::activate(this, &staticMetaObject, 14, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
