/****************************************************************************
** Meta object code from reading C++ file 'ProgressManagerTest.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.17)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/componets/progress/tests/ProgressManagerTest.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ProgressManagerTest.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.17. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ProgressManagerTest_t {
    QByteArrayData data[47];
    char stringdata0[1040];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ProgressManagerTest_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ProgressManagerTest_t qt_meta_stringdata_ProgressManagerTest = {
    {
QT_MOC_LITERAL(0, 0, 19), // "ProgressManagerTest"
QT_MOC_LITERAL(1, 20, 12), // "initTestCase"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 15), // "cleanupTestCase"
QT_MOC_LITERAL(4, 50, 4), // "init"
QT_MOC_LITERAL(5, 55, 7), // "cleanup"
QT_MOC_LITERAL(6, 63, 22), // "testCreateProgressItem"
QT_MOC_LITERAL(7, 86, 32), // "testCreateProgressItemWithParent"
QT_MOC_LITERAL(8, 119, 22), // "testRemoveProgressItem"
QT_MOC_LITERAL(9, 142, 18), // "testUpdateProgress"
QT_MOC_LITERAL(10, 161, 17), // "testStatusChanges"
QT_MOC_LITERAL(11, 179, 27), // "testParentChildRelationship"
QT_MOC_LITERAL(12, 207, 29), // "testParentProgressCalculation"
QT_MOC_LITERAL(13, 237, 28), // "testRemoveParentWithChildren"
QT_MOC_LITERAL(14, 266, 31), // "testCircularReferenceProtection"
QT_MOC_LITERAL(15, 298, 18), // "testSignalEmission"
QT_MOC_LITERAL(16, 317, 21), // "testStatisticsSignals"
QT_MOC_LITERAL(17, 339, 16), // "testThreadSafety"
QT_MOC_LITERAL(18, 356, 20), // "testConcurrentAccess"
QT_MOC_LITERAL(19, 377, 21), // "testInvalidOperations"
QT_MOC_LITERAL(20, 399, 16), // "testEmptyStrings"
QT_MOC_LITERAL(21, 416, 18), // "testNegativeValues"
QT_MOC_LITERAL(22, 435, 16), // "testLargeNumbers"
QT_MOC_LITERAL(23, 452, 25), // "testInfiniteLoopDetection"
QT_MOC_LITERAL(24, 478, 24), // "testRecursiveParentChild"
QT_MOC_LITERAL(25, 503, 15), // "testSignalLoops"
QT_MOC_LITERAL(26, 519, 17), // "testAddTaskDialog"
QT_MOC_LITERAL(27, 537, 22), // "testAddTaskPerformance"
QT_MOC_LITERAL(28, 560, 21), // "testAddTaskSignalLoop"
QT_MOC_LITERAL(29, 582, 25), // "testProgressWidgetAddTask"
QT_MOC_LITERAL(30, 608, 17), // "testUIRefreshLoop"
QT_MOC_LITERAL(31, 626, 17), // "testPauseAllBasic"
QT_MOC_LITERAL(32, 644, 23), // "testPauseAllPerformance"
QT_MOC_LITERAL(33, 668, 20), // "testPauseAllDeadlock"
QT_MOC_LITERAL(34, 689, 27), // "testBatchOperationsDeadlock"
QT_MOC_LITERAL(35, 717, 22), // "testStartPauseAllCycle"
QT_MOC_LITERAL(36, 740, 25), // "testSimulateProgressBasic"
QT_MOC_LITERAL(37, 766, 28), // "testSimulateProgressDeadlock"
QT_MOC_LITERAL(38, 795, 26), // "testUpdateProgressDeadlock"
QT_MOC_LITERAL(39, 822, 28), // "testCompleteProgressDeadlock"
QT_MOC_LITERAL(40, 851, 27), // "testProgressSimulationCycle"
QT_MOC_LITERAL(41, 879, 25), // "testEmptyTaskListDeadlock"
QT_MOC_LITERAL(42, 905, 31), // "testSimulateProgressWithNoTasks"
QT_MOC_LITERAL(43, 937, 30), // "testBatchOperationsWithNoTasks"
QT_MOC_LITERAL(44, 968, 26), // "testEmptyManagerOperations"
QT_MOC_LITERAL(45, 995, 28), // "testPerformanceWithManyItems"
QT_MOC_LITERAL(46, 1024, 15) // "testMemoryLeaks"

    },
    "ProgressManagerTest\0initTestCase\0\0"
    "cleanupTestCase\0init\0cleanup\0"
    "testCreateProgressItem\0"
    "testCreateProgressItemWithParent\0"
    "testRemoveProgressItem\0testUpdateProgress\0"
    "testStatusChanges\0testParentChildRelationship\0"
    "testParentProgressCalculation\0"
    "testRemoveParentWithChildren\0"
    "testCircularReferenceProtection\0"
    "testSignalEmission\0testStatisticsSignals\0"
    "testThreadSafety\0testConcurrentAccess\0"
    "testInvalidOperations\0testEmptyStrings\0"
    "testNegativeValues\0testLargeNumbers\0"
    "testInfiniteLoopDetection\0"
    "testRecursiveParentChild\0testSignalLoops\0"
    "testAddTaskDialog\0testAddTaskPerformance\0"
    "testAddTaskSignalLoop\0testProgressWidgetAddTask\0"
    "testUIRefreshLoop\0testPauseAllBasic\0"
    "testPauseAllPerformance\0testPauseAllDeadlock\0"
    "testBatchOperationsDeadlock\0"
    "testStartPauseAllCycle\0testSimulateProgressBasic\0"
    "testSimulateProgressDeadlock\0"
    "testUpdateProgressDeadlock\0"
    "testCompleteProgressDeadlock\0"
    "testProgressSimulationCycle\0"
    "testEmptyTaskListDeadlock\0"
    "testSimulateProgressWithNoTasks\0"
    "testBatchOperationsWithNoTasks\0"
    "testEmptyManagerOperations\0"
    "testPerformanceWithManyItems\0"
    "testMemoryLeaks"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ProgressManagerTest[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      45,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  239,    2, 0x08 /* Private */,
       3,    0,  240,    2, 0x08 /* Private */,
       4,    0,  241,    2, 0x08 /* Private */,
       5,    0,  242,    2, 0x08 /* Private */,
       6,    0,  243,    2, 0x08 /* Private */,
       7,    0,  244,    2, 0x08 /* Private */,
       8,    0,  245,    2, 0x08 /* Private */,
       9,    0,  246,    2, 0x08 /* Private */,
      10,    0,  247,    2, 0x08 /* Private */,
      11,    0,  248,    2, 0x08 /* Private */,
      12,    0,  249,    2, 0x08 /* Private */,
      13,    0,  250,    2, 0x08 /* Private */,
      14,    0,  251,    2, 0x08 /* Private */,
      15,    0,  252,    2, 0x08 /* Private */,
      16,    0,  253,    2, 0x08 /* Private */,
      17,    0,  254,    2, 0x08 /* Private */,
      18,    0,  255,    2, 0x08 /* Private */,
      19,    0,  256,    2, 0x08 /* Private */,
      20,    0,  257,    2, 0x08 /* Private */,
      21,    0,  258,    2, 0x08 /* Private */,
      22,    0,  259,    2, 0x08 /* Private */,
      23,    0,  260,    2, 0x08 /* Private */,
      24,    0,  261,    2, 0x08 /* Private */,
      25,    0,  262,    2, 0x08 /* Private */,
      26,    0,  263,    2, 0x08 /* Private */,
      27,    0,  264,    2, 0x08 /* Private */,
      28,    0,  265,    2, 0x08 /* Private */,
      29,    0,  266,    2, 0x08 /* Private */,
      30,    0,  267,    2, 0x08 /* Private */,
      31,    0,  268,    2, 0x08 /* Private */,
      32,    0,  269,    2, 0x08 /* Private */,
      33,    0,  270,    2, 0x08 /* Private */,
      34,    0,  271,    2, 0x08 /* Private */,
      35,    0,  272,    2, 0x08 /* Private */,
      36,    0,  273,    2, 0x08 /* Private */,
      37,    0,  274,    2, 0x08 /* Private */,
      38,    0,  275,    2, 0x08 /* Private */,
      39,    0,  276,    2, 0x08 /* Private */,
      40,    0,  277,    2, 0x08 /* Private */,
      41,    0,  278,    2, 0x08 /* Private */,
      42,    0,  279,    2, 0x08 /* Private */,
      43,    0,  280,    2, 0x08 /* Private */,
      44,    0,  281,    2, 0x08 /* Private */,
      45,    0,  282,    2, 0x08 /* Private */,
      46,    0,  283,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ProgressManagerTest::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProgressManagerTest *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->initTestCase(); break;
        case 1: _t->cleanupTestCase(); break;
        case 2: _t->init(); break;
        case 3: _t->cleanup(); break;
        case 4: _t->testCreateProgressItem(); break;
        case 5: _t->testCreateProgressItemWithParent(); break;
        case 6: _t->testRemoveProgressItem(); break;
        case 7: _t->testUpdateProgress(); break;
        case 8: _t->testStatusChanges(); break;
        case 9: _t->testParentChildRelationship(); break;
        case 10: _t->testParentProgressCalculation(); break;
        case 11: _t->testRemoveParentWithChildren(); break;
        case 12: _t->testCircularReferenceProtection(); break;
        case 13: _t->testSignalEmission(); break;
        case 14: _t->testStatisticsSignals(); break;
        case 15: _t->testThreadSafety(); break;
        case 16: _t->testConcurrentAccess(); break;
        case 17: _t->testInvalidOperations(); break;
        case 18: _t->testEmptyStrings(); break;
        case 19: _t->testNegativeValues(); break;
        case 20: _t->testLargeNumbers(); break;
        case 21: _t->testInfiniteLoopDetection(); break;
        case 22: _t->testRecursiveParentChild(); break;
        case 23: _t->testSignalLoops(); break;
        case 24: _t->testAddTaskDialog(); break;
        case 25: _t->testAddTaskPerformance(); break;
        case 26: _t->testAddTaskSignalLoop(); break;
        case 27: _t->testProgressWidgetAddTask(); break;
        case 28: _t->testUIRefreshLoop(); break;
        case 29: _t->testPauseAllBasic(); break;
        case 30: _t->testPauseAllPerformance(); break;
        case 31: _t->testPauseAllDeadlock(); break;
        case 32: _t->testBatchOperationsDeadlock(); break;
        case 33: _t->testStartPauseAllCycle(); break;
        case 34: _t->testSimulateProgressBasic(); break;
        case 35: _t->testSimulateProgressDeadlock(); break;
        case 36: _t->testUpdateProgressDeadlock(); break;
        case 37: _t->testCompleteProgressDeadlock(); break;
        case 38: _t->testProgressSimulationCycle(); break;
        case 39: _t->testEmptyTaskListDeadlock(); break;
        case 40: _t->testSimulateProgressWithNoTasks(); break;
        case 41: _t->testBatchOperationsWithNoTasks(); break;
        case 42: _t->testEmptyManagerOperations(); break;
        case 43: _t->testPerformanceWithManyItems(); break;
        case 44: _t->testMemoryLeaks(); break;
        default: ;
        }
    }
    (void)_a;
}

QT_INIT_METAOBJECT const QMetaObject ProgressManagerTest::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_ProgressManagerTest.data,
    qt_meta_data_ProgressManagerTest,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ProgressManagerTest::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ProgressManagerTest::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ProgressManagerTest.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ProgressManagerTest::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 45)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 45;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 45)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 45;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
