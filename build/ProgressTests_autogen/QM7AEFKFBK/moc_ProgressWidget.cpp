/****************************************************************************
** Meta object code from reading C++ file 'ProgressWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.17)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/componets/progress/ProgressWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ProgressWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.17. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ProgressWidget_t {
    QByteArrayData data[36];
    char stringdata0[462];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ProgressWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ProgressWidget_t qt_meta_stringdata_ProgressWidget = {
    {
QT_MOC_LITERAL(0, 0, 14), // "ProgressWidget"
QT_MOC_LITERAL(1, 15, 16), // "onClearCompleted"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 10), // "onClearAll"
QT_MOC_LITERAL(4, 44, 10), // "onStartAll"
QT_MOC_LITERAL(5, 55, 10), // "onPauseAll"
QT_MOC_LITERAL(6, 66, 11), // "onCancelAll"
QT_MOC_LITERAL(7, 78, 11), // "onExpandAll"
QT_MOC_LITERAL(8, 90, 13), // "onCollapseAll"
QT_MOC_LITERAL(9, 104, 9), // "onRefresh"
QT_MOC_LITERAL(10, 114, 17), // "onNewProgressItem"
QT_MOC_LITERAL(11, 132, 13), // "onItemCreated"
QT_MOC_LITERAL(12, 146, 6), // "itemId"
QT_MOC_LITERAL(13, 153, 13), // "onItemRemoved"
QT_MOC_LITERAL(14, 167, 13), // "onItemUpdated"
QT_MOC_LITERAL(15, 181, 17), // "onProgressUpdated"
QT_MOC_LITERAL(16, 199, 7), // "current"
QT_MOC_LITERAL(17, 207, 5), // "total"
QT_MOC_LITERAL(18, 213, 10), // "percentage"
QT_MOC_LITERAL(19, 224, 15), // "onStatusChanged"
QT_MOC_LITERAL(20, 240, 14), // "ProgressStatus"
QT_MOC_LITERAL(21, 255, 9), // "oldStatus"
QT_MOC_LITERAL(22, 265, 9), // "newStatus"
QT_MOC_LITERAL(23, 275, 19), // "onStatisticsChanged"
QT_MOC_LITERAL(24, 295, 7), // "running"
QT_MOC_LITERAL(25, 303, 6), // "paused"
QT_MOC_LITERAL(26, 310, 9), // "completed"
QT_MOC_LITERAL(27, 320, 5), // "error"
QT_MOC_LITERAL(28, 326, 9), // "cancelled"
QT_MOC_LITERAL(29, 336, 16), // "onStartRequested"
QT_MOC_LITERAL(30, 353, 16), // "onPauseRequested"
QT_MOC_LITERAL(31, 370, 17), // "onResumeRequested"
QT_MOC_LITERAL(32, 388, 17), // "onCancelRequested"
QT_MOC_LITERAL(33, 406, 17), // "onRemoveRequested"
QT_MOC_LITERAL(34, 424, 19), // "onItemDoubleClicked"
QT_MOC_LITERAL(35, 444, 17) // "onSimulationTimer"

    },
    "ProgressWidget\0onClearCompleted\0\0"
    "onClearAll\0onStartAll\0onPauseAll\0"
    "onCancelAll\0onExpandAll\0onCollapseAll\0"
    "onRefresh\0onNewProgressItem\0onItemCreated\0"
    "itemId\0onItemRemoved\0onItemUpdated\0"
    "onProgressUpdated\0current\0total\0"
    "percentage\0onStatusChanged\0ProgressStatus\0"
    "oldStatus\0newStatus\0onStatisticsChanged\0"
    "running\0paused\0completed\0error\0cancelled\0"
    "onStartRequested\0onPauseRequested\0"
    "onResumeRequested\0onCancelRequested\0"
    "onRemoveRequested\0onItemDoubleClicked\0"
    "onSimulationTimer"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ProgressWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      22,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  124,    2, 0x0a /* Public */,
       3,    0,  125,    2, 0x0a /* Public */,
       4,    0,  126,    2, 0x0a /* Public */,
       5,    0,  127,    2, 0x0a /* Public */,
       6,    0,  128,    2, 0x0a /* Public */,
       7,    0,  129,    2, 0x0a /* Public */,
       8,    0,  130,    2, 0x0a /* Public */,
       9,    0,  131,    2, 0x0a /* Public */,
      10,    0,  132,    2, 0x0a /* Public */,
      11,    1,  133,    2, 0x0a /* Public */,
      13,    1,  136,    2, 0x0a /* Public */,
      14,    1,  139,    2, 0x0a /* Public */,
      15,    4,  142,    2, 0x0a /* Public */,
      19,    3,  151,    2, 0x0a /* Public */,
      23,    6,  158,    2, 0x0a /* Public */,
      29,    1,  171,    2, 0x0a /* Public */,
      30,    1,  174,    2, 0x0a /* Public */,
      31,    1,  177,    2, 0x0a /* Public */,
      32,    1,  180,    2, 0x0a /* Public */,
      33,    1,  183,    2, 0x0a /* Public */,
      34,    1,  186,    2, 0x0a /* Public */,
      35,    0,  189,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::Int, QMetaType::Double,   12,   16,   17,   18,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 20, 0x80000000 | 20,   12,   21,   22,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int,   17,   24,   25,   26,   27,   28,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void,

       0        // eod
};

void ProgressWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProgressWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onClearCompleted(); break;
        case 1: _t->onClearAll(); break;
        case 2: _t->onStartAll(); break;
        case 3: _t->onPauseAll(); break;
        case 4: _t->onCancelAll(); break;
        case 5: _t->onExpandAll(); break;
        case 6: _t->onCollapseAll(); break;
        case 7: _t->onRefresh(); break;
        case 8: _t->onNewProgressItem(); break;
        case 9: _t->onItemCreated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 10: _t->onItemRemoved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 11: _t->onItemUpdated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 12: _t->onProgressUpdated((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4]))); break;
        case 13: _t->onStatusChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< ProgressStatus(*)>(_a[2])),(*reinterpret_cast< ProgressStatus(*)>(_a[3]))); break;
        case 14: _t->onStatisticsChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4])),(*reinterpret_cast< int(*)>(_a[5])),(*reinterpret_cast< int(*)>(_a[6]))); break;
        case 15: _t->onStartRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 16: _t->onPauseRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 17: _t->onResumeRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 18: _t->onCancelRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 19: _t->onRemoveRequested((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 20: _t->onItemDoubleClicked((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 21: _t->onSimulationTimer(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ProgressWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_ProgressWidget.data,
    qt_meta_data_ProgressWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ProgressWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ProgressWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ProgressWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ProgressWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 22)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 22;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 22)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 22;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
