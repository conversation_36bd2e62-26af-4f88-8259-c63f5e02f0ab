/****************************************************************************
** Meta object code from reading C++ file 'EmptyTaskDeadlockTest.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.17)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/componets/progress/tests/EmptyTaskDeadlockTest.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'EmptyTaskDeadlockTest.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.17. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_EmptyTaskDeadlockTest_t {
    QByteArrayData data[14];
    char stringdata0[315];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_EmptyTaskDeadlockTest_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_EmptyTaskDeadlockTest_t qt_meta_stringdata_EmptyTaskDeadlockTest = {
    {
QT_MOC_LITERAL(0, 0, 21), // "EmptyTaskDeadlockTest"
QT_MOC_LITERAL(1, 22, 12), // "initTestCase"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 15), // "cleanupTestCase"
QT_MOC_LITERAL(4, 52, 4), // "init"
QT_MOC_LITERAL(5, 57, 7), // "cleanup"
QT_MOC_LITERAL(6, 65, 31), // "testEmptyManagerBatchOperations"
QT_MOC_LITERAL(7, 97, 25), // "testEmptySimulateProgress"
QT_MOC_LITERAL(8, 123, 33), // "testEmptyProgressWidgetOperat..."
QT_MOC_LITERAL(9, 157, 27), // "testEmptyManagerWithSignals"
QT_MOC_LITERAL(10, 185, 32), // "testEmptyManagerConcurrentAccess"
QT_MOC_LITERAL(11, 218, 32), // "testEmptyManagerStatisticsUpdate"
QT_MOC_LITERAL(12, 251, 31), // "testEmptyManagerTimerOperations"
QT_MOC_LITERAL(13, 283, 31) // "testEmptyManagerClearOperations"

    },
    "EmptyTaskDeadlockTest\0initTestCase\0\0"
    "cleanupTestCase\0init\0cleanup\0"
    "testEmptyManagerBatchOperations\0"
    "testEmptySimulateProgress\0"
    "testEmptyProgressWidgetOperations\0"
    "testEmptyManagerWithSignals\0"
    "testEmptyManagerConcurrentAccess\0"
    "testEmptyManagerStatisticsUpdate\0"
    "testEmptyManagerTimerOperations\0"
    "testEmptyManagerClearOperations"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_EmptyTaskDeadlockTest[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   74,    2, 0x08 /* Private */,
       3,    0,   75,    2, 0x08 /* Private */,
       4,    0,   76,    2, 0x08 /* Private */,
       5,    0,   77,    2, 0x08 /* Private */,
       6,    0,   78,    2, 0x08 /* Private */,
       7,    0,   79,    2, 0x08 /* Private */,
       8,    0,   80,    2, 0x08 /* Private */,
       9,    0,   81,    2, 0x08 /* Private */,
      10,    0,   82,    2, 0x08 /* Private */,
      11,    0,   83,    2, 0x08 /* Private */,
      12,    0,   84,    2, 0x08 /* Private */,
      13,    0,   85,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void EmptyTaskDeadlockTest::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<EmptyTaskDeadlockTest *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->initTestCase(); break;
        case 1: _t->cleanupTestCase(); break;
        case 2: _t->init(); break;
        case 3: _t->cleanup(); break;
        case 4: _t->testEmptyManagerBatchOperations(); break;
        case 5: _t->testEmptySimulateProgress(); break;
        case 6: _t->testEmptyProgressWidgetOperations(); break;
        case 7: _t->testEmptyManagerWithSignals(); break;
        case 8: _t->testEmptyManagerConcurrentAccess(); break;
        case 9: _t->testEmptyManagerStatisticsUpdate(); break;
        case 10: _t->testEmptyManagerTimerOperations(); break;
        case 11: _t->testEmptyManagerClearOperations(); break;
        default: ;
        }
    }
    (void)_a;
}

QT_INIT_METAOBJECT const QMetaObject EmptyTaskDeadlockTest::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_EmptyTaskDeadlockTest.data,
    qt_meta_data_EmptyTaskDeadlockTest,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *EmptyTaskDeadlockTest::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *EmptyTaskDeadlockTest::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_EmptyTaskDeadlockTest.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int EmptyTaskDeadlockTest::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
