cmake_minimum_required(VERSION 4.0)
project(BaseWidget)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)


find_package(Qt5 COMPONENTS
        Core
        Gui
        Widgets
        Test
        REQUIRED)

# 通知组件源文件
set(NOTIFICATION_SOURCES
    src/componets/notification/NotificationItem.cpp
    src/componets/notification/NotificationItemWidget.cpp
    src/componets/notification/NotificationListWidget.cpp
    src/componets/notification/NotificationWindow.cpp
    src/componets/notification/BalloonNotification.cpp
    src/componets/notification/BalloonNotificationManager.cpp
)

# 通知组件头文件
set(NOTIFICATION_HEADERS
    src/componets/notification/NotificationItem.h
    src/componets/notification/NotificationItemWidget.h
    src/componets/notification/NotificationListWidget.h
    src/componets/notification/NotificationWindow.h
    src/componets/notification/BalloonNotification.h
    src/componets/notification/BalloonNotificationManager.h
)

# 进度组件源文件
set(PROGRESS_SOURCES
    src/componets/progress/ProgressItem.cpp
    src/componets/progress/ProgressItemWidget.cpp
    src/componets/progress/ProgressListWidget.cpp
    src/componets/progress/ProgressManager.cpp
    src/componets/progress/ProgressWidget.cpp
)

# 进度组件头文件
set(PROGRESS_HEADERS
    src/componets/progress/ProgressItem.h
    src/componets/progress/ProgressItemWidget.h
    src/componets/progress/ProgressListWidget.h
    src/componets/progress/ProgressManager.h
    src/componets/progress/ProgressWidget.h
)

add_executable(BaseWidget
    main.cpp
    ${NOTIFICATION_SOURCES}
    ${NOTIFICATION_HEADERS}
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(BaseWidget
        Qt5::Core
        Qt5::Gui
        Qt5::Widgets
)

# 包含头文件目录
target_include_directories(BaseWidget PRIVATE
    src/componets/notification
    src/componets/progress
)

# 测试源文件
set(TEST_SOURCES
    src/componets/progress/tests/ProgressManagerTest.cpp
    ${PROGRESS_SOURCES}
)

set(TEST_HEADERS
    src/componets/progress/tests/ProgressManagerTest.h
    ${PROGRESS_HEADERS}
)

# 创建测试可执行文件
add_executable(ProgressTests
    src/componets/progress/tests/test_runner.cpp
    ${TEST_SOURCES}
    ${TEST_HEADERS}
)

target_link_libraries(ProgressTests
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::Test
)

target_include_directories(ProgressTests PRIVATE
    src/componets/notification
    src/componets/progress
)

# 启用测试
enable_testing()
add_test(NAME ProgressManagerTests COMMAND ProgressTests)

# 手动测试程序
add_executable(ManualTest
    src/componets/progress/tests/manual_test.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(ManualTest
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

target_include_directories(ManualTest PRIVATE
    src/componets/notification
    src/componets/progress
)

# 调试测试程序
add_executable(DebugAddTask
    src/componets/progress/tests/debug_add_task.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(DebugAddTask
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

target_include_directories(DebugAddTask PRIVATE
    src/componets/notification
    src/componets/progress
)

# Debug Pause All 程序
add_executable(DebugPauseAll
    src/componets/progress/tests/debug_pause_all.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(DebugPauseAll
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

target_include_directories(DebugPauseAll PRIVATE
    src/componets/notification
    src/componets/progress
)

# Debug Simulate Progress 程序
add_executable(DebugSimulate
    src/componets/progress/tests/debug_simulate.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(DebugSimulate
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

target_include_directories(DebugSimulate PRIVATE
    src/componets/notification
    src/componets/progress
)

# Empty Task Deadlock 测试程序
add_executable(EmptyTaskDeadlockTest
    src/componets/progress/tests/EmptyTaskDeadlockTest.cpp
    src/componets/progress/tests/EmptyTaskDeadlockTest.h
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(EmptyTaskDeadlockTest
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::Test
)

target_include_directories(EmptyTaskDeadlockTest PRIVATE
    src/componets/notification
    src/componets/progress
)

# Simple Empty Task 测试程序
add_executable(SimpleEmptyTaskTest
    src/componets/progress/tests/SimpleEmptyTaskTest.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(SimpleEmptyTaskTest
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::Test
)

target_include_directories(SimpleEmptyTaskTest PRIVATE
    src/componets/notification
    src/componets/progress
)

# Direct Simulate Progress 调试程序
add_executable(DebugSimulateDirect
    src/componets/progress/tests/debug_simulate_direct.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(DebugSimulateDirect
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

target_include_directories(DebugSimulateDirect PRIVATE
    src/componets/notification
    src/componets/progress
)

# Step by Step 调试程序
add_executable(DebugStepByStep
    src/componets/progress/tests/debug_step_by_step.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(DebugStepByStep
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

target_include_directories(DebugStepByStep PRIVATE
    src/componets/notification
    src/componets/progress
)

# Single Item 调试程序
add_executable(DebugSingleItem
    src/componets/progress/tests/debug_single_item.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(DebugSingleItem
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

target_include_directories(DebugSingleItem PRIVATE
    src/componets/notification
    src/componets/progress
)

# Final Simulate Test 程序
add_executable(FinalSimulateTest
    src/componets/progress/tests/final_simulate_test.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(FinalSimulateTest
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

target_include_directories(FinalSimulateTest PRIVATE
    src/componets/notification
    src/componets/progress
)

# CLI 调试程序
add_executable(CLIDebug
    src/componets/progress/tests/cli_debug.cpp
    ${PROGRESS_SOURCES}
    ${PROGRESS_HEADERS}
)

target_link_libraries(CLIDebug
    Qt5::Core
    Qt5::Widgets
)

target_include_directories(CLIDebug PRIVATE
    src/componets/notification
    src/componets/progress
)

