#ifndef PROGRESSITEM_H
#define PROGRESSITEM_H

#include <QDateTime>
#include <QIcon>
#include <QList>
#include <QSharedPointer>

/**
 * @brief 进度状态枚举
 */
enum class ProgressStatus
{
    NotStarted, // 未开始
    Running, // 运行中
    Paused, // 暂停
    Completed, // 已完成
    Error, // 错误
    Cancelled // 已取消
};

/**
 * @brief 进度项数据模型
 * 支持层级结构，父项目的进度为所有子项目的平均进度
 */
class ProgressItem : public QEnableSharedFromThis<ProgressItem>
{
public:
    ProgressItem();
    explicit ProgressItem(const QString& title,
                          const QString& message = QString(),
                          int total = 100);

    // 基本属性
    const QString& id() const { return m_id; }
    const QString& title() const { return m_title; }
    const QString& message() const { return m_message; }
    int current() const { return m_current; }
    int total() const { return m_total; }
    const ProgressStatus& status() const { return m_status; }
    const QDateTime& startTime() const { return m_startTime; }
    const QDateTime& endTime() const { return m_endTime; }

    // 计算进度百分比 (0-100)
    double progressPercentage() const;

    // 获取格式化的进度文本
    QString getProgressText() const;

    // 获取状态图标
    QIcon getStatusIcon() const;

    // 获取状态颜色
    QString getStatusColor() const;

    // 获取格式化的时间字符串
    QString getFormattedStartTime() const;
    QString getFormattedEndTime() const;
    QString getElapsedTime() const;

    // 设置器
    void setTitle(const QString& title) { m_title = title; }
    void setMessage(const QString& message) { m_message = message; }
    void setCurrent(int current);
    void setTotal(int total);
    void setStatus(ProgressStatus status);
    void setStartTime(const QDateTime& startTime) { m_startTime = startTime; }
    void setEndTime(const QDateTime& endTime) { m_endTime = endTime; }

    // 进度更新
    void updateProgress(int current, const QString& message = QString());
    void incrementProgress(int increment = 1, const QString& message = QString());

    // 状态控制
    void start();
    void pause();
    void resume();
    void complete();
    void cancel();
    void setError(const QString& errorMessage = QString());

    // 父子关系管理
    QSharedPointer<ProgressItem> parent() const { return m_parent.lock(); }
    void setParent(QSharedPointer<ProgressItem> parent);

    QList<QSharedPointer<ProgressItem>> children() const { return m_children; }
    void addChild(QSharedPointer<ProgressItem> child);
    void removeChild(QSharedPointer<ProgressItem> child);
    void removeChild(const QString& childId);
    QSharedPointer<ProgressItem> findChild(const QString& childId) const;

    // 判断是否为父项目
    bool isParent() const { return !m_children.isEmpty(); }

    // 判断是否为子项目
    bool isChild() const { return !m_parent.isNull(); }

    // 获取所有后代项目（递归）
    QList<QSharedPointer<ProgressItem>> getAllDescendants() const;

private:
    void updateParentProgress() const;
    void calculateParentProgress();
    static QString formatDuration(qint64 milliseconds);

    QString m_id; // 唯一ID
    QString m_title; // 标题
    QString m_message; // 实时消息
    int m_current; // 当前进度
    int m_total; // 总进度
    ProgressStatus m_status; // 状态
    QDateTime m_startTime; // 开始时间
    QDateTime m_endTime; // 结束时间

    // 父子关系
    QWeakPointer<ProgressItem> m_parent;
    QList<QSharedPointer<ProgressItem>> m_children;
};

// 类型别名
using ProgressItemPtr = QSharedPointer<ProgressItem>;

#endif // PROGRESSITEM_H
