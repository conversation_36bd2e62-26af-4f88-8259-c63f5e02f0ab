#include "ProgressListWidget.h"
#include <QApplication>
#include <QStyle>
#include <QDebug>

ProgressListWidget::ProgressListWidget(QWidget *parent)
    : QWidget(parent)
    , m_currentFilter(ProgressFilter::All)
{
    setupUI();
    setupToolBar();
    setupScrollArea();
    
    // 启动刷新定时器（延迟启动，避免初始化时的问题）
    m_refreshTimer = new QTimer(this);
    connect(m_refreshTimer, &QTimer::timeout, this, &ProgressListWidget::onRefreshTimer);

    // 延迟启动定时器，确保所有初始化完成
    QTimer::singleShot(1000, [this]() {
        if (m_refreshTimer) {
            m_refreshTimer->start(REFRESH_INTERVAL);
        }
    });
    
    updateStatistics();
}

void ProgressListWidget::addProgressItem(ProgressItemPtr item)
{
    if (!item || m_items.contains(item->id())) {
        return;
    }

    m_items[item->id()] = item;

    // 不立即创建UI组件，让定时器处理刷新
    // 这样可以避免在批量添加时的死锁问题

    // 延迟刷新显示，避免频繁刷新导致的死锁
    QTimer::singleShot(50, this, [this]() {
        refreshDisplay();
        updateStatistics();
    });
}

void ProgressListWidget::removeProgressItem(const QString& itemId)
{
    if (!m_items.contains(itemId)) {
        return;
    }
    
    ProgressItemPtr item = m_items[itemId];
    
    // 移除所有子项目
    for (auto child : item->children()) {
        removeProgressItem(child->id());
    }
    
    // 从父项目中移除
    if (auto parent = item->parent()) {
        parent->removeChild(item);
    }
    
    // 移除UI组件
    removeItemWidget(itemId);
    
    // 移除数据
    m_items.remove(itemId);
    m_expandedStates.remove(itemId);

    // 延迟刷新显示，避免频繁刷新导致的死锁
    QTimer::singleShot(0, this, [this]() {
        refreshDisplay();
        updateStatistics();
    });
}

void ProgressListWidget::updateProgressItem(const QString& itemId)
{
    updateItemWidget(itemId);
    updateStatistics();
}

void ProgressListWidget::clearAllItems()
{
    // 清除所有UI组件
    for (auto widget : m_itemWidgets.values()) {
        widget->deleteLater();
    }
    
    // 清除数据
    m_items.clear();
    m_itemWidgets.clear();
    m_expandedStates.clear();
    
    refreshDisplay();
    updateStatistics();
}

ProgressItemPtr ProgressListWidget::getProgressItem(const QString& itemId) const
{
    return m_items.value(itemId);
}

QList<ProgressItemPtr> ProgressListWidget::getAllItems() const
{
    return m_items.values();
}

QList<ProgressItemPtr> ProgressListWidget::getFilteredItems() const
{
    QList<ProgressItemPtr> filtered;
    
    for (auto item : m_items.values()) {
        if (shouldShowItem(item)) {
            filtered.append(item);
        }
    }
    
    return filtered;
}

void ProgressListWidget::setFilter(ProgressFilter filter)
{
    if (m_currentFilter != filter) {
        m_currentFilter = filter;
        m_filterComboBox->setCurrentIndex(static_cast<int>(filter));
        refreshDisplay();
    }
}

void ProgressListWidget::setSearchText(const QString& text)
{
    if (m_searchText != text) {
        m_searchText = text;
        m_searchLineEdit->setText(text);
        refreshDisplay();
    }
}

void ProgressListWidget::expandAll()
{
    for (auto item : m_items.values()) {
        if (item->isParent()) {
            setItemExpanded(item->id(), true);
        }
    }
    refreshDisplay();
}

void ProgressListWidget::collapseAll()
{
    for (auto item : m_items.values()) {
        if (item->isParent()) {
            setItemExpanded(item->id(), false);
        }
    }
    refreshDisplay();
}

void ProgressListWidget::expandItem(const QString& itemId)
{
    setItemExpanded(itemId, true);
    refreshDisplay();
}

void ProgressListWidget::collapseItem(const QString& itemId)
{
    setItemExpanded(itemId, false);
    refreshDisplay();
}

int ProgressListWidget::getTotalCount() const
{
    return m_items.size();
}

int ProgressListWidget::getRunningCount() const
{
    int count = 0;
    for (auto item : m_items.values()) {
        if (item->status() == ProgressStatus::Running) {
            count++;
        }
    }
    return count;
}

int ProgressListWidget::getCompletedCount() const
{
    int count = 0;
    for (auto item : m_items.values()) {
        if (item->status() == ProgressStatus::Completed) {
            count++;
        }
    }
    return count;
}

int ProgressListWidget::getErrorCount() const
{
    int count = 0;
    for (auto item : m_items.values()) {
        if (item->status() == ProgressStatus::Error) {
            count++;
        }
    }
    return count;
}

// 工具栏操作槽函数
void ProgressListWidget::onClearCompleted()
{
    QStringList toRemove;
    for (auto item : m_items.values()) {
        if (item->status() == ProgressStatus::Completed) {
            toRemove.append(item->id());
        }
    }
    
    for (const QString& id : toRemove) {
        removeProgressItem(id);
    }
}

void ProgressListWidget::onClearAll()
{
    clearAllItems();
}

void ProgressListWidget::onExpandAll()
{
    expandAll();
}

void ProgressListWidget::onCollapseAll()
{
    collapseAll();
}

void ProgressListWidget::onFilterChanged(int index)
{
    setFilter(static_cast<ProgressFilter>(index));
}

void ProgressListWidget::onSearchTextChanged(const QString& text)
{
    setSearchText(text);
}

// 进度项操作槽函数
void ProgressListWidget::onItemStartRequested(const QString& itemId)
{
    emit startRequested(itemId);
}

void ProgressListWidget::onItemPauseRequested(const QString& itemId)
{
    emit pauseRequested(itemId);
}

void ProgressListWidget::onItemResumeRequested(const QString& itemId)
{
    emit resumeRequested(itemId);
}

void ProgressListWidget::onItemCancelRequested(const QString& itemId)
{
    emit cancelRequested(itemId);
}

void ProgressListWidget::onItemRemoveRequested(const QString& itemId)
{
    emit removeRequested(itemId);
}

void ProgressListWidget::onItemExpandToggled(const QString& itemId, bool expanded)
{
    setItemExpanded(itemId, expanded);
    refreshDisplay();
}

void ProgressListWidget::onItemDoubleClicked(const QString& itemId)
{
    emit itemDoubleClicked(itemId);
}

void ProgressListWidget::onRefreshTimer()
{
    // 定期刷新显示和统计信息
    for (auto widget : m_itemWidgets.values()) {
        widget->updateDisplay();
    }
    updateStatistics();
}

// 私有方法实现
void ProgressListWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    setLayout(m_mainLayout);
}

void ProgressListWidget::setupToolBar()
{
    // 工具栏布局
    m_toolBarLayout = new QHBoxLayout();
    m_toolBarLayout->setContentsMargins(10, 5, 10, 5);
    m_toolBarLayout->setSpacing(10);

    // 过滤器
    m_filterComboBox = new QComboBox(this);
    m_filterComboBox->addItem("All", static_cast<int>(ProgressFilter::All));
    m_filterComboBox->addItem("Running", static_cast<int>(ProgressFilter::Running));
    m_filterComboBox->addItem("Paused", static_cast<int>(ProgressFilter::Paused));
    m_filterComboBox->addItem("Completed", static_cast<int>(ProgressFilter::Completed));
    m_filterComboBox->addItem("Error", static_cast<int>(ProgressFilter::Error));
    m_filterComboBox->addItem("Active", static_cast<int>(ProgressFilter::Active));
    connect(m_filterComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ProgressListWidget::onFilterChanged);

    // 搜索框
    m_searchLineEdit = new QLineEdit(this);
    m_searchLineEdit->setPlaceholderText("Search progress items...");
    connect(m_searchLineEdit, &QLineEdit::textChanged,
            this, &ProgressListWidget::onSearchTextChanged);

    // 展开/折叠按钮
    m_expandAllButton = new QPushButton("Expand All", this);
    m_collapseAllButton = new QPushButton("Collapse All", this);
    connect(m_expandAllButton, &QPushButton::clicked, this, &ProgressListWidget::onExpandAll);
    connect(m_collapseAllButton, &QPushButton::clicked, this, &ProgressListWidget::onCollapseAll);

    // 清理按钮
    m_clearCompletedButton = new QPushButton("Clear Completed", this);
    m_clearAllButton = new QPushButton("Clear All", this);
    connect(m_clearCompletedButton, &QPushButton::clicked, this, &ProgressListWidget::onClearCompleted);
    connect(m_clearAllButton, &QPushButton::clicked, this, &ProgressListWidget::onClearAll);

    // 统计信息标签
    m_statisticsLabel = new QLabel(this);
    m_statisticsLabel->setStyleSheet("color: #666; font-size: 11px;");

    // 组装工具栏
    m_toolBarLayout->addWidget(new QLabel("Filter:"));
    m_toolBarLayout->addWidget(m_filterComboBox);
    m_toolBarLayout->addWidget(m_searchLineEdit, 1);
    m_toolBarLayout->addWidget(m_expandAllButton);
    m_toolBarLayout->addWidget(m_collapseAllButton);
    m_toolBarLayout->addWidget(m_clearCompletedButton);
    m_toolBarLayout->addWidget(m_clearAllButton);
    m_toolBarLayout->addStretch();
    m_toolBarLayout->addWidget(m_statisticsLabel);

    m_mainLayout->addLayout(m_toolBarLayout);
}

void ProgressListWidget::setupScrollArea()
{
    // 滚动区域
    m_scrollArea = new QScrollArea(this);
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 滚动内容
    m_scrollContent = new QWidget();
    m_scrollLayout = new QVBoxLayout(m_scrollContent);
    m_scrollLayout->setContentsMargins(0, 0, 0, 0);
    m_scrollLayout->setSpacing(1);
    m_scrollLayout->addStretch();

    m_scrollArea->setWidget(m_scrollContent);

    // 空状态标签
    m_emptyLabel = new QLabel("No progress items", this);
    m_emptyLabel->setAlignment(Qt::AlignCenter);
    m_emptyLabel->setStyleSheet("color: #999; font-size: 14px; padding: 50px;");
    m_emptyLabel->hide();

    m_mainLayout->addWidget(m_scrollArea, 1);
    m_mainLayout->addWidget(m_emptyLabel, 1);
}

void ProgressListWidget::refreshDisplay()
{
    // 清除现有的UI组件
    for (auto widget : m_itemWidgets.values()) {
        widget->hide();
        m_scrollLayout->removeWidget(widget);
    }

    // 重新添加符合条件的项目
    QList<ProgressItemPtr> rootItems;
    for (auto item : m_items.values()) {
        if (!item->isChild() && shouldShowItem(item)) {
            rootItems.append(item);
        }
    }

    // 按标题排序
    std::sort(rootItems.begin(), rootItems.end(),
              [](const ProgressItemPtr& a, const ProgressItemPtr& b) {
                  return a->title() < b->title();
              });

    // 添加到布局中
    for (auto item : rootItems) {
        addItemToLayoutRecursive(item, 0);
    }

    // 显示空状态
    bool isEmpty = rootItems.isEmpty();
    m_scrollArea->setVisible(!isEmpty);
    m_emptyLabel->setVisible(isEmpty);

    updateItemVisibility();
}

void ProgressListWidget::addItemToLayoutRecursive(ProgressItemPtr item, int indentLevel)
{
    if (!item) return;

    // 获取或创建widget
    ProgressItemWidget* widget = m_itemWidgets.value(item->id());
    if (!widget) {
        widget = new ProgressItemWidget(item, this);
        m_itemWidgets[item->id()] = widget;

        // 连接信号
        connect(widget, &ProgressItemWidget::startRequested,
                this, &ProgressListWidget::onItemStartRequested);
        connect(widget, &ProgressItemWidget::pauseRequested,
                this, &ProgressListWidget::onItemPauseRequested);
        connect(widget, &ProgressItemWidget::resumeRequested,
                this, &ProgressListWidget::onItemResumeRequested);
        connect(widget, &ProgressItemWidget::cancelRequested,
                this, &ProgressListWidget::onItemCancelRequested);
        connect(widget, &ProgressItemWidget::removeRequested,
                this, &ProgressListWidget::onItemRemoveRequested);
        connect(widget, &ProgressItemWidget::expandToggled,
                this, &ProgressListWidget::onItemExpandToggled);
        connect(widget, &ProgressItemWidget::itemDoubleClicked,
                this, &ProgressListWidget::onItemDoubleClicked);
    }

    // 设置缩进和显示状态
    widget->setIndentLevel(indentLevel);
    widget->setShowChildren(isItemExpanded(item->id()));
    widget->updateDisplay();

    // 添加到布局
    m_scrollLayout->insertWidget(m_scrollLayout->count() - 1, widget);
    widget->show();

    // 递归添加子项目
    if (isItemExpanded(item->id())) {
        for (auto child : item->children()) {
            if (shouldShowItem(child)) {
                addItemToLayoutRecursive(child, indentLevel + 1);
            }
        }
    }
}

void ProgressListWidget::updateItemVisibility()
{
    for (auto widget : m_itemWidgets.values()) {
        ProgressItemPtr item = widget->getProgressItem();
        bool visible = shouldShowItem(item);
        widget->setVisible(visible);
    }
}

void ProgressListWidget::updateStatistics()
{
    int total = getTotalCount();
    int running = getRunningCount();
    int completed = getCompletedCount();
    int error = getErrorCount();

    QString text = QString("Total: %1 | Running: %2 | Completed: %3 | Error: %4")
                   .arg(total).arg(running).arg(completed).arg(error);
    m_statisticsLabel->setText(text);

    emit countChanged(total, running, completed, error);
}

void ProgressListWidget::addItemWidget(ProgressItemPtr item, int indentLevel)
{
    if (!item || m_itemWidgets.contains(item->id())) {
        return;
    }

    ProgressItemWidget* widget = new ProgressItemWidget(item, this);
    widget->setIndentLevel(indentLevel);
    m_itemWidgets[item->id()] = widget;

    // 连接信号
    connect(widget, &ProgressItemWidget::startRequested,
            this, &ProgressListWidget::onItemStartRequested);
    connect(widget, &ProgressItemWidget::pauseRequested,
            this, &ProgressListWidget::onItemPauseRequested);
    connect(widget, &ProgressItemWidget::resumeRequested,
            this, &ProgressListWidget::onItemResumeRequested);
    connect(widget, &ProgressItemWidget::cancelRequested,
            this, &ProgressListWidget::onItemCancelRequested);
    connect(widget, &ProgressItemWidget::removeRequested,
            this, &ProgressListWidget::onItemRemoveRequested);
    connect(widget, &ProgressItemWidget::expandToggled,
            this, &ProgressListWidget::onItemExpandToggled);
    connect(widget, &ProgressItemWidget::itemDoubleClicked,
            this, &ProgressListWidget::onItemDoubleClicked);
}

void ProgressListWidget::removeItemWidget(const QString& itemId)
{
    if (m_itemWidgets.contains(itemId)) {
        ProgressItemWidget* widget = m_itemWidgets[itemId];
        m_scrollLayout->removeWidget(widget);
        widget->deleteLater();
        m_itemWidgets.remove(itemId);
    }
}

void ProgressListWidget::updateItemWidget(const QString& itemId)
{
    if (m_itemWidgets.contains(itemId)) {
        m_itemWidgets[itemId]->updateDisplay();
    }
}

bool ProgressListWidget::shouldShowItem(ProgressItemPtr item) const
{
    if (!item) return false;

    // 检查搜索文本
    if (!matchesSearchText(item)) {
        return false;
    }

    // 检查过滤器
    switch (m_currentFilter) {
        case ProgressFilter::All:
            return true;
        case ProgressFilter::Running:
            return item->status() == ProgressStatus::Running;
        case ProgressFilter::Paused:
            return item->status() == ProgressStatus::Paused;
        case ProgressFilter::Completed:
            return item->status() == ProgressStatus::Completed;
        case ProgressFilter::Error:
            return item->status() == ProgressStatus::Error;
        case ProgressFilter::Active:
            return item->status() == ProgressStatus::Running ||
                   item->status() == ProgressStatus::Paused;
        default:
            return true;
    }
}

bool ProgressListWidget::matchesSearchText(ProgressItemPtr item) const
{
    if (m_searchText.isEmpty()) {
        return true;
    }

    QString searchLower = m_searchText.toLower();
    return item->title().toLower().contains(searchLower) ||
           item->message().toLower().contains(searchLower);
}

void ProgressListWidget::setItemExpanded(const QString& itemId, bool expanded)
{
    m_expandedStates[itemId] = expanded;
}

bool ProgressListWidget::isItemExpanded(const QString& itemId) const
{
    return m_expandedStates.value(itemId, true); // 默认展开
}
