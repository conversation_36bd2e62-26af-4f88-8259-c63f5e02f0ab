#include <QApplication>
#include <QDebug>
#include <QElapsedTimer>
#include <QTimer>
#include <QEventLoop>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

class PauseAllDebugger : public QObject
{
    Q_OBJECT

public:
    PauseAllDebugger(QObject* parent = nullptr) : QObject(parent)
    {
        m_manager = ProgressManager::instance();
        
        // 连接信号以监控
        connect(m_manager, &ProgressManager::itemCreated,
                this, [](const QString& id) { qDebug() << "Signal: itemCreated" << id; });
        connect(m_manager, &ProgressManager::itemUpdated,
                this, [](const QString& id) { qDebug() << "Signal: itemUpdated" << id; });
        connect(m_manager, &ProgressManager::itemPaused,
                this, [](const QString& id) { qDebug() << "Signal: itemPaused" << id; });
        connect(m_manager, &ProgressManager::statusChanged,
                this, [](const QString& id, ProgressStatus old, ProgressStatus newStatus) { 
                    qDebug() << "Signal: statusChanged" << id << "from" << (int)old << "to" << (int)newStatus; 
                });
    }

    void runTests()
    {
        qDebug() << "=== Pause All Debug Tests ===";

        testDirectPauseAll();
        // testProgressWidgetPauseAll();  // 暂时跳过，可能有问题
        testRapidPauseAll();

        qDebug() << "=== All Tests Completed ===";
    }

private slots:
    void testDirectPauseAll()
    {
        qDebug() << "\n1. Testing Direct ProgressManager::pauseAllItems()";
        
        // 创建并启动任务
        QString taskId = m_manager->createProgressItem("Direct Pause Test", "Testing direct pause", 100);
        m_manager->startProgress(taskId);
        
        QElapsedTimer timer;
        timer.start();
        
        qDebug() << "   Calling pauseAllItems...";
        m_manager->pauseAllItems();
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: Pause All completed in" << elapsed << "ms";
        
        // 验证状态
        ProgressItemPtr item = m_manager->getProgressItem(taskId);
        if (item && item->status() == ProgressStatus::Paused) {
            qDebug() << "   Status: ✅ PASS";
        } else {
            qDebug() << "   Status: ❌ FAIL";
        }
        
        // 清理
        m_manager->removeProgressItem(taskId);
    }
    
    void testProgressWidgetPauseAll()
    {
        qDebug() << "\n2. Testing ProgressWidget::onPauseAll()";
        
        // 创建 ProgressWidget
        qDebug() << "   Creating ProgressWidget...";
        ProgressWidget* widget = new ProgressWidget();
        
        // 创建并启动任务
        QString taskId = m_manager->createProgressItem("Widget Pause Test", "Testing widget pause", 100);
        m_manager->startProgress(taskId);
        
        QElapsedTimer timer;
        timer.start();
        
        qDebug() << "   Calling widget->onPauseAll()...";
        widget->onPauseAll();
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: Widget Pause All completed in" << elapsed << "ms";
        
        // 验证状态
        ProgressItemPtr item = m_manager->getProgressItem(taskId);
        if (item && item->status() == ProgressStatus::Paused) {
            qDebug() << "   Status: ✅ PASS";
        } else {
            qDebug() << "   Status: ❌ FAIL";
        }
        
        // 清理
        m_manager->removeProgressItem(taskId);
        widget->deleteLater();
    }
    
    void testRapidPauseAll()
    {
        qDebug() << "\n3. Testing Rapid Pause All Operations";
        
        // 创建多个任务
        QStringList taskIds;
        for (int i = 0; i < 5; ++i) {
            QString taskId = m_manager->createProgressItem(
                QString("Rapid Test %1").arg(i), 
                "Testing rapid operations", 
                100
            );
            m_manager->startProgress(taskId);
            taskIds.append(taskId);
        }
        
        QElapsedTimer timer;
        timer.start();
        
        qDebug() << "   Performing rapid start/pause operations...";
        for (int cycle = 0; cycle < 3; ++cycle) {
            qDebug() << "     Cycle" << (cycle + 1) << ": Start All";
            m_manager->startAllItems();
            
            qDebug() << "     Cycle" << (cycle + 1) << ": Pause All";
            m_manager->pauseAllItems();
        }
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: Rapid operations completed in" << elapsed << "ms";
        
        // 验证最终状态
        int pausedCount = 0;
        for (const QString& taskId : taskIds) {
            ProgressItemPtr item = m_manager->getProgressItem(taskId);
            if (item && item->status() == ProgressStatus::Paused) {
                pausedCount++;
            }
        }
        
        if (pausedCount == taskIds.size()) {
            qDebug() << "   Status: ✅ PASS - All" << pausedCount << "tasks paused";
        } else {
            qDebug() << "   Status: ❌ FAIL - Only" << pausedCount << "of" << taskIds.size() << "tasks paused";
        }
        
        // 清理
        for (const QString& taskId : taskIds) {
            m_manager->removeProgressItem(taskId);
        }
    }

private:
    ProgressManager* m_manager;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Debug Pause All Issue";
    qDebug() << "Qt Version:" << QT_VERSION_STR;
    
    PauseAllDebugger debugger;
    
    // 使用定时器延迟执行，确保事件循环启动
    QTimer::singleShot(100, &debugger, &PauseAllDebugger::runTests);
    
    // 设置超时退出
    QTimer::singleShot(10000, &app, &QApplication::quit);
    
    return app.exec();
}

#include "debug_pause_all.moc"
