#include <QApplication>
#include <QDebug>
#include <QTimer>
#include <QElapsedTimer>
#include <QEventLoop>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "=== Direct Simulate Progress Debug ===";
    
    // 创建 ProgressWidget
    ProgressWidget* widget = new ProgressWidget();
    widget->show();
    
    qDebug() << "1. ProgressWidget created and shown";
    
    // 等待初始化完成
    QEventLoop loop;
    QTimer::singleShot(1000, &loop, &QEventLoop::quit);
    loop.exec();
    
    qDebug() << "2. Initialization complete, testing simulate progress...";
    
    QElapsedTimer timer;
    timer.start();
    
    // 直接调用 simulateProgress
    qDebug() << "3. Calling simulateProgress()...";
    widget->simulateProgress();
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "4. simulateProgress() returned after" << elapsed << "ms";
    
    if (elapsed > 5000) {
        qDebug() << "❌ DEADLOCK DETECTED - simulateProgress took too long!";
        return 1;
    }
    
    qDebug() << "5. Waiting 2 seconds to observe behavior...";
    
    // 等待2秒观察行为
    QTimer::singleShot(2000, [&]() {
        qDebug() << "6. Stopping simulation...";
        widget->simulateProgress(); // 停止
        
        qDebug() << "7. Simulation stopped, waiting 1 second...";
        
        QTimer::singleShot(1000, [&]() {
            qDebug() << "8. Test completed successfully!";
            qDebug() << "✅ No deadlock detected in direct simulate progress test";
            app.quit();
        });
    });
    
    // 设置超时保护
    QTimer::singleShot(10000, [&]() {
        qDebug() << "❌ TIMEOUT - Test took too long, possible deadlock!";
        app.exit(1);
    });
    
    return app.exec();
}
