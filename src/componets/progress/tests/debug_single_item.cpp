#include <QApplication>
#include <QDebug>
#include <QTimer>
#include <QElapsedTimer>
#include <QEventLoop>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "=== Single Item Creation Debug ===";
    
    // 创建 ProgressManager
    ProgressManager* manager = ProgressManager::instance();
    qDebug() << "1. ProgressManager created";
    
    // 创建 ProgressWidget
    ProgressWidget* widget = new ProgressWidget();
    qDebug() << "2. ProgressWidget created";
    
    widget->show();
    qDebug() << "3. ProgressWidget shown";
    
    // 等待初始化完成
    QEventLoop loop;
    QTimer::singleShot(500, &loop, &QEventLoop::quit);
    loop.exec();
    qDebug() << "4. Initialization complete";
    
    // 测试创建单个进度项
    qDebug() << "5. Testing single item creation...";
    QElapsedTimer timer;
    timer.start();
    
    // 直接调用 ProgressWidget::addProgressItem
    QString itemId = widget->addProgressItem("Test Task", "Testing single item...", 100);
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "6. Single item creation completed in" << elapsed << "ms, itemId:" << itemId;
    
    if (elapsed > 2000) {
        qDebug() << "❌ DEADLOCK in single item creation!";
        return 1;
    }
    
    // 等待UI更新
    QTimer::singleShot(200, [&]() {
        qDebug() << "7. Testing second item creation...";
        
        QElapsedTimer timer2;
        timer2.start();
        
        QString itemId2 = widget->addProgressItem("Test Task 2", "Testing second item...", 50);
        
        qint64 elapsed2 = timer2.elapsed();
        qDebug() << "8. Second item creation completed in" << elapsed2 << "ms, itemId:" << itemId2;
        
        if (elapsed2 > 2000) {
            qDebug() << "❌ DEADLOCK in second item creation!";
            app.exit(1);
            return;
        }
        
        // 等待UI更新
        QTimer::singleShot(200, [&]() {
            qDebug() << "9. Testing third item creation...";
            
            QElapsedTimer timer3;
            timer3.start();
            
            QString itemId3 = widget->addProgressItem("Test Task 3", "Testing third item...", 75);
            
            qint64 elapsed3 = timer3.elapsed();
            qDebug() << "10. Third item creation completed in" << elapsed3 << "ms, itemId:" << itemId3;
            
            if (elapsed3 > 2000) {
                qDebug() << "❌ DEADLOCK in third item creation!";
                app.exit(1);
                return;
            }
            
            qDebug() << "11. All single item creations completed successfully!";
            qDebug() << "✅ No deadlock detected in single item creation";
            app.quit();
        });
    });
    
    // 设置超时保护
    QTimer::singleShot(10000, [&]() {
        qDebug() << "❌ TIMEOUT - Test took too long!";
        app.exit(1);
    });
    
    return app.exec();
}
