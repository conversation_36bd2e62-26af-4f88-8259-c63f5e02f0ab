/**
 * @file debug_add_task.cpp
 * @brief 调试 Add Task 死循环问题的专用程序
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QDebug>
#include <QElapsedTimer>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

class DebugWindow : public QMainWindow
{
    Q_OBJECT

public:
    DebugWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
    }

private slots:
    void testDirectCreate()
    {
        qDebug() << "=== Testing Direct Create ===";
        
        QElapsedTimer timer;
        timer.start();
        
        // 直接通过 ProgressManager 创建任务
        QString taskId = ProgressManager::instance()->createProgressItem(
            "Direct Test Task",
            "Testing direct creation",
            100
        );
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Direct creation took" << elapsed << "ms";
        qDebug() << "Task ID:" << taskId;
        
        m_resultLabel->setText(QString("Direct: %1ms - %2")
                              .arg(elapsed)
                              .arg(elapsed < 100 ? "✅ OK" : "❌ SLOW"));
    }
    
    void testManagerSignals()
    {
        qDebug() << "=== Testing Manager Signals ===";
        
        // 监听信号
        connect(ProgressManager::instance(), &ProgressManager::itemCreated,
                [](const QString& id) {
                    qDebug() << "Signal: itemCreated" << id;
                });
        
        connect(ProgressManager::instance(), &ProgressManager::itemUpdated,
                [](const QString& id) {
                    qDebug() << "Signal: itemUpdated" << id;
                });
        
        QElapsedTimer timer;
        timer.start();
        
        QString taskId = ProgressManager::instance()->createProgressItem(
            "Signal Test Task",
            "Testing signal emission",
            100
        );
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Creation with signals took" << elapsed << "ms";
        
        m_resultLabel->setText(QString("Signals: %1ms - %2")
                              .arg(elapsed)
                              .arg(elapsed < 100 ? "✅ OK" : "❌ SLOW"));
    }
    
    void testProgressWidget()
    {
        qDebug() << "=== Testing ProgressWidget Creation ===";
        
        QElapsedTimer timer;
        timer.start();
        
        // 这里可能会卡住
        qDebug() << "Creating ProgressWidget...";
        ProgressWidget* widget = new ProgressWidget();
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "ProgressWidget creation took" << elapsed << "ms";
        
        if (elapsed < 1000) {
            qDebug() << "Testing addProgressItem...";
            timer.restart();
            
            QString taskId = widget->addProgressItem("Widget Test", "Testing widget", 100);
            
            qint64 addElapsed = timer.elapsed();
            qDebug() << "Widget addProgressItem took" << addElapsed << "ms";
            
            m_resultLabel->setText(QString("Widget: create=%1ms, add=%2ms - %3")
                                  .arg(elapsed)
                                  .arg(addElapsed)
                                  .arg((elapsed < 1000 && addElapsed < 100) ? "✅ OK" : "❌ SLOW"));
        } else {
            m_resultLabel->setText(QString("Widget: %1ms - ❌ CREATION TOO SLOW").arg(elapsed));
        }
        
        widget->deleteLater();
    }

private:
    void setupUI()
    {
        setWindowTitle("Debug Add Task Issue");
        setMinimumSize(400, 300);
        
        QWidget *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout *layout = new QVBoxLayout(centralWidget);
        
        QPushButton *directBtn = new QPushButton("Test Direct Create", this);
        QPushButton *signalBtn = new QPushButton("Test Manager Signals", this);
        QPushButton *widgetBtn = new QPushButton("Test ProgressWidget", this);
        
        m_resultLabel = new QLabel("Ready to test...", this);
        m_resultLabel->setStyleSheet("QLabel { background-color: #f0f0f0; padding: 10px; }");
        
        layout->addWidget(directBtn);
        layout->addWidget(signalBtn);
        layout->addWidget(widgetBtn);
        layout->addWidget(m_resultLabel);
        layout->addStretch();
        
        connect(directBtn, &QPushButton::clicked, this, &DebugWindow::testDirectCreate);
        connect(signalBtn, &QPushButton::clicked, this, &DebugWindow::testManagerSignals);
        connect(widgetBtn, &QPushButton::clicked, this, &DebugWindow::testProgressWidget);
    }

private:
    QLabel *m_resultLabel;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Starting Debug Add Task Application...";
    
    DebugWindow window;
    window.show();
    
    return app.exec();
}

#include "debug_add_task.moc"
