#include <QApplication>
#include <QTest>
#include <QDebug>
#include <QElapsedTimer>
#include "ProgressManagerTest.h"

/**
 * @brief 测试运行器
 * 运行所有进度管理器相关的测试
 */
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Starting Progress Manager Test Suite...";
    qDebug() << "Qt Version:" << QT_VERSION_STR;
    
    QElapsedTimer totalTimer;
    totalTimer.start();
    
    int result = 0;
    
    // 运行 ProgressManagerTest
    {
        ProgressManagerTest test;
        result += QTest::qExec(&test, argc, argv);
    }
    
    qint64 totalTime = totalTimer.elapsed();
    qDebug() << "Total test time:" << totalTime << "ms";
    
    if (result == 0) {
        qDebug() << "All tests PASSED!";
    } else {
        qDebug() << "Some tests FAILED. Return code:" << result;
    }
    
    return result;
}
