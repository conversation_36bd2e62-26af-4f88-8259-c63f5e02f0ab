#include "ProgressManagerTest.h"
#include <QApplication>
#include <QThread>
#include <QRandomGenerator>
#include <QElapsedTimer>

ProgressManagerTest::ProgressManagerTest()
    : m_manager(nullptr)
    , m_itemCreatedSpy(nullptr)
    , m_itemRemovedSpy(nullptr)
    , m_itemUpdatedSpy(nullptr)
    , m_progressUpdatedSpy(nullptr)
    , m_statusChangedSpy(nullptr)
    , m_statisticsChangedSpy(nullptr)
{
    // 设置超时定时器
    m_timeoutTimer.setSingleShot(true);
    connect(&m_timeoutTimer, &QTimer::timeout, &m_eventLoop, &QEventLoop::quit);
}

void ProgressManagerTest::initTestCase()
{
    qDebug() << "=== ProgressManager Test Suite Started ===";
    
    // 确保有 QApplication 实例
    if (!QApplication::instance()) {
        int argc = 0;
        char* argv[] = {nullptr};
        new QApplication(argc, argv);
    }
}

void ProgressManagerTest::cleanupTestCase()
{
    qDebug() << "=== ProgressManager Test Suite Finished ===";
}

void ProgressManagerTest::init()
{
    // 每个测试前的初始化
    m_manager = ProgressManager::instance();
    QVERIFY(m_manager != nullptr);
    
    // 清理之前的数据
    m_manager->clearAllItems();
    m_testItemIds.clear();
    
    // 创建信号监听器
    m_itemCreatedSpy = new QSignalSpy(m_manager, &ProgressManager::itemCreated);
    m_itemRemovedSpy = new QSignalSpy(m_manager, &ProgressManager::itemRemoved);
    m_itemUpdatedSpy = new QSignalSpy(m_manager, &ProgressManager::itemUpdated);
    m_progressUpdatedSpy = new QSignalSpy(m_manager, &ProgressManager::progressUpdated);
    m_statusChangedSpy = new QSignalSpy(m_manager, &ProgressManager::statusChanged);
    m_statisticsChangedSpy = new QSignalSpy(m_manager, &ProgressManager::statisticsChanged);
    
    QVERIFY(m_itemCreatedSpy->isValid());
    QVERIFY(m_itemRemovedSpy->isValid());
    QVERIFY(m_itemUpdatedSpy->isValid());
    QVERIFY(m_progressUpdatedSpy->isValid());
    QVERIFY(m_statusChangedSpy->isValid());
    QVERIFY(m_statisticsChangedSpy->isValid());
}

void ProgressManagerTest::cleanup()
{
    // 每个测试后的清理
    delete m_itemCreatedSpy;
    delete m_itemRemovedSpy;
    delete m_itemUpdatedSpy;
    delete m_progressUpdatedSpy;
    delete m_statusChangedSpy;
    delete m_statisticsChangedSpy;
    
    m_itemCreatedSpy = nullptr;
    m_itemRemovedSpy = nullptr;
    m_itemUpdatedSpy = nullptr;
    m_progressUpdatedSpy = nullptr;
    m_statusChangedSpy = nullptr;
    m_statisticsChangedSpy = nullptr;
    
    // 清理测试数据
    if (m_manager) {
        m_manager->clearAllItems();
    }
    m_testItemIds.clear();
}

void ProgressManagerTest::testCreateProgressItem()
{
    qDebug() << "Testing createProgressItem...";
    
    // 测试创建基本进度项
    QString itemId = m_manager->createProgressItem("Test Task", "Initial message", 100);
    
    QVERIFY(!itemId.isEmpty());
    QCOMPARE(m_itemCreatedSpy->count(), 1);
    QCOMPARE(m_itemUpdatedSpy->count(), 1);
    
    // 验证项目属性
    ProgressItemPtr item = m_manager->getProgressItem(itemId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->title(), QString("Test Task"));
    QCOMPARE(item->message(), QString("Initial message"));
    QCOMPARE(item->total(), 100);
    QCOMPARE(item->current(), 0);
    QCOMPARE(item->status(), ProgressStatus::NotStarted);
    
    m_testItemIds.append(itemId);
}

void ProgressManagerTest::testCreateProgressItemWithParent()
{
    qDebug() << "Testing createProgressItem with parent...";
    
    // 创建父项目
    QString parentId = m_manager->createProgressItem("Parent Task", "Parent message", 100);
    QVERIFY(!parentId.isEmpty());
    
    // 重置信号计数器
    m_itemCreatedSpy->clear();
    m_itemUpdatedSpy->clear();
    
    // 创建子项目
    QString childId = m_manager->createProgressItem("Child Task", "Child message", 50, parentId);
    QVERIFY(!childId.isEmpty());
    QVERIFY(childId != parentId);
    
    // 验证信号
    QCOMPARE(m_itemCreatedSpy->count(), 1);
    QCOMPARE(m_itemUpdatedSpy->count(), 1);
    
    // 验证父子关系
    ProgressItemPtr parent = m_manager->getProgressItem(parentId);
    ProgressItemPtr child = m_manager->getProgressItem(childId);
    
    QVERIFY(parent != nullptr);
    QVERIFY(child != nullptr);
    QVERIFY(parent->isParent());
    QVERIFY(!child->isParent());
    QCOMPARE(parent->children().size(), 1);
    QCOMPARE(parent->children().first()->id(), childId);
    
    m_testItemIds.append(parentId);
    m_testItemIds.append(childId);
}

void ProgressManagerTest::testInfiniteLoopDetection()
{
    qDebug() << "Testing infinite loop detection...";
    
    // 这个测试专门检测添加任务时的死循环问题
    QElapsedTimer timer;
    timer.start();
    
    // 创建一个简单的任务，监控是否会卡死
    QString itemId = m_manager->createProgressItem("Loop Test Task", "Testing for loops", 100);
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Task creation took" << elapsed << "ms";
    
    // 如果超过1秒，说明可能有死循环
    QVERIFY2(elapsed < 1000, QString("Task creation took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    QVERIFY(!itemId.isEmpty());
    
    // 测试更新进度是否会导致死循环
    timer.restart();
    m_manager->updateProgress(itemId, 50, "Halfway done");
    elapsed = timer.elapsed();
    
    qDebug() << "Progress update took" << elapsed << "ms";
    QVERIFY2(elapsed < 100, QString("Progress update took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    m_testItemIds.append(itemId);
}

void ProgressManagerTest::testSignalLoops()
{
    qDebug() << "Testing signal loops...";
    
    // 创建任务并监控信号发射
    QElapsedTimer timer;
    timer.start();
    
    QString itemId = m_manager->createProgressItem("Signal Test", "Testing signals", 100);
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Signal emission took" << elapsed << "ms";
    
    // 检查信号数量是否合理
    int totalSignals = m_itemCreatedSpy->count() + m_itemUpdatedSpy->count() + 
                      m_progressUpdatedSpy->count() + m_statusChangedSpy->count();
    
    qDebug() << "Total signals emitted:" << totalSignals;
    qDebug() << "itemCreated:" << m_itemCreatedSpy->count();
    qDebug() << "itemUpdated:" << m_itemUpdatedSpy->count();
    qDebug() << "progressUpdated:" << m_progressUpdatedSpy->count();
    qDebug() << "statusChanged:" << m_statusChangedSpy->count();
    
    // 信号数量应该是合理的（不应该有数百个信号）
    QVERIFY2(totalSignals < 10, QString("Too many signals emitted: %1").arg(totalSignals).toLocal8Bit());
    QVERIFY2(elapsed < 100, QString("Signal processing took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    m_testItemIds.append(itemId);
}

void ProgressManagerTest::testRecursiveParentChild()
{
    qDebug() << "Testing recursive parent-child operations...";
    
    // 创建父项目
    QString parentId = m_manager->createProgressItem("Parent", "Parent task", 100);
    
    // 创建多个子项目
    QStringList childIds;
    for (int i = 0; i < 3; ++i) {
        QString childId = m_manager->createProgressItem(
            QString("Child %1").arg(i), 
            QString("Child task %1").arg(i), 
            50, 
            parentId
        );
        childIds.append(childId);
    }
    
    // 测试父项目进度计算是否会导致死循环
    QElapsedTimer timer;
    timer.start();
    
    // 更新子项目进度
    for (const QString& childId : childIds) {
        m_manager->updateProgress(childId, 25, "Progress update");
    }
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Parent progress calculation took" << elapsed << "ms";
    
    QVERIFY2(elapsed < 500, QString("Parent progress calculation took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    // 验证父项目状态
    ProgressItemPtr parent = m_manager->getProgressItem(parentId);
    QVERIFY(parent != nullptr);
    
    m_testItemIds.append(parentId);
    m_testItemIds.append(childIds);
}

void ProgressManagerTest::waitForSignals(int timeoutMs)
{
    m_timeoutTimer.start(timeoutMs);
    m_eventLoop.exec();
    m_timeoutTimer.stop();
}

QString ProgressManagerTest::createTestItem(const QString& title, int total)
{
    QString itemId = m_manager->createProgressItem(title, "Test message", total);
    m_testItemIds.append(itemId);
    return itemId;
}

void ProgressManagerTest::verifyItemState(const QString& itemId, ProgressStatus expectedStatus)
{
    ProgressItemPtr item = m_manager->getProgressItem(itemId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->status(), expectedStatus);
}

void ProgressManagerTest::simulateUserInteraction()
{
    // 模拟用户快速点击创建任务的场景
    QElapsedTimer timer;
    timer.start();
    
    QStringList rapidIds;
    for (int i = 0; i < 5; ++i) {
        QString id = m_manager->createProgressItem(
            QString("Rapid Task %1").arg(i), 
            "Rapid creation test", 
            100
        );
        rapidIds.append(id);
        
        // 模拟快速操作
        m_manager->startProgress(id);
        m_manager->updateProgress(id, 10, "Quick update");
    }
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Rapid task creation took" << elapsed << "ms";
    
    QVERIFY2(elapsed < 1000, QString("Rapid operations took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    m_testItemIds.append(rapidIds);
}

void ProgressManagerTest::testRemoveProgressItem()
{
    qDebug() << "Testing removeProgressItem...";

    QString itemId = createTestItem("Remove Test");
    QVERIFY(m_manager->getProgressItem(itemId) != nullptr);

    m_itemRemovedSpy->clear();
    bool result = m_manager->removeProgressItem(itemId);

    QVERIFY(result);
    QCOMPARE(m_itemRemovedSpy->count(), 1);
    QVERIFY(m_manager->getProgressItem(itemId) == nullptr);
}

void ProgressManagerTest::testUpdateProgress()
{
    qDebug() << "Testing updateProgress...";

    QString itemId = createTestItem("Update Test");
    m_progressUpdatedSpy->clear();

    m_manager->updateProgress(itemId, 50, "Half done");

    QCOMPARE(m_progressUpdatedSpy->count(), 1);

    ProgressItemPtr item = m_manager->getProgressItem(itemId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->current(), 50);
    QCOMPARE(item->message(), QString("Half done"));
}

void ProgressManagerTest::testStatusChanges()
{
    qDebug() << "Testing status changes...";

    QString itemId = createTestItem("Status Test");
    m_statusChangedSpy->clear();

    m_manager->startProgress(itemId);
    QCOMPARE(m_statusChangedSpy->count(), 1);
    verifyItemState(itemId, ProgressStatus::Running);

    m_manager->pauseProgress(itemId);
    QCOMPARE(m_statusChangedSpy->count(), 2);
    verifyItemState(itemId, ProgressStatus::Paused);

    m_manager->resumeProgress(itemId);
    QCOMPARE(m_statusChangedSpy->count(), 3);
    verifyItemState(itemId, ProgressStatus::Running);

    m_manager->completeProgress(itemId);
    QCOMPARE(m_statusChangedSpy->count(), 4);
    verifyItemState(itemId, ProgressStatus::Completed);
}

void ProgressManagerTest::testParentChildRelationship()
{
    qDebug() << "Testing parent-child relationship...";

    QString parentId = createTestItem("Parent Test");
    QString childId = m_manager->createProgressItem("Child Test", "Child message", 50, parentId);

    ProgressItemPtr parent = m_manager->getProgressItem(parentId);
    ProgressItemPtr child = m_manager->getProgressItem(childId);

    QVERIFY(parent->isParent());
    QVERIFY(!child->isParent());
    QCOMPARE(parent->children().size(), 1);
    QCOMPARE(parent->children().first()->id(), childId);
}

void ProgressManagerTest::testParentProgressCalculation()
{
    qDebug() << "Testing parent progress calculation...";

    QString parentId = createTestItem("Parent Calc Test");
    QString child1 = m_manager->createProgressItem("Child 1", "Child 1", 100, parentId);
    QString child2 = m_manager->createProgressItem("Child 2", "Child 2", 100, parentId);

    // 更新子项目进度
    m_manager->updateProgress(child1, 50);
    m_manager->updateProgress(child2, 100);

    ProgressItemPtr parent = m_manager->getProgressItem(parentId);
    QVERIFY(parent != nullptr);

    // 父项目进度应该是子项目的平均值
    double expectedProgress = (50.0 + 100.0) / 2.0;
    QCOMPARE(parent->progressPercentage(), expectedProgress);
}

void ProgressManagerTest::testRemoveParentWithChildren()
{
    qDebug() << "Testing remove parent with children...";

    QString parentId = createTestItem("Parent Remove Test");
    QString childId = m_manager->createProgressItem("Child Remove Test", "Child", 50, parentId);

    m_itemRemovedSpy->clear();
    bool result = m_manager->removeProgressItem(parentId);

    QVERIFY(result);
    // 应该移除父项目和子项目
    QVERIFY(m_itemRemovedSpy->count() >= 2);
    QVERIFY(m_manager->getProgressItem(parentId) == nullptr);
    QVERIFY(m_manager->getProgressItem(childId) == nullptr);
}

void ProgressManagerTest::testCircularReferenceProtection()
{
    qDebug() << "Testing circular reference protection...";

    QString item1 = createTestItem("Item 1");
    QString item2 = m_manager->createProgressItem("Item 2", "Item 2", 100, item1);

    // 尝试创建循环引用（应该被阻止）
    QString item3 = m_manager->createProgressItem("Item 3", "Item 3", 100, item2);

    // 验证没有循环引用
    ProgressItemPtr ptr1 = m_manager->getProgressItem(item1);
    ProgressItemPtr ptr2 = m_manager->getProgressItem(item2);
    ProgressItemPtr ptr3 = m_manager->getProgressItem(item3);

    QVERIFY(ptr1 != nullptr);
    QVERIFY(ptr2 != nullptr);
    QVERIFY(ptr3 != nullptr);

    // 验证层级结构正确
    QVERIFY(ptr1->isParent());
    QVERIFY(ptr2->isParent());
    QVERIFY(!ptr3->isParent());
}

void ProgressManagerTest::testSignalEmission()
{
    qDebug() << "Testing signal emission...";

    QString itemId = createTestItem("Signal Test");

    // 验证创建信号
    QVERIFY(m_itemCreatedSpy->count() > 0);

    // 测试更新信号
    m_progressUpdatedSpy->clear();
    m_manager->updateProgress(itemId, 25);
    QCOMPARE(m_progressUpdatedSpy->count(), 1);

    // 测试状态变化信号
    m_statusChangedSpy->clear();
    m_manager->startProgress(itemId);
    QCOMPARE(m_statusChangedSpy->count(), 1);
}

void ProgressManagerTest::testStatisticsSignals()
{
    qDebug() << "Testing statistics signals...";

    m_statisticsChangedSpy->clear();

    QString itemId = createTestItem("Stats Test");

    // 统计信息应该更新
    QVERIFY(m_statisticsChangedSpy->count() > 0);

    // 验证统计数据
    QVERIFY(m_manager->getTotalCount() > 0);
}

void ProgressManagerTest::testThreadSafety()
{
    qDebug() << "Testing thread safety...";

    // 简单的线程安全测试
    QStringList ids;
    for (int i = 0; i < 10; ++i) {
        QString id = createTestItem(QString("Thread Test %1").arg(i));
        ids.append(id);
    }

    // 并发更新
    for (const QString& id : ids) {
        m_manager->updateProgress(id, 50);
        m_manager->startProgress(id);
    }

    // 验证所有项目都存在
    for (const QString& id : ids) {
        QVERIFY(m_manager->getProgressItem(id) != nullptr);
    }
}

void ProgressManagerTest::testConcurrentAccess()
{
    qDebug() << "Testing concurrent access...";

    QString itemId = createTestItem("Concurrent Test");

    // 快速连续操作
    for (int i = 0; i < 100; ++i) {
        m_manager->updateProgress(itemId, i % 100);
    }

    ProgressItemPtr item = m_manager->getProgressItem(itemId);
    QVERIFY(item != nullptr);
}

void ProgressManagerTest::testInvalidOperations()
{
    qDebug() << "Testing invalid operations...";

    // 测试无效ID操作
    bool result = m_manager->removeProgressItem("invalid-id");
    QVERIFY(!result);

    // 测试更新不存在的项目
    m_manager->updateProgress("invalid-id", 50);
    // 应该不会崩溃

    // 测试获取不存在的项目
    ProgressItemPtr item = m_manager->getProgressItem("invalid-id");
    QVERIFY(item == nullptr);
}

void ProgressManagerTest::testEmptyStrings()
{
    qDebug() << "Testing empty strings...";

    QString itemId = m_manager->createProgressItem("", "", 100);
    QVERIFY(!itemId.isEmpty());

    ProgressItemPtr item = m_manager->getProgressItem(itemId);
    QVERIFY(item != nullptr);

    m_testItemIds.append(itemId);
}

void ProgressManagerTest::testNegativeValues()
{
    qDebug() << "Testing negative values...";

    QString itemId = createTestItem("Negative Test");

    // 测试负数进度
    m_manager->updateProgress(itemId, -10);

    ProgressItemPtr item = m_manager->getProgressItem(itemId);
    QVERIFY(item != nullptr);
    QVERIFY(item->current() >= 0); // 应该被限制为非负数
}

void ProgressManagerTest::testLargeNumbers()
{
    qDebug() << "Testing large numbers...";

    QString itemId = m_manager->createProgressItem("Large Test", "Large numbers", 1000000);

    m_manager->updateProgress(itemId, 500000);

    ProgressItemPtr item = m_manager->getProgressItem(itemId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->total(), 1000000);
    QCOMPARE(item->current(), 500000);

    m_testItemIds.append(itemId);
}

void ProgressManagerTest::testPerformanceWithManyItems()
{
    qDebug() << "Testing performance with many items...";

    QElapsedTimer timer;
    timer.start();

    QStringList manyIds;
    for (int i = 0; i < 100; ++i) {
        QString id = m_manager->createProgressItem(
            QString("Perf Test %1").arg(i),
            "Performance test",
            100
        );
        manyIds.append(id);
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "Creating 100 items took" << elapsed << "ms";

    QVERIFY2(elapsed < 1000, QString("Creating items took too long: %1ms").arg(elapsed).toLocal8Bit());

    m_testItemIds.append(manyIds);
}

void ProgressManagerTest::testMemoryLeaks()
{
    qDebug() << "Testing memory leaks...";

    // 创建和删除大量项目
    for (int i = 0; i < 50; ++i) {
        QString id = m_manager->createProgressItem(
            QString("Memory Test %1").arg(i),
            "Memory test",
            100
        );
        m_manager->removeProgressItem(id);
    }

    // 验证管理器状态
    QCOMPARE(m_manager->getTotalCount(), 0);
}

// Add Task 功能测试
void ProgressManagerTest::testAddTaskDialog()
{
    qDebug() << "Testing Add Task dialog functionality...";

    // 模拟通过对话框添加任务的过程
    QElapsedTimer timer;
    timer.start();

    // 模拟用户输入
    QString title = "Dialog Test Task";
    QString message = "Task created through dialog";
    int total = 100;

    // 创建任务（模拟对话框确认后的操作）
    QString taskId = m_manager->createProgressItem(title, message, total);

    qint64 elapsed = timer.elapsed();
    qDebug() << "Dialog task creation took" << elapsed << "ms";

    // 验证任务创建成功
    QVERIFY(!taskId.isEmpty());
    QVERIFY2(elapsed < 100, QString("Dialog task creation took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证任务属性
    ProgressItemPtr item = m_manager->getProgressItem(taskId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->title(), title);
    QCOMPARE(item->message(), message);
    QCOMPARE(item->total(), total);

    m_testItemIds.append(taskId);
}

void ProgressManagerTest::testAddTaskPerformance()
{
    qDebug() << "Testing Add Task performance...";

    QElapsedTimer timer;
    timer.start();

    // 快速连续添加多个任务（模拟用户快速点击）
    QStringList taskIds;
    for (int i = 0; i < 10; ++i) {
        QString taskId = m_manager->createProgressItem(
            QString("Performance Test %1").arg(i),
            QString("Performance test task %1").arg(i),
            100
        );
        taskIds.append(taskId);
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "10 rapid task creations took" << elapsed << "ms";

    // 验证性能
    QVERIFY2(elapsed < 500, QString("Rapid task creation took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证所有任务都创建成功
    QCOMPARE(taskIds.size(), 10);
    for (const QString& taskId : taskIds) {
        QVERIFY(!taskId.isEmpty());
        QVERIFY(m_manager->getProgressItem(taskId) != nullptr);
    }

    m_testItemIds.append(taskIds);
}

void ProgressManagerTest::testAddTaskSignalLoop()
{
    qDebug() << "Testing Add Task signal loop prevention...";

    // 监控信号发射数量
    m_itemCreatedSpy->clear();
    m_itemUpdatedSpy->clear();
    m_progressUpdatedSpy->clear();
    m_statusChangedSpy->clear();

    QElapsedTimer timer;
    timer.start();

    // 创建任务
    QString taskId = m_manager->createProgressItem("Signal Loop Test", "Testing signal loops", 100);

    qint64 elapsed = timer.elapsed();
    qDebug() << "Task creation with signal monitoring took" << elapsed << "ms";

    // 检查信号数量
    int totalSignals = m_itemCreatedSpy->count() + m_itemUpdatedSpy->count() +
                      m_progressUpdatedSpy->count() + m_statusChangedSpy->count();

    qDebug() << "Total signals emitted:" << totalSignals;
    qDebug() << "itemCreated:" << m_itemCreatedSpy->count();
    qDebug() << "itemUpdated:" << m_itemUpdatedSpy->count();
    qDebug() << "progressUpdated:" << m_progressUpdatedSpy->count();
    qDebug() << "statusChanged:" << m_statusChangedSpy->count();

    // 验证没有信号循环
    QVERIFY2(elapsed < 100, QString("Task creation took too long (possible signal loop): %1ms").arg(elapsed).toLocal8Bit());
    QVERIFY2(totalSignals < 20, QString("Too many signals emitted (possible signal loop): %1").arg(totalSignals).toLocal8Bit());

    // 验证基本信号正常发射
    QVERIFY(m_itemCreatedSpy->count() >= 1);
    QVERIFY(m_itemUpdatedSpy->count() >= 1);

    m_testItemIds.append(taskId);
}

void ProgressManagerTest::testProgressWidgetAddTask()
{
    qDebug() << "Testing ProgressWidget Add Task functionality...";

    // 不创建完整的 ProgressWidget，直接测试 addProgressItem 逻辑
    QElapsedTimer timer;
    timer.start();

    // 直接通过管理器创建任务（模拟 ProgressWidget::addProgressItem 的核心逻辑）
    QString taskId = m_manager->createProgressItem("Widget Test Task", "Task added through widget", 100);

    qint64 elapsed = timer.elapsed();
    qDebug() << "ProgressWidget-style task creation took" << elapsed << "ms";

    // 验证任务创建
    QVERIFY(!taskId.isEmpty());
    QVERIFY2(elapsed < 100, QString("ProgressWidget-style task creation took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证任务在管理器中存在
    ProgressItemPtr item = m_manager->getProgressItem(taskId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->title(), QString("Widget Test Task"));

    m_testItemIds.append(taskId);
}

void ProgressManagerTest::testUIRefreshLoop()
{
    qDebug() << "Testing UI refresh loop prevention...";

    // 这个测试专门检测 UI 刷新时的死循环问题
    QElapsedTimer timer;
    timer.start();

    // 创建任务并立即更新（模拟 UI 刷新场景）
    QString taskId = m_manager->createProgressItem("UI Refresh Test", "Testing UI refresh", 100);

    // 快速连续更新进度（模拟 UI 刷新触发的更新）
    for (int i = 0; i < 5; ++i) {
        m_manager->updateProgress(taskId, i * 20, QString("Update %1").arg(i));
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "UI refresh simulation took" << elapsed << "ms";

    // 验证没有死循环
    QVERIFY2(elapsed < 300, QString("UI refresh took too long (possible loop): %1ms").arg(elapsed).toLocal8Bit());

    // 验证最终状态正确
    ProgressItemPtr item = m_manager->getProgressItem(taskId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->current(), 80); // 最后一次更新的值

    // 额外测试：模拟 ProgressItemWidget 的 setShowChildren 调用
    // 这是之前死循环的主要源头
    timer.restart();

    // 创建父子任务
    QString parentId = m_manager->createProgressItem("Parent Task", "Parent task", 100);
    QString childId = m_manager->createProgressItem("Child Task", "Child task", 50, parentId);

    // 模拟展开/折叠操作（这之前会导致死循环）
    ProgressItemPtr parentItem = m_manager->getProgressItem(parentId);
    QVERIFY(parentItem != nullptr);
    QVERIFY(parentItem->isParent());

    elapsed = timer.elapsed();
    qDebug() << "Parent-child creation and validation took" << elapsed << "ms";

    // 验证没有死循环
    QVERIFY2(elapsed < 100, QString("Parent-child operations took too long: %1ms").arg(elapsed).toLocal8Bit());

    m_testItemIds.append(taskId);
    m_testItemIds.append(parentId);
    m_testItemIds.append(childId);
}

// Pause All 功能测试
void ProgressManagerTest::testPauseAllBasic()
{
    qDebug() << "Testing basic Pause All functionality...";

    // 创建多个运行中的任务
    QStringList taskIds;
    for (int i = 0; i < 3; ++i) {
        QString taskId = m_manager->createProgressItem(
            QString("Pause Test Task %1").arg(i),
            "Testing pause all",
            100
        );
        m_manager->startProgress(taskId);
        taskIds.append(taskId);
    }

    QElapsedTimer timer;
    timer.start();

    // 执行 Pause All
    m_manager->pauseAllItems();

    qint64 elapsed = timer.elapsed();
    qDebug() << "Pause All took" << elapsed << "ms";

    // 验证所有任务都被暂停
    for (const QString& taskId : taskIds) {
        ProgressItemPtr item = m_manager->getProgressItem(taskId);
        QVERIFY(item != nullptr);
        QCOMPARE(item->status(), ProgressStatus::Paused);
    }

    // 验证性能
    QVERIFY2(elapsed < 200, QString("Pause All took too long: %1ms").arg(elapsed).toLocal8Bit());

    m_testItemIds.append(taskIds);
}

void ProgressManagerTest::testPauseAllPerformance()
{
    qDebug() << "Testing Pause All performance with many items...";

    // 创建大量运行中的任务
    QStringList taskIds;
    for (int i = 0; i < 20; ++i) {
        QString taskId = m_manager->createProgressItem(
            QString("Performance Pause Test %1").arg(i),
            "Testing pause all performance",
            100
        );
        m_manager->startProgress(taskId);
        taskIds.append(taskId);
    }

    QElapsedTimer timer;
    timer.start();

    // 执行 Pause All
    m_manager->pauseAllItems();

    qint64 elapsed = timer.elapsed();
    qDebug() << "Pause All for 20 items took" << elapsed << "ms";

    // 验证性能
    QVERIFY2(elapsed < 500, QString("Pause All performance too slow: %1ms").arg(elapsed).toLocal8Bit());

    // 验证所有任务都被暂停
    int pausedCount = 0;
    for (const QString& taskId : taskIds) {
        ProgressItemPtr item = m_manager->getProgressItem(taskId);
        if (item && item->status() == ProgressStatus::Paused) {
            pausedCount++;
        }
    }

    QCOMPARE(pausedCount, 20);

    m_testItemIds.append(taskIds);
}

void ProgressManagerTest::testPauseAllDeadlock()
{
    qDebug() << "Testing Pause All deadlock prevention...";

    // 监控信号发射
    m_itemUpdatedSpy->clear();
    m_statusChangedSpy->clear();

    // 创建任务并启动
    QString taskId = m_manager->createProgressItem("Deadlock Test", "Testing deadlock", 100);
    m_manager->startProgress(taskId);

    QElapsedTimer timer;
    timer.start();

    // 执行 Pause All（之前会导致死锁）
    m_manager->pauseAllItems();

    qint64 elapsed = timer.elapsed();
    qDebug() << "Pause All with signal monitoring took" << elapsed << "ms";

    // 检查信号数量
    int totalSignals = m_itemUpdatedSpy->count() + m_statusChangedSpy->count();
    qDebug() << "Total signals emitted:" << totalSignals;
    qDebug() << "itemUpdated:" << m_itemUpdatedSpy->count();
    qDebug() << "statusChanged:" << m_statusChangedSpy->count();

    // 验证没有死锁
    QVERIFY2(elapsed < 200, QString("Pause All took too long (possible deadlock): %1ms").arg(elapsed).toLocal8Bit());

    // 验证任务状态
    ProgressItemPtr item = m_manager->getProgressItem(taskId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->status(), ProgressStatus::Paused);

    m_testItemIds.append(taskId);
}

void ProgressManagerTest::testBatchOperationsDeadlock()
{
    qDebug() << "Testing batch operations deadlock prevention...";

    // 创建多个任务
    QStringList taskIds;
    for (int i = 0; i < 5; ++i) {
        QString taskId = m_manager->createProgressItem(
            QString("Batch Test %1").arg(i),
            "Testing batch operations",
            100
        );
        taskIds.append(taskId);
    }

    QElapsedTimer timer;
    timer.start();

    // 快速连续执行批量操作
    m_manager->startAllItems();
    m_manager->pauseAllItems();
    m_manager->startAllItems();
    m_manager->cancelAllItems();

    qint64 elapsed = timer.elapsed();
    qDebug() << "Batch operations took" << elapsed << "ms";

    // 验证没有死锁
    QVERIFY2(elapsed < 500, QString("Batch operations took too long: %1ms").arg(elapsed).toLocal8Bit());

    m_testItemIds.append(taskIds);
}

void ProgressManagerTest::testStartPauseAllCycle()
{
    qDebug() << "Testing Start/Pause All cycle...";

    // 创建任务
    QStringList taskIds;
    for (int i = 0; i < 3; ++i) {
        QString taskId = m_manager->createProgressItem(
            QString("Cycle Test %1").arg(i),
            "Testing start/pause cycle",
            100
        );
        taskIds.append(taskId);
    }

    QElapsedTimer timer;
    timer.start();

    // 执行多轮启动/暂停循环
    for (int cycle = 0; cycle < 3; ++cycle) {
        m_manager->startAllItems();
        m_manager->pauseAllItems();
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "3 start/pause cycles took" << elapsed << "ms";

    // 验证性能
    QVERIFY2(elapsed < 300, QString("Start/Pause cycles took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证最终状态
    for (const QString& taskId : taskIds) {
        ProgressItemPtr item = m_manager->getProgressItem(taskId);
        QVERIFY(item != nullptr);
        QCOMPARE(item->status(), ProgressStatus::Paused);
    }

    m_testItemIds.append(taskIds);
}

// Simulate Progress 功能测试
void ProgressManagerTest::testSimulateProgressBasic()
{
    qDebug() << "Testing basic Simulate Progress functionality...";

    // 创建任务并启动
    QString taskId = m_manager->createProgressItem("Simulate Test", "Testing simulation", 100);
    m_manager->startProgress(taskId);

    QElapsedTimer timer;
    timer.start();

    // 模拟进度更新
    for (int i = 10; i <= 100; i += 10) {
        m_manager->updateProgress(taskId, i, QString("Progress: %1%").arg(i));
        if (i == 100) {
            m_manager->completeProgress(taskId);
        }
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "Simulate progress took" << elapsed << "ms";

    // 验证最终状态
    ProgressItemPtr item = m_manager->getProgressItem(taskId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->status(), ProgressStatus::Completed);
    QCOMPARE(item->current(), 100);

    // 验证性能
    QVERIFY2(elapsed < 100, QString("Simulate progress took too long: %1ms").arg(elapsed).toLocal8Bit());

    m_testItemIds.append(taskId);
}

void ProgressManagerTest::testSimulateProgressDeadlock()
{
    qDebug() << "Testing Simulate Progress deadlock prevention...";

    // 监控信号发射
    m_itemUpdatedSpy->clear();
    m_progressUpdatedSpy->clear();

    // 创建任务并启动
    QString taskId = m_manager->createProgressItem("Deadlock Simulate Test", "Testing deadlock", 100);
    m_manager->startProgress(taskId);

    QElapsedTimer timer;
    timer.start();

    // 快速连续进度更新（之前可能导致死锁）
    for (int i = 1; i <= 50; i++) {
        m_manager->updateProgress(taskId, i * 2, QString("Step %1").arg(i));
    }
    m_manager->completeProgress(taskId);

    qint64 elapsed = timer.elapsed();
    qDebug() << "Rapid progress updates took" << elapsed << "ms";

    // 检查信号数量
    int totalSignals = m_itemUpdatedSpy->count() + m_progressUpdatedSpy->count();
    qDebug() << "Total signals emitted:" << totalSignals;
    qDebug() << "itemUpdated:" << m_itemUpdatedSpy->count();
    qDebug() << "progressUpdated:" << m_progressUpdatedSpy->count();

    // 验证没有死锁
    QVERIFY2(elapsed < 200, QString("Progress updates took too long (possible deadlock): %1ms").arg(elapsed).toLocal8Bit());

    // 验证最终状态
    ProgressItemPtr item = m_manager->getProgressItem(taskId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->status(), ProgressStatus::Completed);

    m_testItemIds.append(taskId);
}

void ProgressManagerTest::testUpdateProgressDeadlock()
{
    qDebug() << "Testing updateProgress deadlock prevention...";

    // 创建任务
    QString taskId = m_manager->createProgressItem("Update Test", "Testing update", 100);
    m_manager->startProgress(taskId);

    QElapsedTimer timer;
    timer.start();

    // 快速连续调用 updateProgress
    for (int i = 0; i < 20; i++) {
        m_manager->updateProgress(taskId, i * 5, QString("Update %1").arg(i));
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "20 updateProgress calls took" << elapsed << "ms";

    // 验证性能
    QVERIFY2(elapsed < 100, QString("updateProgress calls took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证状态
    ProgressItemPtr item = m_manager->getProgressItem(taskId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->current(), 95); // 最后一次更新是 19 * 5 = 95

    m_testItemIds.append(taskId);
}

void ProgressManagerTest::testCompleteProgressDeadlock()
{
    qDebug() << "Testing completeProgress deadlock prevention...";

    // 创建多个任务
    QStringList taskIds;
    for (int i = 0; i < 5; i++) {
        QString taskId = m_manager->createProgressItem(
            QString("Complete Test %1").arg(i),
            "Testing completion",
            100
        );
        m_manager->startProgress(taskId);
        taskIds.append(taskId);
    }

    QElapsedTimer timer;
    timer.start();

    // 快速连续完成所有任务
    for (const QString& taskId : taskIds) {
        m_manager->updateProgress(taskId, 100);
        m_manager->completeProgress(taskId);
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "5 completeProgress calls took" << elapsed << "ms";

    // 验证性能
    QVERIFY2(elapsed < 100, QString("completeProgress calls took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证所有任务都完成
    for (const QString& taskId : taskIds) {
        ProgressItemPtr item = m_manager->getProgressItem(taskId);
        QVERIFY(item != nullptr);
        QCOMPARE(item->status(), ProgressStatus::Completed);
    }

    m_testItemIds.append(taskIds);
}

void ProgressManagerTest::testProgressSimulationCycle()
{
    qDebug() << "Testing progress simulation cycle...";

    // 创建任务
    QString taskId = m_manager->createProgressItem("Cycle Test", "Testing simulation cycle", 100);
    m_manager->startProgress(taskId);

    QElapsedTimer timer;
    timer.start();

    // 模拟完整的进度循环
    for (int cycle = 0; cycle < 3; cycle++) {
        // 重置进度
        m_manager->updateProgress(taskId, 0, "Starting cycle");

        // 逐步更新进度
        for (int progress = 10; progress <= 100; progress += 10) {
            m_manager->updateProgress(taskId, progress, QString("Cycle %1: %2%").arg(cycle + 1).arg(progress));
        }

        if (cycle < 2) {
            // 重新启动（除了最后一次）
            m_manager->startProgress(taskId);
        } else {
            // 最后一次完成
            m_manager->completeProgress(taskId);
        }
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "3 simulation cycles took" << elapsed << "ms";

    // 验证性能
    QVERIFY2(elapsed < 200, QString("Simulation cycles took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证最终状态
    ProgressItemPtr item = m_manager->getProgressItem(taskId);
    QVERIFY(item != nullptr);
    QCOMPARE(item->status(), ProgressStatus::Completed);

    m_testItemIds.append(taskId);
}

// 空任务情况测试
void ProgressManagerTest::testEmptyTaskListDeadlock()
{
    qDebug() << "Testing empty task list deadlock prevention...";

    // 确保管理器为空
    m_manager->clearAllItems();

    QElapsedTimer timer;
    timer.start();

    // 在空任务列表上执行各种操作
    m_manager->startAllItems();
    m_manager->pauseAllItems();
    m_manager->cancelAllItems();
    m_manager->clearCompletedItems();
    m_manager->clearErrorItems();

    qint64 elapsed = timer.elapsed();
    qDebug() << "Empty task list operations took" << elapsed << "ms";

    // 验证没有死锁
    QVERIFY2(elapsed < 100, QString("Empty operations took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证管理器为空
    QList<ProgressItemPtr> allItems = m_manager->getAllItems();
    QCOMPARE(allItems.size(), 0);
}

void ProgressManagerTest::testSimulateProgressWithNoTasks()
{
    qDebug() << "Testing simulate progress with no tasks...";

    // 确保管理器为空
    m_manager->clearAllItems();

    QElapsedTimer timer;
    timer.start();

    // 尝试在空列表上模拟进度（这应该不会导致死锁）
    QStringList emptyList;
    for (int i = 0; i < 10; i++) {
        // 模拟 ProgressWidget::onSimulationTimer 的逻辑
        for (const QString& itemId : emptyList) {
            ProgressItemPtr item = m_manager->getProgressItem(itemId);
            if (item && item->status() == ProgressStatus::Running) {
                // 这个分支不应该执行，因为列表为空
                QFAIL("Should not reach here with empty list");
            }
        }
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "Empty simulation took" << elapsed << "ms";

    // 验证没有死锁
    QVERIFY2(elapsed < 50, QString("Empty simulation took too long: %1ms").arg(elapsed).toLocal8Bit());
}

void ProgressManagerTest::testBatchOperationsWithNoTasks()
{
    qDebug() << "Testing batch operations with no tasks...";

    // 确保管理器为空
    m_manager->clearAllItems();

    // 监控信号发射
    m_itemUpdatedSpy->clear();
    m_statusChangedSpy->clear();

    QElapsedTimer timer;
    timer.start();

    // 快速连续执行批量操作（空列表）
    for (int i = 0; i < 5; i++) {
        m_manager->startAllItems();
        m_manager->pauseAllItems();
        m_manager->cancelAllItems();
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "5 batch operation cycles on empty list took" << elapsed << "ms";

    // 检查信号数量（应该为0，因为没有任务）
    int totalSignals = m_itemUpdatedSpy->count() + m_statusChangedSpy->count();
    qDebug() << "Total signals emitted:" << totalSignals;

    // 验证没有死锁
    QVERIFY2(elapsed < 100, QString("Empty batch operations took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证没有信号发射（因为没有任务）
    QCOMPARE(totalSignals, 0);
}

void ProgressManagerTest::testEmptyManagerOperations()
{
    qDebug() << "Testing various operations on empty manager...";

    // 确保管理器为空
    m_manager->clearAllItems();

    QElapsedTimer timer;
    timer.start();

    // 尝试各种可能导致死锁的操作

    // 1. 获取不存在的项目
    ProgressItemPtr item = m_manager->getProgressItem("non-existent-id");
    QVERIFY(item == nullptr);

    // 2. 更新不存在的项目
    m_manager->updateProgress("non-existent-id", 50, "test");
    m_manager->completeProgress("non-existent-id");
    m_manager->startProgress("non-existent-id");

    // 3. 删除不存在的项目
    bool removed = m_manager->removeProgressItem("non-existent-id");
    QVERIFY(!removed);

    // 4. 获取所有项目（应该为空）
    QList<ProgressItemPtr> allItems = m_manager->getAllItems();
    QVERIFY(allItems.isEmpty());

    // 5. 获取根项目（应该为空）
    QList<ProgressItemPtr> rootItems = m_manager->getRootItems();
    QVERIFY(rootItems.isEmpty());

    qint64 elapsed = timer.elapsed();
    qDebug() << "Empty manager operations took" << elapsed << "ms";

    // 验证没有死锁
    QVERIFY2(elapsed < 100, QString("Empty manager operations took too long: %1ms").arg(elapsed).toLocal8Bit());
}

// 测试实现完成
