/**
 * @file add_task_test.cpp
 * @brief 专门测试 Add Task 功能的手动测试程序
 * 
 * 这个程序专门用于验证 Add Task 功能是否修复了死循环问题
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QTimer>
#include <QElapsedTimer>
#include <QDebug>
#include "../ProgressWidget.h"
#include "../ProgressManager.h"

class AddTaskTestWindow : public QMainWindow
{
    Q_OBJECT

public:
    AddTaskTestWindow(QWidget *parent = nullptr)
        : QMainWindow(parent)
        , m_progressWidget(new ProgressWidget(this))
        , m_testCount(0)
    {
        setupUI();
        setupTests();
    }

private slots:
    void runSingleAddTest()
    {
        qDebug() << "=== Running Single Add Task Test ===";
        
        QElapsedTimer timer;
        timer.start();
        
        // 模拟用户点击 Add Task 按钮
        QString taskId = m_progressWidget->addProgressItem(
            QString("Test Task %1").arg(++m_testCount),
            "Testing add task functionality",
            100
        );
        
        qint64 elapsed = timer.elapsed();
        
        QString result = QString("Task created in %1ms - %2")
                        .arg(elapsed)
                        .arg(elapsed < 100 ? "✅ PASS" : "❌ FAIL (too slow)");
        
        m_resultLabel->setText(result);
        qDebug() << result;
        qDebug() << "Task ID:" << taskId;
    }
    
    void runRapidAddTest()
    {
        qDebug() << "=== Running Rapid Add Task Test ===";
        
        QElapsedTimer timer;
        timer.start();
        
        // 快速连续添加多个任务
        QStringList taskIds;
        for (int i = 0; i < 5; ++i) {
            QString taskId = m_progressWidget->addProgressItem(
                QString("Rapid Test %1").arg(++m_testCount),
                QString("Rapid test task %1").arg(i),
                100
            );
            taskIds.append(taskId);
        }
        
        qint64 elapsed = timer.elapsed();
        
        QString result = QString("5 tasks created in %1ms - %2")
                        .arg(elapsed)
                        .arg(elapsed < 500 ? "✅ PASS" : "❌ FAIL (too slow)");
        
        m_resultLabel->setText(result);
        qDebug() << result;
        qDebug() << "Task IDs:" << taskIds;
    }
    
    void runStressTest()
    {
        qDebug() << "=== Running Stress Test ===";
        
        QElapsedTimer timer;
        timer.start();
        
        // 压力测试：创建大量任务
        for (int i = 0; i < 20; ++i) {
            m_progressWidget->addProgressItem(
                QString("Stress Test %1").arg(++m_testCount),
                QString("Stress test task %1").arg(i),
                100
            );
        }
        
        qint64 elapsed = timer.elapsed();
        
        QString result = QString("20 tasks created in %1ms - %2")
                        .arg(elapsed)
                        .arg(elapsed < 1000 ? "✅ PASS" : "❌ FAIL (too slow)");
        
        m_resultLabel->setText(result);
        qDebug() << result;
    }

private:
    void setupUI()
    {
        setWindowTitle("Add Task Test - 死循环修复验证");
        setMinimumSize(800, 600);
        
        QWidget *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);
        
        // 测试按钮
        QHBoxLayout *buttonLayout = new QHBoxLayout();
        
        QPushButton *singleTestBtn = new QPushButton("Single Add Test", this);
        QPushButton *rapidTestBtn = new QPushButton("Rapid Add Test", this);
        QPushButton *stressTestBtn = new QPushButton("Stress Test", this);
        QPushButton *clearBtn = new QPushButton("Clear All", this);
        
        buttonLayout->addWidget(singleTestBtn);
        buttonLayout->addWidget(rapidTestBtn);
        buttonLayout->addWidget(stressTestBtn);
        buttonLayout->addWidget(clearBtn);
        
        // 结果显示
        m_resultLabel = new QLabel("Ready to test...", this);
        m_resultLabel->setStyleSheet("QLabel { background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc; }");
        
        // 进度组件
        mainLayout->addLayout(buttonLayout);
        mainLayout->addWidget(m_resultLabel);
        mainLayout->addWidget(m_progressWidget, 1);
        
        // 连接信号
        connect(singleTestBtn, &QPushButton::clicked, this, &AddTaskTestWindow::runSingleAddTest);
        connect(rapidTestBtn, &QPushButton::clicked, this, &AddTaskTestWindow::runRapidAddTest);
        connect(stressTestBtn, &QPushButton::clicked, this, &AddTaskTestWindow::runStressTest);
        connect(clearBtn, &QPushButton::clicked, [this]() {
            ProgressManager::instance()->clearAllItems();
            m_resultLabel->setText("All tasks cleared.");
        });
    }
    
    void setupTests()
    {
        // 自动运行初始测试
        QTimer::singleShot(1000, this, &AddTaskTestWindow::runSingleAddTest);
    }

private:
    ProgressWidget *m_progressWidget;
    QLabel *m_resultLabel;
    int m_testCount;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Starting Add Task Test Application...";
    qDebug() << "This test verifies that the Add Task death loop has been fixed.";
    
    AddTaskTestWindow window;
    window.show();
    
    return app.exec();
}

#include "add_task_test.moc"
