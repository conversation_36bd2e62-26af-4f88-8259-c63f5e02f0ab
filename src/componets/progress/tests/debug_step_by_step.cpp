#include <QApplication>
#include <QDebug>
#include <QTimer>
#include <QElapsedTimer>
#include <QEventLoop>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "=== Step by Step Simulate Progress Debug ===";
    
    // 创建 ProgressManager
    ProgressManager* manager = ProgressManager::instance();
    qDebug() << "1. ProgressManager created";
    
    // 创建 ProgressWidget
    ProgressWidget* widget = new ProgressWidget();
    qDebug() << "2. ProgressWidget created";
    
    widget->show();
    qDebug() << "3. ProgressWidget shown";
    
    // 等待初始化完成
    QEventLoop loop;
    QTimer::singleShot(500, &loop, &QEventLoop::quit);
    loop.exec();
    qDebug() << "4. Initialization complete";
    
    // 测试步骤1：直接创建一个进度项
    qDebug() << "5. Testing direct createProgressItem...";
    QElapsedTimer timer;
    timer.start();
    
    QString itemId = manager->createProgressItem("Test Task", "Testing...", 100);
    qint64 elapsed = timer.elapsed();
    qDebug() << "6. createProgressItem completed in" << elapsed << "ms, itemId:" << itemId;
    
    if (elapsed > 1000) {
        qDebug() << "❌ DEADLOCK in createProgressItem!";
        return 1;
    }
    
    // 等待信号处理
    QTimer::singleShot(100, [&]() {
        qDebug() << "7. Testing addTestProgressItems...";
        
        QElapsedTimer timer2;
        timer2.start();
        
        // 直接调用 addTestProgressItems
        widget->addTestProgressItems();
        
        qint64 elapsed2 = timer2.elapsed();
        qDebug() << "8. addTestProgressItems completed in" << elapsed2 << "ms";
        
        if (elapsed2 > 2000) {
            qDebug() << "❌ DEADLOCK in addTestProgressItems!";
            app.exit(1);
            return;
        }
        
        // 等待UI更新
        QTimer::singleShot(200, [&]() {
            qDebug() << "9. Testing simulateProgress...";
            
            QElapsedTimer timer3;
            timer3.start();
            
            // 现在测试 simulateProgress（应该不会调用 addTestProgressItems）
            widget->simulateProgress();
            
            qint64 elapsed3 = timer3.elapsed();
            qDebug() << "10. simulateProgress completed in" << elapsed3 << "ms";
            
            if (elapsed3 > 1000) {
                qDebug() << "❌ DEADLOCK in simulateProgress!";
                app.exit(1);
                return;
            }
            
            qDebug() << "11. All tests completed successfully!";
            qDebug() << "✅ No deadlock detected";
            app.quit();
        });
    });
    
    // 设置超时保护
    QTimer::singleShot(10000, [&]() {
        qDebug() << "❌ TIMEOUT - Test took too long!";
        app.exit(1);
    });
    
    return app.exec();
}
