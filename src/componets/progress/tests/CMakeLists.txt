cmake_minimum_required(VERSION 3.16)

# 测试项目配置
project(ProgressManagerTests)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找 Qt5 组件
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Test)

# 启用 Qt MOC
set(CMAKE_AUTOMOC ON)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../../..)

# 测试源文件
set(TEST_SOURCES
    test_runner.cpp
    ProgressManagerTest.cpp
    ../ProgressItem.cpp
    ../ProgressManager.cpp
    ../ProgressItemWidget.cpp
    ../ProgressListWidget.cpp
)

# 测试头文件
set(TEST_HEADERS
    ProgressManagerTest.h
    ../ProgressItem.h
    ../ProgressManager.h
    ../ProgressItemWidget.h
    ../ProgressListWidget.h
)

# 创建测试可执行文件
add_executable(ProgressManagerTests ${TEST_SOURCES} ${TEST_HEADERS})

# 链接 Qt 库
target_link_libraries(ProgressManagerTests 
    Qt5::Core 
    Qt5::Widgets 
    Qt5::Test
)

# 启用测试
enable_testing()

# 添加测试
add_test(NAME ProgressManagerTests COMMAND ProgressManagerTests)

# 设置测试属性
set_tests_properties(ProgressManagerTests PROPERTIES
    TIMEOUT 30
    ENVIRONMENT "QT_QPA_PLATFORM=offscreen"
)
