#include "EmptyTaskDeadlockTest.h"
#include <QDebug>
#include <QThread>
#include <QRandomGenerator>

EmptyTaskDeadlockTest::EmptyTaskDeadlockTest()
    : m_manager(nullptr)
    , m_widget(nullptr)
    , m_itemCreatedSpy(nullptr)
    , m_itemUpdatedSpy(nullptr)
    , m_itemRemovedSpy(nullptr)
    , m_statusChangedSpy(nullptr)
    , m_progressUpdatedSpy(nullptr)
    , m_statisticsChangedSpy(nullptr)
{
}

void EmptyTaskDeadlockTest::initTestCase()
{
    qDebug() << "=== Empty Task Deadlock Test Suite Started ===";
    
    // 获取 ProgressManager 实例
    m_manager = ProgressManager::instance();
    QVERIFY(m_manager != nullptr);
    
    // 创建 ProgressWidget
    m_widget = new ProgressWidget();
    QVERIFY(m_widget != nullptr);
    
    // 设置信号监控
    m_itemCreatedSpy = new QSignalSpy(m_manager, &ProgressManager::itemCreated);
    m_itemUpdatedSpy = new QSignalSpy(m_manager, &ProgressManager::itemUpdated);
    m_itemRemovedSpy = new QSignalSpy(m_manager, &ProgressManager::itemRemoved);
    m_statusChangedSpy = new QSignalSpy(m_manager, &ProgressManager::statusChanged);
    m_progressUpdatedSpy = new QSignalSpy(m_manager, &ProgressManager::progressUpdated);
    m_statisticsChangedSpy = new QSignalSpy(m_manager, &ProgressManager::statisticsChanged);
    
    QVERIFY(m_itemCreatedSpy->isValid());
    QVERIFY(m_itemUpdatedSpy->isValid());
    QVERIFY(m_itemRemovedSpy->isValid());
    QVERIFY(m_statusChangedSpy->isValid());
    QVERIFY(m_progressUpdatedSpy->isValid());
    QVERIFY(m_statisticsChangedSpy->isValid());
}

void EmptyTaskDeadlockTest::cleanupTestCase()
{
    qDebug() << "=== Empty Task Deadlock Test Suite Finished ===";
    
    // 清理信号监控
    delete m_itemCreatedSpy;
    delete m_itemUpdatedSpy;
    delete m_itemRemovedSpy;
    delete m_statusChangedSpy;
    delete m_progressUpdatedSpy;
    delete m_statisticsChangedSpy;
    
    // 清理 ProgressWidget
    if (m_widget) {
        m_widget->deleteLater();
        m_widget = nullptr;
    }
}

void EmptyTaskDeadlockTest::init()
{
    // 每个测试前确保管理器为空
    ensureManagerIsEmpty();
    
    // 清空信号监控
    m_itemCreatedSpy->clear();
    m_itemUpdatedSpy->clear();
    m_itemRemovedSpy->clear();
    m_statusChangedSpy->clear();
    m_progressUpdatedSpy->clear();
    m_statisticsChangedSpy->clear();
}

void EmptyTaskDeadlockTest::cleanup()
{
    // 每个测试后确保管理器为空
    ensureManagerIsEmpty();
}

void EmptyTaskDeadlockTest::testEmptyManagerBatchOperations()
{
    qDebug() << "Testing batch operations on empty manager...";
    
    QElapsedTimer timer;
    timer.start();
    
    // 在空管理器上执行批量操作
    m_manager->startAllItems();
    m_manager->pauseAllItems();
    m_manager->cancelAllItems();
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Empty batch operations took" << elapsed << "ms";
    
    // 验证没有死锁（应该很快完成）
    QVERIFY2(elapsed < 100, QString("Batch operations took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    // 验证没有信号发射（因为没有任务）
    QCOMPARE(m_itemUpdatedSpy->count(), 0);
    QCOMPARE(m_statusChangedSpy->count(), 0);
    
    // 验证管理器仍然为空
    QList<ProgressItemPtr> items = m_manager->getAllItems();
    QCOMPARE(items.size(), 0);
}

void EmptyTaskDeadlockTest::testEmptySimulateProgress()
{
    qDebug() << "Testing simulate progress with empty manager...";
    
    QElapsedTimer timer;
    timer.start();
    
    // 模拟 ProgressWidget::onSimulationTimer 的行为
    QStringList emptySimulationItems; // 空的模拟项目列表
    
    for (int iteration = 0; iteration < 10; iteration++) {
        // 模拟进度更新循环
        for (const QString& itemId : emptySimulationItems) {
            ProgressItemPtr item = m_manager->getProgressItem(itemId);
            if (item && item->status() == ProgressStatus::Running) {
                // 这个分支不应该执行，因为列表为空
                QFAIL("Should not reach here with empty simulation list");
            }
        }
        
        // 检查是否所有任务都完成了（空列表应该立即返回true）
        bool allCompleted = true;
        for (const QString& itemId : emptySimulationItems) {
            ProgressItemPtr item = m_manager->getProgressItem(itemId);
            if (item && item->status() != ProgressStatus::Completed) {
                allCompleted = false;
                break;
            }
        }
        
        if (allCompleted) {
            break; // 空列表应该立即跳出
        }
    }
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Empty simulation took" << elapsed << "ms";
    
    // 验证没有死锁
    QVERIFY2(elapsed < 50, QString("Empty simulation took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    // 验证没有信号发射
    QCOMPARE(m_progressUpdatedSpy->count(), 0);
    QCOMPARE(m_itemUpdatedSpy->count(), 0);
}

void EmptyTaskDeadlockTest::testEmptyProgressWidgetOperations()
{
    qDebug() << "Testing ProgressWidget operations with empty manager...";
    
    QElapsedTimer timer;
    timer.start();
    
    // 测试 ProgressWidget 的各种操作（避免弹出对话框的方法）
    m_widget->onStartAll();
    m_widget->onPauseAll();
    m_widget->onCancelAll();
    m_widget->onClearCompleted();
    // 跳过 onClearAll() 因为它会弹出对话框
    m_widget->onRefresh();
    
    // 跳过 simulateProgress() 因为它会在空列表时自动创建测试任务
    // 这不符合我们测试空任务情况的目的
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "ProgressWidget operations took" << elapsed << "ms";
    
    // 验证没有死锁
    QVERIFY2(elapsed < 200, QString("ProgressWidget operations took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    // 验证管理器仍然为空
    QList<ProgressItemPtr> items = m_manager->getAllItems();
    QCOMPARE(items.size(), 0);
}

void EmptyTaskDeadlockTest::testEmptyManagerWithSignals()
{
    qDebug() << "Testing empty manager signal behavior...";
    
    QElapsedTimer timer;
    timer.start();
    
    // 尝试各种可能触发信号的操作
    m_manager->updateProgress("non-existent", 50, "test");
    m_manager->completeProgress("non-existent");
    m_manager->startProgress("non-existent");
    m_manager->removeProgressItem("non-existent");
    
    // 批量操作
    m_manager->startAllItems();
    m_manager->pauseAllItems();
    m_manager->cancelAllItems();
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Signal operations took" << elapsed << "ms";
    
    // 验证没有死锁
    QVERIFY2(elapsed < 100, QString("Signal operations took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    // 验证没有意外的信号发射
    QCOMPARE(m_itemCreatedSpy->count(), 0);
    QCOMPARE(m_itemUpdatedSpy->count(), 0);
    QCOMPARE(m_itemRemovedSpy->count(), 0);
    QCOMPARE(m_statusChangedSpy->count(), 0);
    QCOMPARE(m_progressUpdatedSpy->count(), 0);
}

void EmptyTaskDeadlockTest::testEmptyManagerConcurrentAccess()
{
    qDebug() << "Testing concurrent access to empty manager...";
    
    QElapsedTimer timer;
    timer.start();
    
    // 模拟并发访问
    QList<QThread*> threads;
    
    for (int i = 0; i < 3; i++) {
        QThread* thread = QThread::create([this]() {
            for (int j = 0; j < 10; j++) {
                m_manager->getAllItems();
                m_manager->getRootItems();
                m_manager->getProgressItem("non-existent");
                m_manager->startAllItems();
                m_manager->pauseAllItems();
                QThread::msleep(1);
            }
        });
        threads.append(thread);
        thread->start();
    }
    
    // 等待所有线程完成
    for (QThread* thread : threads) {
        QVERIFY(thread->wait(5000)); // 5秒超时
        thread->deleteLater();
    }
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Concurrent access took" << elapsed << "ms";
    
    // 验证没有死锁
    QVERIFY2(elapsed < 1000, QString("Concurrent access took too long: %1ms").arg(elapsed).toLocal8Bit());
    
    // 验证管理器仍然为空
    QList<ProgressItemPtr> items = m_manager->getAllItems();
    QCOMPARE(items.size(), 0);
}

void EmptyTaskDeadlockTest::ensureManagerIsEmpty()
{
    m_manager->clearAllItems();
    QList<ProgressItemPtr> items = m_manager->getAllItems();
    if (!items.isEmpty()) {
        qWarning() << "Manager not empty, found" << items.size() << "items";
        for (const auto& item : items) {
            m_manager->removeProgressItem(item->id());
        }
    }
}

bool EmptyTaskDeadlockTest::waitForSignalOrTimeout(QSignalSpy* spy, int expectedCount, int timeoutMs)
{
    QElapsedTimer timer;
    timer.start();
    
    while (spy->count() < expectedCount && timer.elapsed() < timeoutMs) {
        QTest::qWait(10);
    }
    
    return spy->count() >= expectedCount;
}

void EmptyTaskDeadlockTest::simulateProgressWidgetTimer()
{
    // 模拟 ProgressWidget 的定时器触发
    QStringList emptyItems;
    
    for (const QString& itemId : emptyItems) {
        ProgressItemPtr item = m_manager->getProgressItem(itemId);
        if (item && item->status() == ProgressStatus::Running) {
            // 不应该执行到这里
        }
    }
}

void EmptyTaskDeadlockTest::testEmptyManagerStatisticsUpdate()
{
    qDebug() << "Testing statistics update with empty manager...";

    QElapsedTimer timer;
    timer.start();

    // 触发统计更新（这可能是死锁的原因）
    // 注意：updateStatistics() 是私有方法，我们通过其他操作间接触发

    // 这些操作应该触发统计更新
    m_manager->clearAllItems();
    m_manager->clearCompletedItems();
    m_manager->clearErrorItems();

    // 等待统计定时器可能的触发
    QTest::qWait(50);

    qint64 elapsed = timer.elapsed();
    qDebug() << "Statistics update took" << elapsed << "ms";

    // 验证没有死锁
    QVERIFY2(elapsed < 200, QString("Statistics update took too long: %1ms").arg(elapsed).toLocal8Bit());
}

void EmptyTaskDeadlockTest::testEmptyManagerTimerOperations()
{
    qDebug() << "Testing timer operations with empty manager...";

    QElapsedTimer timer;
    timer.start();

    // 模拟定时器相关的操作
    // 这些可能涉及内部定时器的启动/停止

    for (int i = 0; i < 5; i++) {
        m_manager->startAllItems();
        QTest::qWait(10);
        m_manager->pauseAllItems();
        QTest::qWait(10);
        m_manager->cancelAllItems();
        QTest::qWait(10);
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "Timer operations took" << elapsed << "ms";

    // 验证没有死锁
    QVERIFY2(elapsed < 300, QString("Timer operations took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证没有意外信号
    QCOMPARE(m_itemUpdatedSpy->count(), 0);
    QCOMPARE(m_statusChangedSpy->count(), 0);
}

void EmptyTaskDeadlockTest::testEmptyManagerClearOperations()
{
    qDebug() << "Testing clear operations with empty manager...";

    QElapsedTimer timer;
    timer.start();

    // 测试各种清理操作
    m_manager->clearAllItems();
    m_manager->clearCompletedItems();
    m_manager->clearErrorItems();

    // 重复多次以测试稳定性
    for (int i = 0; i < 10; i++) {
        m_manager->clearAllItems();
        m_manager->clearCompletedItems();
        m_manager->clearErrorItems();
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "Clear operations took" << elapsed << "ms";

    // 验证没有死锁
    QVERIFY2(elapsed < 100, QString("Clear operations took too long: %1ms").arg(elapsed).toLocal8Bit());

    // 验证管理器确实为空
    QList<ProgressItemPtr> items = m_manager->getAllItems();
    QCOMPARE(items.size(), 0);
}

QTEST_MAIN(EmptyTaskDeadlockTest)
