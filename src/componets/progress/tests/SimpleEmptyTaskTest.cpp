#include <QTest>
#include <QDebug>
#include <QElapsedTimer>
#include <QSignalSpy>
#include "../ProgressManager.h"

class SimpleEmptyTaskTest : public QObject
{
    Q_OBJECT

private slots:
    void testEmptyManagerOperations()
    {
        qDebug() << "Testing empty manager operations...";
        
        ProgressManager* manager = ProgressManager::instance();
        QVERIFY(manager != nullptr);
        
        // 确保管理器为空
        manager->clearAllItems();
        
        QElapsedTimer timer;
        timer.start();
        
        // 测试各种操作
        manager->startAllItems();
        manager->pauseAllItems();
        manager->cancelAllItems();
        manager->clearCompletedItems();
        manager->clearErrorItems();
        
        // 测试获取操作
        QList<ProgressItemPtr> items = manager->getAllItems();
        QCOMPARE(items.size(), 0);
        
        QList<ProgressItemPtr> rootItems = manager->getRootItems();
        QCOMPARE(rootItems.size(), 0);
        
        // 测试不存在的项目操作
        ProgressItemPtr item = manager->getProgressItem("non-existent");
        QVERIFY(item == nullptr);
        
        manager->updateProgress("non-existent", 50, "test");
        manager->completeProgress("non-existent");
        manager->startProgress("non-existent");
        
        bool removed = manager->removeProgressItem("non-existent");
        QVERIFY(!removed);
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Empty manager operations took" << elapsed << "ms";
        
        // 验证没有死锁（应该很快完成）
        QVERIFY2(elapsed < 100, QString("Operations took too long: %1ms").arg(elapsed).toLocal8Bit());
        
        qDebug() << "✅ Empty manager operations test PASSED";
    }
    
    void testEmptyManagerSignals()
    {
        qDebug() << "Testing empty manager signals...";
        
        ProgressManager* manager = ProgressManager::instance();
        manager->clearAllItems();
        
        // 设置信号监控
        QSignalSpy itemCreatedSpy(manager, &ProgressManager::itemCreated);
        QSignalSpy itemUpdatedSpy(manager, &ProgressManager::itemUpdated);
        QSignalSpy itemRemovedSpy(manager, &ProgressManager::itemRemoved);
        QSignalSpy progressUpdatedSpy(manager, &ProgressManager::progressUpdated);
        
        QElapsedTimer timer;
        timer.start();
        
        // 执行各种操作
        manager->startAllItems();
        manager->pauseAllItems();
        manager->cancelAllItems();
        manager->updateProgress("non-existent", 50, "test");
        manager->completeProgress("non-existent");
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Signal operations took" << elapsed << "ms";
        
        // 验证没有死锁
        QVERIFY2(elapsed < 100, QString("Signal operations took too long: %1ms").arg(elapsed).toLocal8Bit());
        
        // 验证没有意外信号（除了可能的 clearAllItems 信号）
        qDebug() << "itemCreated signals:" << itemCreatedSpy.count();
        qDebug() << "itemUpdated signals:" << itemUpdatedSpy.count();
        qDebug() << "itemRemoved signals:" << itemRemovedSpy.count();
        qDebug() << "progressUpdated signals:" << progressUpdatedSpy.count();
        
        // 对于空管理器，这些操作不应该产生信号
        QCOMPARE(itemCreatedSpy.count(), 0);
        QCOMPARE(itemUpdatedSpy.count(), 0);
        QCOMPARE(progressUpdatedSpy.count(), 0);
        
        qDebug() << "✅ Empty manager signals test PASSED";
    }
    
    void testEmptyManagerClearOperations()
    {
        qDebug() << "Testing empty manager clear operations...";
        
        ProgressManager* manager = ProgressManager::instance();
        
        QElapsedTimer timer;
        timer.start();
        
        // 重复清理操作
        for (int i = 0; i < 10; i++) {
            manager->clearAllItems();
            manager->clearCompletedItems();
            manager->clearErrorItems();
        }
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Clear operations took" << elapsed << "ms";
        
        // 验证没有死锁
        QVERIFY2(elapsed < 200, QString("Clear operations took too long: %1ms").arg(elapsed).toLocal8Bit());
        
        // 验证管理器仍然为空
        QList<ProgressItemPtr> items = manager->getAllItems();
        QCOMPARE(items.size(), 0);
        
        qDebug() << "✅ Empty manager clear operations test PASSED";
    }
    
    void testEmptyManagerBatchOperations()
    {
        qDebug() << "Testing empty manager batch operations...";
        
        ProgressManager* manager = ProgressManager::instance();
        manager->clearAllItems();
        
        QElapsedTimer timer;
        timer.start();
        
        // 快速连续批量操作
        for (int i = 0; i < 5; i++) {
            manager->startAllItems();
            manager->pauseAllItems();
            manager->cancelAllItems();
        }
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Batch operations took" << elapsed << "ms";
        
        // 验证没有死锁
        QVERIFY2(elapsed < 100, QString("Batch operations took too long: %1ms").arg(elapsed).toLocal8Bit());
        
        qDebug() << "✅ Empty manager batch operations test PASSED";
    }
};

QTEST_MAIN(SimpleEmptyTaskTest)
#include "SimpleEmptyTaskTest.moc"
