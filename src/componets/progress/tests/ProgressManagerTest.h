#ifndef PROGRESSMANAGERTEST_H
#define PROGRESSMANAGERTEST_H

#include <QObject>
#include <QTest>
#include <QSignalSpy>
#include <QTimer>
#include <QEventLoop>
#include <QDebug>
#include "../ProgressManager.h"
#include "../ProgressItem.h"
#include "../ProgressWidget.h"

/**
 * @brief 进度管理器测试类
 * 测试 ProgressManager 的各种功能和边界情况
 */
class ProgressManagerTest : public QObject
{
    Q_OBJECT

public:
    ProgressManagerTest();

private slots:
    // 测试初始化和清理
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();
    
    // 基本功能测试
    void testCreateProgressItem();
    void testCreateProgressItemWithParent();
    void testRemoveProgressItem();
    void testUpdateProgress();
    void testStatusChanges();
    
    // 父子关系测试
    void testParentChildRelationship();
    void testParentProgressCalculation();
    void testRemoveParentWithChildren();
    void testCircularReferenceProtection();
    
    // 信号测试
    void testSignalEmission();
    void testStatisticsSignals();
    
    // 线程安全测试
    void testThreadSafety();
    void testConcurrentAccess();
    
    // 边界情况测试
    void testInvalidOperations();
    void testEmptyStrings();
    void testNegativeValues();
    void testLargeNumbers();
    
    // 死循环检测测试
    void testInfiniteLoopDetection();
    void testRecursiveParentChild();
    void testSignalLoops();

    // Add Task 功能测试
    void testAddTaskDialog();
    void testAddTaskPerformance();
    void testAddTaskSignalLoop();
    void testProgressWidgetAddTask();
    void testUIRefreshLoop();

    // Pause All 功能测试
    void testPauseAllBasic();
    void testPauseAllPerformance();
    void testPauseAllDeadlock();
    void testBatchOperationsDeadlock();
    void testStartPauseAllCycle();

    // Simulate Progress 功能测试
    void testSimulateProgressBasic();
    void testSimulateProgressDeadlock();
    void testUpdateProgressDeadlock();
    void testCompleteProgressDeadlock();
    void testProgressSimulationCycle();

    // 空任务情况测试
    void testEmptyTaskListDeadlock();
    void testSimulateProgressWithNoTasks();
    void testBatchOperationsWithNoTasks();
    void testEmptyManagerOperations();
    
    // 性能测试
    void testPerformanceWithManyItems();
    void testMemoryLeaks();

private:
    // 辅助方法
    void waitForSignals(int timeoutMs = 1000);
    QString createTestItem(const QString& title = "Test Item", int total = 100);
    void verifyItemState(const QString& itemId, ProgressStatus expectedStatus);
    void simulateUserInteraction();
    
    // 测试数据
    ProgressManager* m_manager;
    QStringList m_testItemIds;
    QEventLoop m_eventLoop;
    QTimer m_timeoutTimer;
    
    // 信号监听器
    QSignalSpy* m_itemCreatedSpy;
    QSignalSpy* m_itemRemovedSpy;
    QSignalSpy* m_itemUpdatedSpy;
    QSignalSpy* m_progressUpdatedSpy;
    QSignalSpy* m_statusChangedSpy;
    QSignalSpy* m_statisticsChangedSpy;
};

#endif // PROGRESSMANAGERTEST_H
