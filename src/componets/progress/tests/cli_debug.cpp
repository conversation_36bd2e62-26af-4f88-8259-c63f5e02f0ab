/**
 * @file cli_debug.cpp
 * @brief 命令行调试 Add Task 死循环问题
 */

#include <QApplication>
#include <QDebug>
#include <QElapsedTimer>
#include <QTimer>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

class DebugRunner : public QObject
{
    Q_OBJECT

public:
    DebugRunner(QObject *parent = nullptr) : QObject(parent) {}

public slots:
    void runTests()
    {
        qDebug() << "=== Starting CLI Debug Tests ===";
        
        testDirectManagerCreate();
        testManagerSignals();
        testProgressWidgetCreate();
        testProgressWidgetAddTask();
        
        qDebug() << "=== All Tests Completed ===";
        QApplication::quit();
    }

private:
    void testDirectManagerCreate()
    {
        qDebug() << "\n1. Testing Direct ProgressManager::createProgressItem()";
        
        QElapsedTimer timer;
        timer.start();
        
        QString taskId = ProgressManager::instance()->createProgressItem(
            "Direct Test Task",
            "Testing direct creation",
            100
        );
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: Task created in" << elapsed << "ms";
        qDebug() << "   Task ID:" << taskId;
        qDebug() << "   Status:" << (elapsed < 100 ? "✅ PASS" : "❌ SLOW");
    }
    
    void testManagerSignals()
    {
        qDebug() << "\n2. Testing ProgressManager signals";
        
        int signalCount = 0;
        
        // 监听信号
        connect(ProgressManager::instance(), &ProgressManager::itemCreated,
                [&signalCount](const QString& id) {
                    signalCount++;
                    qDebug() << "   Signal: itemCreated" << id;
                });
        
        connect(ProgressManager::instance(), &ProgressManager::itemUpdated,
                [&signalCount](const QString& id) {
                    signalCount++;
                    qDebug() << "   Signal: itemUpdated" << id;
                });
        
        QElapsedTimer timer;
        timer.start();
        
        QString taskId = ProgressManager::instance()->createProgressItem(
            "Signal Test Task",
            "Testing signal emission",
            100
        );
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: Task created in" << elapsed << "ms";
        qDebug() << "   Signals emitted:" << signalCount;
        qDebug() << "   Status:" << (elapsed < 100 && signalCount <= 5 ? "✅ PASS" : "❌ FAIL");
    }
    
    void testProgressWidgetCreate()
    {
        qDebug() << "\n3. Testing ProgressWidget creation";
        
        QElapsedTimer timer;
        timer.start();
        
        qDebug() << "   Creating ProgressWidget...";
        ProgressWidget* widget = new ProgressWidget();
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: ProgressWidget created in" << elapsed << "ms";
        qDebug() << "   Status:" << (elapsed < 2000 ? "✅ PASS" : "❌ SLOW");
        
        // 清理
        widget->deleteLater();
        m_testWidget = widget; // 保存引用用于下一个测试
    }
    
    void testProgressWidgetAddTask()
    {
        qDebug() << "\n4. Testing ProgressWidget::addProgressItem()";
        
        if (!m_testWidget) {
            qDebug() << "   Creating new ProgressWidget for test...";
            m_testWidget = new ProgressWidget();
        }
        
        QElapsedTimer timer;
        timer.start();
        
        qDebug() << "   Calling addProgressItem...";
        QString taskId = m_testWidget->addProgressItem(
            "Widget Test Task",
            "Testing widget add task",
            100
        );
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: Task added in" << elapsed << "ms";
        qDebug() << "   Task ID:" << taskId;
        qDebug() << "   Status:" << (elapsed < 500 ? "✅ PASS" : "❌ SLOW");
        
        // 清理
        m_testWidget->deleteLater();
    }

private:
    ProgressWidget* m_testWidget = nullptr;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "CLI Debug Add Task Issue";
    qDebug() << "Qt Version:" << qVersion();
    
    DebugRunner runner;
    
    // 延迟启动测试，让事件循环开始
    QTimer::singleShot(100, &runner, &DebugRunner::runTests);
    
    return app.exec();
}

#include "cli_debug.moc"
