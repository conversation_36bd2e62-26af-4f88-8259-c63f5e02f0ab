#include <QApplication>
#include <QDebug>
#include <QTimer>
#include <QElapsedTimer>
#include <QEventLoop>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "=== Final Simulate Progress Test ===";
    
    // 创建 ProgressManager
    ProgressManager* manager = ProgressManager::instance();
    qDebug() << "1. ProgressManager created";
    
    // 创建 ProgressWidget
    ProgressWidget* widget = new ProgressWidget();
    qDebug() << "2. ProgressWidget created";
    
    widget->show();
    qDebug() << "3. ProgressWidget shown";
    
    // 等待初始化完成
    QEventLoop loop;
    QTimer::singleShot(500, &loop, &QEventLoop::quit);
    loop.exec();
    qDebug() << "4. Initialization complete";
    
    // 测试多次 simulateProgress 调用
    for (int i = 1; i <= 5; i++) {
        qDebug() << QString("5.%1. Testing simulateProgress call %2...").arg(i).arg(i);
        QElapsedTimer timer;
        timer.start();
        
        // 调用 simulateProgress
        widget->simulateProgress();
        
        qint64 elapsed = timer.elapsed();
        qDebug() << QString("6.%1. simulateProgress call %2 completed in %3 ms").arg(i).arg(i).arg(elapsed);
        
        if (elapsed > 2000) {
            qDebug() << QString("❌ DEADLOCK in simulateProgress call %1!").arg(i);
            return 1;
        }
        
        // 等待一段时间再进行下一次测试
        QEventLoop waitLoop;
        QTimer::singleShot(200, &waitLoop, &QEventLoop::quit);
        waitLoop.exec();
    }
    
    qDebug() << "7. Testing rapid simulateProgress calls...";
    
    // 测试快速连续调用
    QElapsedTimer rapidTimer;
    rapidTimer.start();
    
    for (int i = 0; i < 10; i++) {
        widget->simulateProgress();
    }
    
    qint64 rapidElapsed = rapidTimer.elapsed();
    qDebug() << "8. Rapid simulateProgress calls completed in" << rapidElapsed << "ms";
    
    if (rapidElapsed > 5000) {
        qDebug() << "❌ DEADLOCK in rapid simulateProgress calls!";
        return 1;
    }
    
    qDebug() << "9. All simulateProgress tests completed successfully!";
    qDebug() << "✅ No deadlock detected in any scenario";
    qDebug() << "✅ Simulate Progress functionality is working correctly";
    
    // 等待一段时间让用户看到结果
    QTimer::singleShot(2000, [&]() {
        qDebug() << "10. Test completed, exiting...";
        app.quit();
    });
    
    return app.exec();
}
