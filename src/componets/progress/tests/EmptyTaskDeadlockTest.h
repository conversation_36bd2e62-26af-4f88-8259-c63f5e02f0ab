#ifndef EMPTYTASKDEADLOCKTEST_H
#define EMPTYTASKDEADLOCKTEST_H

#include <QObject>
#include <QTest>
#include <QSignalSpy>
#include <QTimer>
#include <QElapsedTimer>
#include <QEventLoop>
#include <QApplication>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

class EmptyTaskDeadlockTest : public QObject
{
    Q_OBJECT

public:
    EmptyTaskDeadlockTest();

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 空任务死锁测试
    void testEmptyManagerBatchOperations();
    void testEmptySimulateProgress();
    void testEmptyProgressWidgetOperations();
    void testEmptyManagerWithSignals();
    void testEmptyManagerConcurrentAccess();
    void testEmptyManagerStatisticsUpdate();
    void testEmptyManagerTimerOperations();
    void testEmptyManagerClearOperations();

private:
    ProgressManager* m_manager;
    ProgressWidget* m_widget;
    
    // 信号监控
    QSignalSpy* m_itemCreatedSpy;
    QSignalSpy* m_itemUpdatedSpy;
    QSignalSpy* m_itemRemovedSpy;
    QSignalSpy* m_statusChangedSpy;
    QSignalSpy* m_progressUpdatedSpy;
    QSignalSpy* m_statisticsChangedSpy;
    
    // 测试辅助方法
    void ensureManagerIsEmpty();
    bool waitForSignalOrTimeout(QSignalSpy* spy, int expectedCount, int timeoutMs = 1000);
    void simulateProgressWidgetTimer();
};

#endif // EMPTYTASKDEADLOCKTEST_H
