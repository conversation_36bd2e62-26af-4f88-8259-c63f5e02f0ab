#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QDebug>
#include <QElapsedTimer>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

/**
 * @brief 手动测试程序
 * 用于测试添加任务时是否会出现鼠标旋转（死循环）问题
 */
class ManualTestWidget : public QWidget
{
    Q_OBJECT

public:
    ManualTestWidget(QWidget* parent = nullptr) : QWidget(parent)
    {
        setupUI();
        m_manager = ProgressManager::instance();
    }

private slots:
    void onAddSimpleTask()
    {
        qDebug() << "Adding simple task...";
        QElapsedTimer timer;
        timer.start();
        
        QString taskId = m_manager->createProgressItem("Simple Task", "Testing simple task creation", 100);
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Simple task creation took" << elapsed << "ms";
        
        m_statusLabel->setText(QString("Simple task created in %1ms. ID: %2").arg(elapsed).arg(taskId));
        
        if (elapsed > 100) {
            m_statusLabel->setStyleSheet("color: red;");
        } else {
            m_statusLabel->setStyleSheet("color: green;");
        }
    }
    
    void onAddParentChildTasks()
    {
        qDebug() << "Adding parent-child tasks...";
        QElapsedTimer timer;
        timer.start();
        
        QString parentId = m_manager->createProgressItem("Parent Task", "Testing parent task", 100);
        QString child1Id = m_manager->createProgressItem("Child Task 1", "Testing child 1", 50, parentId);
        QString child2Id = m_manager->createProgressItem("Child Task 2", "Testing child 2", 50, parentId);
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Parent-child tasks creation took" << elapsed << "ms";
        
        m_statusLabel->setText(QString("Parent-child tasks created in %1ms").arg(elapsed));
        
        if (elapsed > 200) {
            m_statusLabel->setStyleSheet("color: red;");
        } else {
            m_statusLabel->setStyleSheet("color: green;");
        }
    }
    
    void onAddManyTasks()
    {
        qDebug() << "Adding many tasks...";
        QElapsedTimer timer;
        timer.start();
        
        for (int i = 0; i < 20; ++i) {
            QString taskId = m_manager->createProgressItem(
                QString("Batch Task %1").arg(i), 
                QString("Testing batch creation %1").arg(i), 
                100
            );
            
            // 立即更新进度
            m_manager->updateProgress(taskId, i * 5, QString("Progress %1").arg(i * 5));
        }
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Many tasks creation took" << elapsed << "ms";
        
        m_statusLabel->setText(QString("20 tasks created in %1ms").arg(elapsed));
        
        if (elapsed > 500) {
            m_statusLabel->setStyleSheet("color: red;");
        } else {
            m_statusLabel->setStyleSheet("color: green;");
        }
    }
    
    void onRemoveAllTasks()
    {
        qDebug() << "Removing all tasks...";
        QElapsedTimer timer;
        timer.start();
        
        m_manager->clearAllItems();
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "Clear all tasks took" << elapsed << "ms";
        
        m_statusLabel->setText(QString("All tasks cleared in %1ms").arg(elapsed));
        m_statusLabel->setStyleSheet("color: blue;");
    }
    
    void onTestProgressWidget()
    {
        qDebug() << "Testing ProgressWidget...";
        QElapsedTimer timer;
        timer.start();
        
        // 创建 ProgressWidget 窗口
        ProgressWidget* widget = new ProgressWidget();
        widget->setWindowTitle("Progress Widget Test");
        widget->resize(800, 600);
        widget->show();
        
        // 添加测试数据
        widget->addTestProgressItems();
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "ProgressWidget creation and test data took" << elapsed << "ms";
        
        m_statusLabel->setText(QString("ProgressWidget created in %1ms").arg(elapsed));
        
        if (elapsed > 1000) {
            m_statusLabel->setStyleSheet("color: red;");
        } else {
            m_statusLabel->setStyleSheet("color: green;");
        }
    }

private:
    void setupUI()
    {
        setWindowTitle("Progress Manager Manual Test");
        resize(400, 300);
        
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        QLabel* titleLabel = new QLabel("Progress Manager Manual Test", this);
        titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;");
        layout->addWidget(titleLabel);
        
        QPushButton* simpleBtn = new QPushButton("Add Simple Task", this);
        connect(simpleBtn, &QPushButton::clicked, this, &ManualTestWidget::onAddSimpleTask);
        layout->addWidget(simpleBtn);
        
        QPushButton* parentChildBtn = new QPushButton("Add Parent-Child Tasks", this);
        connect(parentChildBtn, &QPushButton::clicked, this, &ManualTestWidget::onAddParentChildTasks);
        layout->addWidget(parentChildBtn);
        
        QPushButton* manyBtn = new QPushButton("Add Many Tasks (20)", this);
        connect(manyBtn, &QPushButton::clicked, this, &ManualTestWidget::onAddManyTasks);
        layout->addWidget(manyBtn);
        
        QPushButton* removeBtn = new QPushButton("Remove All Tasks", this);
        connect(removeBtn, &QPushButton::clicked, this, &ManualTestWidget::onRemoveAllTasks);
        layout->addWidget(removeBtn);
        
        QPushButton* widgetBtn = new QPushButton("Test ProgressWidget", this);
        connect(widgetBtn, &QPushButton::clicked, this, &ManualTestWidget::onTestProgressWidget);
        layout->addWidget(widgetBtn);
        
        m_statusLabel = new QLabel("Ready to test...", this);
        m_statusLabel->setStyleSheet("margin: 10px; padding: 10px; border: 1px solid gray;");
        layout->addWidget(m_statusLabel);
        
        layout->addStretch();
    }
    
    ProgressManager* m_manager;
    QLabel* m_statusLabel;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    ManualTestWidget widget;
    widget.show();
    
    return app.exec();
}

#include "manual_test.moc"
