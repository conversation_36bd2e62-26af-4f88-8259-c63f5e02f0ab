#include <QApplication>
#include <QDebug>
#include <QElapsedTimer>
#include <QTimer>
#include <QEventLoop>
#include <QRandomGenerator>
#include "../ProgressManager.h"
#include "../ProgressWidget.h"

class SimulateDebugger : public QObject
{
    Q_OBJECT

public:
    SimulateDebugger(QObject* parent = nullptr) : QObject(parent)
    {
        m_manager = ProgressManager::instance();
        
        // 连接信号以监控
        connect(m_manager, &ProgressManager::itemCreated,
                this, [](const QString& id) { qDebug() << "Signal: itemCreated" << id; });
        connect(m_manager, &ProgressManager::itemUpdated,
                this, [](const QString& id) { qDebug() << "Signal: itemUpdated" << id; });
        connect(m_manager, &ProgressManager::progressUpdated,
                this, [](const QString& id, int current, int total, float percentage) { 
                    qDebug() << "Signal: progressUpdated" << id << current << "/" << total << "(" << percentage << "%)"; 
                });
        connect(m_manager, &ProgressManager::itemCompleted,
                this, [](const QString& id) { qDebug() << "Signal: itemCompleted" << id; });
        connect(m_manager, &ProgressManager::statusChanged,
                this, [](const QString& id, ProgressStatus old, ProgressStatus newStatus) { 
                    qDebug() << "Signal: statusChanged" << id << "from" << (int)old << "to" << (int)newStatus; 
                });
    }

    void runTests()
    {
        qDebug() << "=== Simulate Progress Debug Tests ===";
        
        testDirectProgressUpdates();
        testProgressCompletion();
        testSimulationLoop();
        
        qDebug() << "=== All Tests Completed ===";
    }

private slots:
    void testDirectProgressUpdates()
    {
        qDebug() << "\n1. Testing Direct Progress Updates";
        
        // 创建并启动任务
        QString taskId = m_manager->createProgressItem("Direct Update Test", "Testing direct updates", 100);
        m_manager->startProgress(taskId);
        
        QElapsedTimer timer;
        timer.start();
        
        qDebug() << "   Performing rapid progress updates...";
        for (int i = 10; i <= 90; i += 10) {
            m_manager->updateProgress(taskId, i, QString("Progress: %1%").arg(i));
        }
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: 9 progress updates completed in" << elapsed << "ms";
        
        // 验证状态
        ProgressItemPtr item = m_manager->getProgressItem(taskId);
        if (item && item->current() == 90) {
            qDebug() << "   Status: ✅ PASS";
        } else {
            qDebug() << "   Status: ❌ FAIL";
        }
        
        // 清理
        m_manager->removeProgressItem(taskId);
    }
    
    void testProgressCompletion()
    {
        qDebug() << "\n2. Testing Progress Completion";
        
        // 创建并启动任务
        QString taskId = m_manager->createProgressItem("Completion Test", "Testing completion", 100);
        m_manager->startProgress(taskId);
        
        QElapsedTimer timer;
        timer.start();
        
        qDebug() << "   Updating to 100% and completing...";
        m_manager->updateProgress(taskId, 100, "Finalizing...");
        m_manager->completeProgress(taskId);
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: Progress completion in" << elapsed << "ms";
        
        // 验证状态
        ProgressItemPtr item = m_manager->getProgressItem(taskId);
        if (item && item->status() == ProgressStatus::Completed) {
            qDebug() << "   Status: ✅ PASS";
        } else {
            qDebug() << "   Status: ❌ FAIL";
        }
        
        // 清理
        m_manager->removeProgressItem(taskId);
    }
    
    void testSimulationLoop()
    {
        qDebug() << "\n3. Testing Simulation Loop";
        
        // 创建多个任务
        QStringList taskIds;
        for (int i = 0; i < 3; i++) {
            QString taskId = m_manager->createProgressItem(
                QString("Simulation Task %1").arg(i + 1),
                "Testing simulation loop",
                100
            );
            m_manager->startProgress(taskId);
            taskIds.append(taskId);
        }
        
        QElapsedTimer timer;
        timer.start();
        
        qDebug() << "   Simulating progress for 3 tasks...";
        
        // 模拟进度更新循环
        bool allCompleted = false;
        int iterations = 0;
        const int maxIterations = 50; // 防止无限循环
        
        while (!allCompleted && iterations < maxIterations) {
            allCompleted = true;
            
            for (const QString& taskId : taskIds) {
                ProgressItemPtr item = m_manager->getProgressItem(taskId);
                if (item && item->status() == ProgressStatus::Running) {
                    int current = item->current();
                    int total = item->total();
                    
                    if (current < total) {
                        int increment = QRandomGenerator::global()->bounded(5, 15); // 随机增加5-14
                        current = qMin(current + increment, total);
                        
                        QString message = QString("Processing... (%1/%2)").arg(current).arg(total);
                        m_manager->updateProgress(taskId, current, message);
                        
                        if (current >= total) {
                            m_manager->completeProgress(taskId);
                        } else {
                            allCompleted = false;
                        }
                    }
                }
            }
            
            iterations++;
        }
        
        qint64 elapsed = timer.elapsed();
        qDebug() << "   Result: Simulation completed in" << elapsed << "ms (" << iterations << "iterations)";
        
        // 验证所有任务都完成
        int completedCount = 0;
        for (const QString& taskId : taskIds) {
            ProgressItemPtr item = m_manager->getProgressItem(taskId);
            if (item && item->status() == ProgressStatus::Completed) {
                completedCount++;
            }
        }
        
        if (completedCount == taskIds.size() && iterations < maxIterations) {
            qDebug() << "   Status: ✅ PASS - All" << completedCount << "tasks completed";
        } else {
            qDebug() << "   Status: ❌ FAIL - Only" << completedCount << "of" << taskIds.size() << "tasks completed";
        }
        
        // 清理
        for (const QString& taskId : taskIds) {
            m_manager->removeProgressItem(taskId);
        }
    }

private:
    ProgressManager* m_manager;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Debug Simulate Progress Issue";
    qDebug() << "Qt Version:" << QT_VERSION_STR;
    
    SimulateDebugger debugger;
    
    // 使用定时器延迟执行，确保事件循环启动
    QTimer::singleShot(100, &debugger, &SimulateDebugger::runTests);
    
    // 设置超时退出
    QTimer::singleShot(15000, &app, &QApplication::quit);
    
    return app.exec();
}

#include "debug_simulate.moc"
