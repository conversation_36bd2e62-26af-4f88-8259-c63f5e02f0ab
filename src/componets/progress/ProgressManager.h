#ifndef PROGRESSMANAGER_H
#define PROGRESSMANAGER_H

#include <QMap>
#include <QTimer>
#include <QMutex>
#include <QThread>
#include "ProgressItem.h"

/**
 * @brief 进度管理器
 * 管理所有进度项，处理信号更新、父子关系、状态变化等
 * 提供线程安全的进度更新接口
 */
class ProgressManager : public QObject
{
    Q_OBJECT

public:
    explicit ProgressManager(QObject *parent = nullptr);
    ~ProgressManager() override;
    
    // 单例模式
    static ProgressManager* instance();
    
    // 进度项管理
    QString createProgressItem(const QString& title, 
                              const QString& message = QString(),
                              int total = 100,
                              const QString& parentId = QString());
    
    bool removeProgressItem(const QString& itemId);
    void clearAllItems();
    
    // 获取进度项
    ProgressItemPtr getProgressItem(const QString& itemId) const;
    QList<ProgressItemPtr> getAllItems() const;
    QList<ProgressItemPtr> getRootItems() const;
    QList<ProgressItemPtr> getChildItems(const QString& parentId) const;
    
    // 进度更新（线程安全）
    void updateProgress(const QString& itemId, int current, const QString& message = QString());
    void incrementProgress(const QString& itemId, int increment = 1, const QString& message = QString());
    void setProgressMessage(const QString& itemId, const QString& message);
    void setProgressTotal(const QString& itemId, int total);
    
    // 状态控制
    void startProgress(const QString& itemId);
    void pauseProgress(const QString& itemId);
    void resumeProgress(const QString& itemId);
    void completeProgress(const QString& itemId);
    void cancelProgress(const QString& itemId);
    void setProgressError(const QString& itemId, const QString& errorMessage = QString());
    
    // 父子关系管理
    bool addChildItem(const QString& parentId, const QString& childId);
    bool removeChildItem(const QString& parentId, const QString& childId);
    QString createChildItem(const QString& parentId, 
                           const QString& title,
                           const QString& message = QString(),
                           int total = 100);
    
    // 批量操作
    void startAllItems();
    void pauseAllItems();
    void cancelAllItems();
    void clearCompletedItems();
    void clearErrorItems();
    
    // 统计信息
    int getTotalCount() const;
    int getRunningCount() const;
    int getPausedCount() const;
    int getCompletedCount() const;
    int getErrorCount() const;
    int getCancelledCount() const;
    
    // 查找功能
    QList<ProgressItemPtr> findItemsByTitle(const QString& title) const;
    QList<ProgressItemPtr> findItemsByStatus(ProgressStatus status) const;
    ProgressItemPtr findItemByTitle(const QString& title) const;

signals:
    // 进度项生命周期信号
    void itemCreated(const QString& itemId);
    void itemRemoved(const QString& itemId);
    void itemUpdated(const QString& itemId);
    
    // 进度更新信号
    void progressUpdated(const QString& itemId, int current, int total, double percentage);
    void messageUpdated(const QString& itemId, const QString& message);
    
    // 状态变化信号
    void statusChanged(const QString& itemId, ProgressStatus oldStatus, ProgressStatus newStatus);
    void itemStarted(const QString& itemId);
    void itemPaused(const QString& itemId);
    void itemResumed(const QString& itemId);
    void itemCompleted(const QString& itemId);
    void itemCancelled(const QString& itemId);
    void itemError(const QString& itemId, const QString& errorMessage);
    
    // 父子关系信号
    void childAdded(const QString& parentId, const QString& childId);
    void childRemoved(const QString& parentId, const QString& childId);
    
    // 统计信息信号
    void statisticsChanged(int total, int running, int paused, int completed, int error, int cancelled);

public slots:
    // 外部控制槽函数
    void onStartRequested(const QString& itemId);
    void onPauseRequested(const QString& itemId);
    void onResumeRequested(const QString& itemId);
    void onCancelRequested(const QString& itemId);
    void onRemoveRequested(const QString& itemId);

private slots:
    void onStatisticsTimer();
    void processProgressUpdate(const QString& itemId, int current, const QString& message);

private:
    void initializeManager();
    void updateStatistics();
    void emitStatisticsChanged();
    bool isValidItemId(const QString& itemId) const;
    void connectItemSignals(ProgressItemPtr item);
    void disconnectItemSignals(ProgressItemPtr item);
    void notifyItemUpdated(const QString& itemId);
    
    // 线程安全的内部方法
    void setStatusInternal(const QString& itemId, ProgressStatus status, const QString& message = QString());
    bool removeProgressItemInternal(const QString& itemId);
    
    // 数据成员
    mutable QMutex m_mutex;
    QMap<QString, ProgressItemPtr> m_items;
    
    // 统计信息
    int m_totalCount;
    int m_runningCount;
    int m_pausedCount;
    int m_completedCount;
    int m_errorCount;
    int m_cancelledCount;
    
    // 定时器
    QTimer* m_statisticsTimer = nullptr;
    
    // 单例实例
    static ProgressManager* s_instance;
    static QMutex s_instanceMutex;
    
    // 常量
    static constexpr int STATISTICS_UPDATE_INTERVAL = 1000; // 1秒更新一次统计信息
};

// 便利宏定义
#define PROGRESS_MANAGER ProgressManager::instance()

#endif // PROGRESSMANAGER_H
