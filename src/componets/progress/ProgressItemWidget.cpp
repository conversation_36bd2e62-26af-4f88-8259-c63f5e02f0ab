#include "ProgressItemWidget.h"
#include <QApplication>
#include <QStyle>
#include <QPainter>
#include <QMouseEvent>
#include <QDebug>

ProgressItemWidget::ProgressItemWidget(ProgressItemPtr item, QWidget *parent)
    : QWidget(parent)
    , m_item(item)
    , m_indentLevel(0)
    , m_showChildren(true)
    , m_isHovered(false)
{
    setupUI();
    setupLayout();
    setupActions();
    setupAnimations();
    updateDisplay();
    
    // 启动更新定时器
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &ProgressItemWidget::onUpdateTimer);
    m_updateTimer->start(UPDATE_INTERVAL);
}

void ProgressItemWidget::updateDisplay()
{
    if (!m_item) return;
    
    // 更新标题和消息
    m_titleLabel->setText(m_item->title());
    m_messageLabel->setText(m_item->message());
    m_messageLabel->setVisible(!m_item->message().isEmpty());
    
    // 更新进度条和标签
    updateProgressBar();
    
    // 更新状态图标
    updateStatusIcon();
    
    // 更新按钮状态
    updateButtons();
    
    // 更新时间显示
    updateTimeDisplay();
    
    // 更新展开按钮
    m_expandButton->setVisible(m_item->isParent());
    m_expandButton->setText(m_showChildren ? "−" : "+");
    
    // 更新样式
    updateStyleSheet();
}

void ProgressItemWidget::setIndentLevel(int level)
{
    m_indentLevel = qMax(0, level);
    m_indentWidget->setFixedWidth(m_indentLevel * INDENT_SIZE);
}

void ProgressItemWidget::setShowChildren(bool show)
{
    // 静默更新，不发射信号，避免循环
    if (m_showChildren != show) {
        m_showChildren = show;
        m_expandButton->setText(m_showChildren ? "−" : "+");
        // 不发射信号，避免任何可能的循环
    }
}

void ProgressItemWidget::setShowChildrenWithSignal(bool show)
{
    // 带信号的版本，只在用户交互时使用
    if (m_showChildren != show) {
        m_showChildren = show;
        m_expandButton->setText(m_showChildren ? "−" : "+");
        emit expandToggled(m_item->id(), m_showChildren);
    }
}

void ProgressItemWidget::fadeIn()
{
    m_opacityEffect->setOpacity(0.0);
    m_fadeAnimation->setStartValue(0.0);
    m_fadeAnimation->setEndValue(1.0);
    m_fadeAnimation->start();
}

void ProgressItemWidget::fadeOut()
{
    m_fadeAnimation->setStartValue(1.0);
    m_fadeAnimation->setEndValue(0.0);
    m_fadeAnimation->start();
}

void ProgressItemWidget::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制背景
    QRect rect = this->rect();
    if (m_isHovered) {
        painter.fillRect(rect, QColor(240, 240, 240, 100));
    }
    
    // 绘制左侧状态条
    QString statusColor = m_item->getStatusColor();
    painter.fillRect(0, 0, 3, rect.height(), QColor(statusColor));
    
    QWidget::paintEvent(event);
}

void ProgressItemWidget::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit itemDoubleClicked(m_item->id());
    }
    QWidget::mouseDoubleClickEvent(event);
}

void ProgressItemWidget::enterEvent(QEvent* event)
{
    m_isHovered = true;
    update();
    QWidget::enterEvent(event);
}

void ProgressItemWidget::leaveEvent(QEvent* event)
{
    m_isHovered = false;
    update();
    QWidget::leaveEvent(event);
}

void ProgressItemWidget::onStartPauseClicked()
{
    if (!m_item) return;
    
    switch (m_item->status()) {
        case ProgressStatus::NotStarted:
        case ProgressStatus::Cancelled:
            emit startRequested(m_item->id());
            break;
        case ProgressStatus::Running:
            emit pauseRequested(m_item->id());
            break;
        case ProgressStatus::Paused:
            emit resumeRequested(m_item->id());
            break;
        default:
            break;
    }
}

void ProgressItemWidget::onCancelClicked()
{
    if (m_item) {
        emit cancelRequested(m_item->id());
    }
}

void ProgressItemWidget::onMenuRequested()
{
    if (m_contextMenu) {
        m_contextMenu->exec(QCursor::pos());
    }
}

void ProgressItemWidget::onExpandClicked()
{
    // 用户点击时使用带信号的版本
    setShowChildrenWithSignal(!m_showChildren);
}

void ProgressItemWidget::onUpdateTimer()
{
    updateTimeDisplay();
}

void ProgressItemWidget::onFadeAnimationFinished()
{
    if (m_fadeAnimation->endValue().toDouble() == 0.0) {
        hide();
    }
}

void ProgressItemWidget::setupUI()
{
    // 创建主要组件
    m_indentWidget = new QWidget(this);
    m_expandButton = new QPushButton(this);
    m_statusIconLabel = new QLabel(this);
    m_titleLabel = new QLabel(this);
    m_messageLabel = new QLabel(this);
    m_progressBar = new QProgressBar(this);
    m_progressLabel = new QLabel(this);
    m_startPauseButton = new QPushButton(this);
    m_cancelButton = new QPushButton(this);
    m_menuButton = new QPushButton(this);
    m_timeLabel = new QLabel(this);
    m_elapsedLabel = new QLabel(this);
    
    // 设置基本属性
    m_indentWidget->setFixedWidth(0);
    
    m_expandButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_expandButton->setFlat(true);
    m_expandButton->setText("+");
    
    m_statusIconLabel->setFixedSize(ICON_SIZE, ICON_SIZE);
    m_statusIconLabel->setScaledContents(true);
    
    m_titleLabel->setStyleSheet("font-weight: bold; color: #333;");
    m_messageLabel->setStyleSheet("color: #666; font-size: 11px;");
    m_messageLabel->setWordWrap(true);
    
    m_progressBar->setTextVisible(false);
    m_progressBar->setFixedHeight(6);
    
    m_progressLabel->setStyleSheet("color: #666; font-size: 11px;");
    m_progressLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    
    m_startPauseButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_startPauseButton->setFlat(true);
    
    m_cancelButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_cancelButton->setFlat(true);
    m_cancelButton->setText("✕");
    
    m_menuButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_menuButton->setFlat(true);
    m_menuButton->setText("⋯");
    
    m_timeLabel->setStyleSheet("color: #666; font-size: 10px;");
    m_elapsedLabel->setStyleSheet("color: #666; font-size: 10px;");
    
    // 连接信号
    connect(m_expandButton, &QPushButton::clicked, this, &ProgressItemWidget::onExpandClicked);
    connect(m_startPauseButton, &QPushButton::clicked, this, &ProgressItemWidget::onStartPauseClicked);
    connect(m_cancelButton, &QPushButton::clicked, this, &ProgressItemWidget::onCancelClicked);
    connect(m_menuButton, &QPushButton::clicked, this, &ProgressItemWidget::onMenuRequested);
}

void ProgressItemWidget::setupLayout()
{
    // 主布局
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);
    
    // 缩进
    m_mainLayout->addWidget(m_indentWidget);
    
    // 展开按钮
    m_mainLayout->addWidget(m_expandButton);
    
    // 内容布局
    m_contentLayout = new QVBoxLayout();
    m_contentLayout->setSpacing(3);
    
    // 头部布局（图标、标题、按钮）
    m_headerLayout = new QHBoxLayout();
    m_headerLayout->setSpacing(8);
    m_headerLayout->addWidget(m_statusIconLabel);
    m_headerLayout->addWidget(m_titleLabel, 1);
    m_headerLayout->addWidget(m_startPauseButton);
    m_headerLayout->addWidget(m_cancelButton);
    m_headerLayout->addWidget(m_menuButton);
    
    // 进度布局
    m_progressLayout = new QHBoxLayout();
    m_progressLayout->setSpacing(8);
    m_progressLayout->addWidget(m_progressBar, 1);
    m_progressLayout->addWidget(m_progressLabel);
    
    // 信息布局
    m_infoLayout = new QHBoxLayout();
    m_infoLayout->setSpacing(8);
    m_infoLayout->addWidget(m_timeLabel, 1);
    m_infoLayout->addWidget(m_elapsedLabel);
    
    // 组装内容布局
    m_contentLayout->addLayout(m_headerLayout);
    m_contentLayout->addWidget(m_messageLabel);
    m_contentLayout->addLayout(m_progressLayout);
    m_contentLayout->addLayout(m_infoLayout);
    
    m_mainLayout->addLayout(m_contentLayout, 1);
    
    setLayout(m_mainLayout);
}

void ProgressItemWidget::setupActions()
{
    createContextMenu();
}

void ProgressItemWidget::setupAnimations()
{
    // 透明度效果
    m_opacityEffect = new QGraphicsOpacityEffect(this);
    setGraphicsEffect(m_opacityEffect);
    
    // 淡入淡出动画
    m_fadeAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_fadeAnimation->setDuration(300);
    connect(m_fadeAnimation, &QPropertyAnimation::finished, 
            this, &ProgressItemWidget::onFadeAnimationFinished);
}

void ProgressItemWidget::updateStyleSheet()
{
    if (!m_item) return;
    
    QString statusColor = m_item->getStatusColor();
    
    // 更新进度条样式
    QString progressBarStyle = QString(
        "QProgressBar {"
        "    border: 1px solid #ddd;"
        "    border-radius: 3px;"
        "    background-color: #f5f5f5;"
        "}"
        "QProgressBar::chunk {"
        "    background-color: %1;"
        "    border-radius: 2px;"
        "}"
    ).arg(statusColor);
    
    m_progressBar->setStyleSheet(progressBarStyle);
}

void ProgressItemWidget::updateProgressBar() const
{
    if (!m_item) return;
    
    double percentage = m_item->progressPercentage();
    m_progressBar->setValue(static_cast<int>(percentage));
    m_progressLabel->setText(m_item->getProgressText());
}

void ProgressItemWidget::updateStatusIcon() const
{
    if (!m_item) return;
    
    QIcon icon = m_item->getStatusIcon();
    m_statusIconLabel->setPixmap(icon.pixmap(ICON_SIZE, ICON_SIZE));
}

void ProgressItemWidget::updateButtons() const
{
    if (!m_item) return;
    
    // 更新开始/暂停按钮
    switch (m_item->status()) {
        case ProgressStatus::NotStarted:
        case ProgressStatus::Cancelled:
            m_startPauseButton->setText("▶");
            m_startPauseButton->setToolTip("Start");
            m_startPauseButton->setEnabled(true);
            break;
        case ProgressStatus::Running:
            m_startPauseButton->setText("⏸");
            m_startPauseButton->setToolTip("Pause");
            m_startPauseButton->setEnabled(true);
            break;
        case ProgressStatus::Paused:
            m_startPauseButton->setText("▶");
            m_startPauseButton->setToolTip("Resume");
            m_startPauseButton->setEnabled(true);
            break;
        case ProgressStatus::Completed:
        case ProgressStatus::Error:
            m_startPauseButton->setText("▶");
            m_startPauseButton->setToolTip("Restart");
            m_startPauseButton->setEnabled(true);
            break;
    }
    
    // 取消按钮
    bool canCancel = (m_item->status() == ProgressStatus::Running || 
                      m_item->status() == ProgressStatus::Paused);
    m_cancelButton->setEnabled(canCancel);
}

void ProgressItemWidget::updateTimeDisplay() const
{
    if (!m_item) return;
    
    m_timeLabel->setText(m_item->getFormattedStartTime());
    m_elapsedLabel->setText(m_item->getElapsedTime());
}

void ProgressItemWidget::createContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    m_startAction = m_contextMenu->addAction("Start");
    m_pauseAction = m_contextMenu->addAction("Pause");
    m_resumeAction = m_contextMenu->addAction("Resume");
    m_contextMenu->addSeparator();
    m_cancelAction = m_contextMenu->addAction("Cancel");
    m_removeAction = m_contextMenu->addAction("Remove");
    m_contextMenu->addSeparator();
    m_showDetailsAction = m_contextMenu->addAction("Show Details");
    
    // 连接信号
    connect(m_startAction, &QAction::triggered, [this]() {
        emit startRequested(m_item->id());
    });
    connect(m_pauseAction, &QAction::triggered, [this]() {
        emit pauseRequested(m_item->id());
    });
    connect(m_resumeAction, &QAction::triggered, [this]() {
        emit resumeRequested(m_item->id());
    });
    connect(m_cancelAction, &QAction::triggered, [this]() {
        emit cancelRequested(m_item->id());
    });
    connect(m_removeAction, &QAction::triggered, [this]() {
        emit removeRequested(m_item->id());
    });
}
