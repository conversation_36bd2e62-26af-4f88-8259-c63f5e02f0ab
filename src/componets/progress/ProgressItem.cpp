#include "ProgressItem.h"
#include <QApplication>
#include <QStyle>
#include <QDebug>
#include <QUuid>

ProgressItem::ProgressItem()
    : m_id(QUuid::createUuid().toString(QUuid::WithoutBraces))
      , m_title("Untitled Task")
      , m_current(0)
      , m_total(100)
      , m_status(ProgressStatus::NotStarted)
{
}

ProgressItem::ProgressItem(const QString& title, const QString& message, int total)
    : m_id(QUuid::createUuid().toString(QUuid::WithoutBraces))
      , m_title(title)
      , m_message(message)
      , m_current(0)
      , m_total(total)
      , m_status(ProgressStatus::NotStarted)
{
}

double ProgressItem::progressPercentage() const
{
    if (isParent())
    {
        // 父项目：计算所有子项目的平均进度
        if (m_children.isEmpty())
        {
            return 0.0;
        }

        double totalProgress = 0.0;
        for (const auto& child : m_children)
        {
            totalProgress += child->progressPercentage();
        }
        return totalProgress / m_children.size();
    }
    // 子项目或独立项目：使用自身进度
    if (m_total <= 0)
    {
        return 0.0;
    }
    return (static_cast<double>(m_current) / m_total) * 100.0;
}

QString ProgressItem::getProgressText() const
{
    if (isParent())
    {
        return QString("%1%").arg(QString::number(progressPercentage(), 'f', 1));
    }
    return QString("%1/%2 (%3%)")
           .arg(m_current)
           .arg(m_total)
           .arg(QString::number(progressPercentage(), 'f', 1));
}

QIcon ProgressItem::getStatusIcon() const
{
    QStyle* style = QApplication::style();

    switch (m_status)
    {
    case ProgressStatus::NotStarted:
        return style->standardIcon(QStyle::SP_MediaStop);
    case ProgressStatus::Running:
        return style->standardIcon(QStyle::SP_MediaPlay);
    case ProgressStatus::Paused:
        return style->standardIcon(QStyle::SP_MediaPause);
    case ProgressStatus::Completed:
        return style->standardIcon(QStyle::SP_DialogApplyButton);
    case ProgressStatus::Error:
        return style->standardIcon(QStyle::SP_MessageBoxCritical);
    case ProgressStatus::Cancelled:
        return style->standardIcon(QStyle::SP_DialogCancelButton);
    default:
        return QIcon();
    }
}

QString ProgressItem::getStatusColor() const
{
    switch (m_status)
    {
    case ProgressStatus::NotStarted:
        return "#6C757D"; // 灰色
    case ProgressStatus::Running:
        return "#007ACC"; // 蓝色 (JetBrains风格)
    case ProgressStatus::Paused:
        return "#FFA500"; // 橙色
    case ProgressStatus::Completed:
        return "#28A745"; // 绿色
    case ProgressStatus::Error:
        return "#DC3545"; // 红色
    case ProgressStatus::Cancelled:
        return "#6C757D"; // 灰色
    default:
        return "#6C757D";
    }
}

QString ProgressItem::getFormattedStartTime() const
{
    if (m_startTime.isValid())
    {
        return m_startTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    return "Not started";
}

QString ProgressItem::getFormattedEndTime() const
{
    if (m_endTime.isValid())
    {
        return m_endTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    return "Not finished";
}

QString ProgressItem::getElapsedTime() const
{
    if (!m_startTime.isValid())
    {
        return "00:00:00";
    }

    QDateTime endTime = m_endTime.isValid() ? m_endTime : QDateTime::currentDateTime();
    qint64 elapsed = m_startTime.msecsTo(endTime);
    return formatDuration(elapsed);
}

void ProgressItem::setCurrent(int current)
{
    m_current = qMax(0, qMin(current, m_total));
    updateParentProgress();
}

void ProgressItem::setTotal(int total)
{
    m_total = qMax(1, total);
    m_current = qMin(m_current, m_total);
    updateParentProgress();
}

void ProgressItem::setStatus(ProgressStatus status)
{
    m_status = status;

    // 自动设置时间
    if (status == ProgressStatus::Running && !m_startTime.isValid())
    {
        m_startTime = QDateTime::currentDateTime();
    }
    else if ((status == ProgressStatus::Completed ||
            status == ProgressStatus::Error ||
            status == ProgressStatus::Cancelled) &&
        !m_endTime.isValid())
    {
        m_endTime = QDateTime::currentDateTime();
    }
}

void ProgressItem::updateProgress(int current, const QString& message)
{
    setCurrent(current);
    if (!message.isEmpty())
    {
        m_message = message;
    }
}

void ProgressItem::incrementProgress(int increment, const QString& message)
{
    updateProgress(m_current + increment, message);
}

void ProgressItem::start()
{
    setStatus(ProgressStatus::Running);
    if (!m_startTime.isValid())
    {
        m_startTime = QDateTime::currentDateTime();
    }
}

void ProgressItem::pause()
{
    if (m_status == ProgressStatus::Running)
    {
        setStatus(ProgressStatus::Paused);
    }
}

void ProgressItem::resume()
{
    if (m_status == ProgressStatus::Paused)
    {
        setStatus(ProgressStatus::Running);
    }
}

void ProgressItem::complete()
{
    m_current = m_total;
    setStatus(ProgressStatus::Completed);
    updateParentProgress();
}

void ProgressItem::cancel()
{
    setStatus(ProgressStatus::Cancelled);
}

void ProgressItem::setError(const QString& errorMessage)
{
    setStatus(ProgressStatus::Error);
    if (!errorMessage.isEmpty())
    {
        m_message = errorMessage;
    }
}

void ProgressItem::setParent(QSharedPointer<ProgressItem> parent)
{
    m_parent = parent;
}

void ProgressItem::addChild(QSharedPointer<ProgressItem> child)
{
    if (!child || m_children.contains(child))
    {
        return;
    }

    m_children.append(child);
    calculateParentProgress();
}

void ProgressItem::removeChild(QSharedPointer<ProgressItem> child)
{
    if (m_children.removeOne(child))
    {
        child->m_parent.clear();
        calculateParentProgress();
    }
}

void ProgressItem::removeChild(const QString& childId)
{
    for (int i = 0; i < m_children.size(); ++i)
    {
        if (m_children[i]->id() == childId)
        {
            m_children[i]->m_parent.clear();
            m_children.removeAt(i);
            calculateParentProgress();
            break;
        }
    }
}

QSharedPointer<ProgressItem> ProgressItem::findChild(const QString& childId) const
{
    for (const auto& child : m_children)
    {
        if (child->id() == childId)
        {
            return child;
        }
    }
    return nullptr;
}

QList<QSharedPointer<ProgressItem>> ProgressItem::getAllDescendants() const
{
    QList<QSharedPointer<ProgressItem>> descendants;

    for (const auto& child : m_children)
    {
        descendants.append(child);
        descendants.append(child->getAllDescendants());
    }

    return descendants;
}

void ProgressItem::updateParentProgress() const
{
    if (auto parent = m_parent.lock())
    {
        parent->calculateParentProgress();
    }
}

void ProgressItem::calculateParentProgress()
{
    if (!isParent())
    {
        return;
    }

    // 父项目的进度基于子项目的平均进度
    // 这里不直接修改m_current和m_total，因为父项目的进度是计算得出的

    // 更新父项目的状态
    bool allCompleted = true;
    bool anyRunning = false;
    bool anyError = false;

    for (const auto& child : m_children)
    {
        switch (child->status())
        {
        case ProgressStatus::Running:
            anyRunning = true;
            allCompleted = false;
            break;
        case ProgressStatus::Error:
            anyError = true;
            allCompleted = false;
            break;
        case ProgressStatus::NotStarted:
        case ProgressStatus::Paused:
        case ProgressStatus::Cancelled:
            allCompleted = false;
            break;
        case ProgressStatus::Completed:
            break;
        }
    }

    // 直接设置状态，避免递归调用
    ProgressStatus newStatus = m_status;
    if (anyError)
    {
        newStatus = ProgressStatus::Error;
    }
    else if (allCompleted && !m_children.isEmpty())
    {
        newStatus = ProgressStatus::Completed;
    }
    else if (anyRunning)
    {
        newStatus = ProgressStatus::Running;
    }

    // 只有状态真正改变时才更新
    if (newStatus != m_status)
    {
        m_status = newStatus;
        // 不调用 updateParentProgress() 避免无限递归
    }
}

QString ProgressItem::formatDuration(qint64 milliseconds)
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    const qint64 hours = minutes / 60;

    seconds %= 60;
    minutes %= 60;

    return QString("%1:%2:%3")
           .arg(hours, 2, 10, QChar('0'))
           .arg(minutes, 2, 10, QChar('0'))
           .arg(seconds, 2, 10, QChar('0'));
}
