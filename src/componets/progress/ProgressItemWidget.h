#ifndef PROGRESSITEMWIDGET_H
#define PROGRESSITEMWIDGET_H

#include <QLabel>
#include <QPushButton>
#include <QProgressBar>
#include <QVBoxLayout>
#include <QMenu>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include "ProgressItem.h"

/**
 * @brief 进度项UI组件
 * 显示单个进度项，包含状态图标、标题、进度条、操作按钮、时间等
 * 支持JetBrains风格的美观设计
 */
class ProgressItemWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ProgressItemWidget(ProgressItemPtr item, QWidget *parent = nullptr);
    
    // 获取关联的进度项
    ProgressItemPtr getProgressItem() const { return m_item; }
    
    // 更新显示
    void updateDisplay();
    
    // 设置缩进级别（用于层级显示）
    void setIndentLevel(int level);
    
    // 设置是否显示子项目
    void setShowChildren(bool show);  // 静默设置，不发射信号
    void setShowChildrenWithSignal(bool show);  // 带信号版本，用于用户交互
    bool isShowingChildren() const { return m_showChildren; }
    
    // 动画效果
    void fadeIn();
    void fadeOut();

signals:
    // 操作信号
    void startRequested(const QString& itemId);
    void pauseRequested(const QString& itemId);
    void resumeRequested(const QString& itemId);
    void cancelRequested(const QString& itemId);
    void removeRequested(const QString& itemId);
    
    // 展开/折叠信号
    void expandToggled(const QString& itemId, bool expanded);
    
    // 双击信号
    void itemDoubleClicked(const QString& itemId);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;
    void enterEvent(QEvent* event) override;
    void leaveEvent(QEvent* event) override;

private slots:
    void onStartPauseClicked();
    void onCancelClicked();
    void onMenuRequested();
    void onExpandClicked();
    void onUpdateTimer();
    void onFadeAnimationFinished();

private:
    void setupUI();
    void setupLayout();
    void setupActions();
    void setupAnimations();
    void updateStyleSheet();
    void updateProgressBar() const;
    void updateStatusIcon() const;
    void updateButtons() const;
    void updateTimeDisplay() const;
    void createContextMenu();
    
    ProgressItemPtr m_item;
    int m_indentLevel;
    bool m_showChildren;
    bool m_isHovered;
    
    // UI组件
    QHBoxLayout* m_mainLayout;
    QVBoxLayout* m_contentLayout;
    QHBoxLayout* m_headerLayout;
    QHBoxLayout* m_progressLayout;
    QHBoxLayout* m_infoLayout;
    
    // 缩进和展开
    QWidget* m_indentWidget;
    QPushButton* m_expandButton;
    
    // 状态和标题
    QLabel* m_statusIconLabel;
    QLabel* m_titleLabel;
    QLabel* m_messageLabel;
    
    // 进度条
    QProgressBar* m_progressBar;
    QLabel* m_progressLabel;
    
    // 操作按钮
    QPushButton* m_startPauseButton;
    QPushButton* m_cancelButton;
    QPushButton* m_menuButton;
    
    // 时间信息
    QLabel* m_timeLabel;
    QLabel* m_elapsedLabel;
    
    // 上下文菜单
    QMenu* m_contextMenu;
    QAction* m_startAction;
    QAction* m_pauseAction;
    QAction* m_resumeAction;
    QAction* m_cancelAction;
    QAction* m_removeAction;
    QAction* m_showDetailsAction;
    
    // 定时器和动画
    QTimer* m_updateTimer;
    QPropertyAnimation* m_fadeAnimation;
    QGraphicsOpacityEffect* m_opacityEffect;

    // 样式常量
    static constexpr int INDENT_SIZE = 20;
    static constexpr int ICON_SIZE = 16;
    static constexpr int BUTTON_SIZE = 24;
    static constexpr int UPDATE_INTERVAL = 1000; // 1秒更新一次
};

#endif // PROGRESSITEMWIDGET_H
