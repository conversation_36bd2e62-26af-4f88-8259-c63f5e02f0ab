# Progress Manager 进度管理组件

基于Qt的进度条集合系统，支持层级结构、实时更新、状态管理等功能，采用JetBrains风格的美观设计。

## 功能特性

### 核心功能
1. **层级结构支持** - 支持父子项目关系，父项目进度自动计算为子项目平均进度
2. **实时进度更新** - 通过信号槽机制实现线程安全的进度更新
3. **多种状态管理** - 支持未开始、运行中、暂停、完成、错误、取消等状态
4. **操作控制** - 提供开始、暂停、继续、取消等操作按钮
5. **时间跟踪** - 自动记录开始时间、结束时间和运行时长
6. **过滤和搜索** - 支持按状态过滤和文本搜索
7. **美观界面** - 参考JetBrains产品设计，提供现代化UI

### 组件架构
- **ProgressItem** - 进度项数据模型
- **ProgressItemWidget** - 单个进度项UI组件
- **ProgressListWidget** - 进度列表管理组件
- **ProgressManager** - 进度管理器（单例模式）
- **ProgressWindow** - 主进度窗口

## 使用方法

### 基本使用

```cpp
#include "ProgressWindow.h"
#include "ProgressManager.h"

// 创建进度窗口
ProgressWindow window;
window.show();

// 创建进度项
QString taskId = PROGRESS_MANAGER->createProgressItem(
    "编译项目",           // 标题
    "正在初始化...",      // 初始消息
    100                  // 总步数
);

// 更新进度
PROGRESS_MANAGER->updateProgress(taskId, 50, "编译中...");

// 完成任务
PROGRESS_MANAGER->completeProgress(taskId);
```

### 创建层级结构

```cpp
// 创建父任务
QString mainTaskId = PROGRESS_MANAGER->createProgressItem("主构建任务", "", 100);

// 创建子任务
QString compileId = PROGRESS_MANAGER->createChildItem(
    mainTaskId, "编译源码", "编译C++文件...", 50
);
QString linkId = PROGRESS_MANAGER->createChildItem(
    mainTaskId, "链接库文件", "链接对象文件...", 30
);

// 启动任务
PROGRESS_MANAGER->startProgress(mainTaskId);
PROGRESS_MANAGER->startProgress(compileId);
```

### 状态控制

```cpp
// 启动任务
PROGRESS_MANAGER->startProgress(taskId);

// 暂停任务
PROGRESS_MANAGER->pauseProgress(taskId);

// 恢复任务
PROGRESS_MANAGER->resumeProgress(taskId);

// 取消任务
PROGRESS_MANAGER->cancelProgress(taskId);

// 设置错误状态
PROGRESS_MANAGER->setProgressError(taskId, "编译失败：语法错误");
```

### 批量操作

```cpp
// 启动所有任务
PROGRESS_MANAGER->startAllItems();

// 暂停所有任务
PROGRESS_MANAGER->pauseAllItems();

// 取消所有任务
PROGRESS_MANAGER->cancelAllItems();

// 清除已完成的任务
PROGRESS_MANAGER->clearCompletedItems();
```

### 信号连接

```cpp
// 连接进度管理器信号
connect(PROGRESS_MANAGER, &ProgressManager::itemCreated,
        this, &MyClass::onItemCreated);
connect(PROGRESS_MANAGER, &ProgressManager::progressUpdated,
        this, &MyClass::onProgressUpdated);
connect(PROGRESS_MANAGER, &ProgressManager::statusChanged,
        this, &MyClass::onStatusChanged);
```

## API 参考

### ProgressManager 主要方法

#### 项目管理
- `createProgressItem(title, message, total, parentId)` - 创建进度项
- `removeProgressItem(itemId)` - 移除进度项
- `getProgressItem(itemId)` - 获取进度项
- `getAllItems()` - 获取所有项目
- `getRootItems()` - 获取根项目

#### 进度更新
- `updateProgress(itemId, current, message)` - 更新进度
- `incrementProgress(itemId, increment, message)` - 增量更新
- `setProgressMessage(itemId, message)` - 设置消息
- `setProgressTotal(itemId, total)` - 设置总数

#### 状态控制
- `startProgress(itemId)` - 启动
- `pauseProgress(itemId)` - 暂停
- `resumeProgress(itemId)` - 恢复
- `completeProgress(itemId)` - 完成
- `cancelProgress(itemId)` - 取消
- `setProgressError(itemId, message)` - 设置错误

### ProgressItem 属性

- `id()` - 唯一标识符
- `title()` - 标题
- `message()` - 当前消息
- `current()` - 当前进度
- `total()` - 总进度
- `status()` - 状态
- `progressPercentage()` - 进度百分比
- `startTime()` - 开始时间
- `endTime()` - 结束时间
- `getElapsedTime()` - 运行时长

## 设计特点

### JetBrains 风格设计
- 现代化扁平设计
- 清晰的状态指示
- 直观的操作按钮
- 优雅的动画效果
- 合理的颜色搭配

### 状态颜色方案
- **未开始**: 灰色 (#6C757D)
- **运行中**: 蓝色 (#007ACC)
- **暂停**: 橙色 (#FFA500)
- **完成**: 绿色 (#28A745)
- **错误**: 红色 (#DC3545)
- **取消**: 灰色 (#6C757D)

### 线程安全
- 使用QMutex保护共享数据
- 信号槽机制确保UI线程安全
- 支持多线程环境下的进度更新

## 示例应用

项目包含完整的示例应用，展示了所有功能的使用方法：

1. 运行 `./BaseWidget`
2. 切换到 "Progress Manager" 标签页
3. 点击 "Tools" -> "Add Test Items" 添加测试数据
4. 点击 "Tools" -> "Simulate Progress" 开始模拟进度
5. 尝试各种操作按钮和功能

## 编译要求

- Qt 5.15 或更高版本
- C++17 支持
- CMake 4.0 或更高版本

## 文件结构

```
src/componets/progress/
├── ProgressItem.h/cpp              # 进度项数据模型
├── ProgressItemWidget.h/cpp        # 进度项UI组件
├── ProgressListWidget.h/cpp        # 进度列表组件
├── ProgressManager.h/cpp           # 进度管理器
├── ProgressWindow.h/cpp            # 主进度窗口
├── prd.md                          # 产品需求文档
└── README.md                       # 说明文档
```

## 扩展建议

1. **持久化支持** - 添加进度数据的保存和恢复功能
2. **网络同步** - 支持多客户端进度同步
3. **插件系统** - 支持自定义进度处理器
4. **更多动画** - 添加更丰富的视觉效果
5. **主题支持** - 支持多种UI主题切换
