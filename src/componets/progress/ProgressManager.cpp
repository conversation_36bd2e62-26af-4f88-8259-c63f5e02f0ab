#include "ProgressManager.h"
#include <QApplication>
#include <QDebug>
#include <QMutexLocker>

// 静态成员初始化
ProgressManager* ProgressManager::s_instance = nullptr;
QMutex ProgressManager::s_instanceMutex;

ProgressManager::ProgressManager(QObject* parent)
    : QObject(parent)
      , m_totalCount(0)
      , m_runningCount(0)
      , m_pausedCount(0)
      , m_completedCount(0)
      , m_errorCount(0)
      , m_cancelledCount(0)
{
    initializeManager();
}

ProgressManager::~ProgressManager()
{
    clearAllItems();
}

ProgressManager* ProgressManager::instance()
{
    QMutexLocker locker(&s_instanceMutex);
    if (!s_instance)
    {
        s_instance = new ProgressManager();
    }
    return s_instance;
}

QString ProgressManager::createProgressItem(const QString& title,
                                            const QString& message,
                                            int total,
                                            const QString& parentId)
{
    QString itemId;

    // 在锁的作用域内创建项目
    {
        QMutexLocker locker(&m_mutex);

        // 创建新的进度项
        ProgressItemPtr item = QSharedPointer<ProgressItem>::create(title, message, total);

        // 设置父子关系
        if (!parentId.isEmpty() && m_items.contains(parentId))
        {
            ProgressItemPtr parent = m_items[parentId];
            item->setParent(parent);
            parent->addChild(item);
        }

        // 添加到管理器
        m_items[item->id()] = item;
        connectItemSignals(item);

        updateStatistics();

        itemId = item->id();
    }

    // 在锁外发射信号，避免死锁
    emit itemCreated(itemId);
    emit itemUpdated(itemId);

    return itemId;
}

bool ProgressManager::removeProgressItem(const QString& itemId)
{
    QMutexLocker locker(&m_mutex);
    return removeProgressItemInternal(itemId);
}

bool ProgressManager::removeProgressItemInternal(const QString& itemId)
{
    // 注意：调用此方法时必须已经持有 m_mutex 锁

    if (!m_items.contains(itemId))
    {
        return false;
    }

    ProgressItemPtr item = m_items[itemId];

    // 收集所有子项目ID，避免在迭代过程中修改容器
    QStringList childIds;
    QList<ProgressItemPtr> children = item->children();
    for (const auto& child : children)
    {
        childIds.append(child->id());
    }

    // 递归移除所有子项目（不需要重新获取锁）
    for (const QString& childId : childIds)
    {
        removeProgressItemInternal(childId);
    }

    // 从父项目中移除
    if (auto parent = item->parent())
    {
        parent->removeChild(item);
        emit childRemoved(parent->id(), itemId);
    }

    // 断开信号连接
    disconnectItemSignals(item);

    // 从管理器中移除
    m_items.remove(itemId);

    updateStatistics();

    emit itemRemoved(itemId);

    return true;
}

void ProgressManager::clearAllItems()
{
    QStringList itemIds;

    // 在锁的作用域内清除项目
    {
        QMutexLocker locker(&m_mutex);

        // 断开所有信号连接
        auto items = m_items.values();
        for (const auto& item : items)
        {
            disconnectItemSignals(item);
        }

        // 收集项目ID
        itemIds = m_items.keys();

        // 清除所有项目
        m_items.clear();

        updateStatistics();
    }

    // 在锁外发送移除信号，避免死锁
    for (const QString& itemId : itemIds)
    {
        emit itemRemoved(itemId);
    }
}

ProgressItemPtr ProgressManager::getProgressItem(const QString& itemId) const
{
    QMutexLocker locker(&m_mutex);
    return m_items.value(itemId);
}

QList<ProgressItemPtr> ProgressManager::getAllItems() const
{
    QMutexLocker locker(&m_mutex);
    return m_items.values();
}

QList<ProgressItemPtr> ProgressManager::getRootItems() const
{
    QMutexLocker locker(&m_mutex);
    QList<ProgressItemPtr> rootItems;
    auto items = m_items.values();
    for (const auto& item : items)
    {
        if (!item->isChild())
        {
            rootItems.append(item);
        }
    }

    return rootItems;
}

QList<ProgressItemPtr> ProgressManager::getChildItems(const QString& parentId) const
{
    QMutexLocker locker(&m_mutex);

    if (m_items.contains(parentId))
    {
        return m_items[parentId]->children();
    }

    return {};
}

void ProgressManager::updateProgress(const QString& itemId, int current, const QString& message)
{
    int total = 0;
    float percentage = 0.0f;
    bool shouldEmitProgress = false;
    bool shouldEmitMessage = false;

    // 在锁的作用域内更新进度
    {
        QMutexLocker locker(&m_mutex);

        if (m_items.contains(itemId))
        {
            int oldCurrent = 0;
            ProgressItemPtr item = m_items[itemId];
            oldCurrent = item->current();

            item->updateProgress(current, message);

            total = item->total();
            percentage = item->progressPercentage();

            shouldEmitProgress = (oldCurrent != item->current());
            shouldEmitMessage = !message.isEmpty();

            updateStatistics();
        }
    }

    // 在锁外发射信号，避免死锁
    if (shouldEmitProgress)
    {
        emit progressUpdated(itemId, current, total, percentage);
    }
    if (shouldEmitMessage)
    {
        emit messageUpdated(itemId, message);
    }
    if (shouldEmitProgress || shouldEmitMessage)
    {
        emit itemUpdated(itemId);
    }
}

void ProgressManager::incrementProgress(const QString& itemId, int increment, const QString& message)
{
    int newCurrent = 0;
    int total = 0;
    float percentage = 0.0f;
    bool shouldEmitProgress = false;
    bool shouldEmitMessage = false;

    // 在锁的作用域内更新进度
    {
        QMutexLocker locker(&m_mutex);

        if (m_items.contains(itemId))
        {
            ProgressItemPtr item = m_items[itemId];
            int oldCurrent = item->current();
            newCurrent = oldCurrent + increment;

            item->updateProgress(newCurrent, message);

            total = item->total();
            percentage = item->progressPercentage();

            shouldEmitProgress = (oldCurrent != newCurrent);
            shouldEmitMessage = !message.isEmpty();

            updateStatistics();
        }
    }

    // 在锁外发射信号，避免死锁
    if (shouldEmitProgress)
    {
        emit progressUpdated(itemId, newCurrent, total, percentage);
    }
    if (shouldEmitMessage)
    {
        emit messageUpdated(itemId, message);
    }
    if (shouldEmitProgress || shouldEmitMessage)
    {
        emit itemUpdated(itemId);
    }
}

void ProgressManager::setProgressMessage(const QString& itemId, const QString& message)
{
    bool shouldEmit = false;

    // 在锁的作用域内更新消息
    {
        QMutexLocker locker(&m_mutex);

        if (m_items.contains(itemId))
        {
            ProgressItemPtr item = m_items[itemId];
            item->setMessage(message);
            shouldEmit = true;
        }
    }

    // 在锁外发射信号，避免死锁
    if (shouldEmit)
    {
        emit messageUpdated(itemId, message);
        emit itemUpdated(itemId);
    }
}

void ProgressManager::setProgressTotal(const QString& itemId, int total)
{
    int current = 0;
    float percentage = 0.0f;
    bool shouldEmit = false;

    // 在锁的作用域内更新总数
    {
        QMutexLocker locker(&m_mutex);

        if (m_items.contains(itemId))
        {
            ProgressItemPtr item = m_items[itemId];
            item->setTotal(total);
            current = item->current();
            percentage = item->progressPercentage();
            shouldEmit = true;
        }
    }

    // 在锁外发射信号，避免死锁
    if (shouldEmit)
    {
        emit progressUpdated(itemId, current, total, percentage);
        emit itemUpdated(itemId);
    }
}

void ProgressManager::startProgress(const QString& itemId)
{
    setStatusInternal(itemId, ProgressStatus::Running);
}

void ProgressManager::pauseProgress(const QString& itemId)
{
    setStatusInternal(itemId, ProgressStatus::Paused);
}

void ProgressManager::resumeProgress(const QString& itemId)
{
    setStatusInternal(itemId, ProgressStatus::Running);
}

void ProgressManager::completeProgress(const QString& itemId)
{
    ProgressStatus oldStatus = ProgressStatus::NotStarted;
    int current = 0;
    int total = 0;
    float percentage = 0.0f;
    bool shouldEmit = false;

    // 在锁的作用域内完成进度
    {
        QMutexLocker locker(&m_mutex);

        if (m_items.contains(itemId))
        {
            ProgressItemPtr item = m_items[itemId];
            oldStatus = item->status();
            item->complete();

            current = item->current();
            total = item->total();
            percentage = item->progressPercentage();
            shouldEmit = true;

            updateStatistics();
        }
    }

    // 在锁外发射信号，避免死锁
    if (shouldEmit)
    {
        emit statusChanged(itemId, oldStatus, ProgressStatus::Completed);
        emit itemCompleted(itemId);
        emit progressUpdated(itemId, current, total, percentage);
        emit itemUpdated(itemId);
    }
}

void ProgressManager::cancelProgress(const QString& itemId)
{
    setStatusInternal(itemId, ProgressStatus::Cancelled);
}

void ProgressManager::setProgressError(const QString& itemId, const QString& errorMessage)
{
    setStatusInternal(itemId, ProgressStatus::Error, errorMessage);
}

bool ProgressManager::addChildItem(const QString& parentId, const QString& childId)
{
    bool success = false;

    // 在锁的作用域内添加子项目
    {
        QMutexLocker locker(&m_mutex);

        if (!m_items.contains(parentId) || !m_items.contains(childId))
        {
            return false;
        }

        ProgressItemPtr parent = m_items[parentId];
        ProgressItemPtr child = m_items[childId];

        // 避免循环引用
        if (child->getAllDescendants().contains(parent))
        {
            return false;
        }

        child->setParent(parent);
        parent->addChild(child);
        success = true;
    }

    // 在锁外发射信号，避免死锁
    if (success)
    {
        emit childAdded(parentId, childId);
        emit itemUpdated(parentId);
        emit itemUpdated(childId);
    }

    return success;
}

bool ProgressManager::removeChildItem(const QString& parentId, const QString& childId)
{
    bool success = false;

    // 在锁的作用域内移除子项目
    {
        QMutexLocker locker(&m_mutex);

        if (!m_items.contains(parentId) || !m_items.contains(childId))
        {
            return false;
        }

        ProgressItemPtr parent = m_items[parentId];
        ProgressItemPtr child = m_items[childId];

        parent->removeChild(child);
        success = true;
    }

    // 在锁外发射信号，避免死锁
    if (success)
    {
        emit childRemoved(parentId, childId);
        emit itemUpdated(parentId);
        emit itemUpdated(childId);
    }

    return success;
}

QString ProgressManager::createChildItem(const QString& parentId,
                                         const QString& title,
                                         const QString& message,
                                         int total)
{
    return createProgressItem(title, message, total, parentId);
}

void ProgressManager::startAllItems()
{
    QStringList startedItemIds;
    QList<ProgressStatus> oldStatuses;

    // 在锁的作用域内启动项目
    {
        QMutexLocker locker(&m_mutex);

        for (auto item : m_items.values())
        {
            if (item->status() == ProgressStatus::NotStarted ||
                item->status() == ProgressStatus::Paused ||
                item->status() == ProgressStatus::Cancelled)
            {
                ProgressStatus oldStatus = item->status();
                item->start();
                startedItemIds.append(item->id());
                oldStatuses.append(oldStatus);
            }
        }

        updateStatistics();
    }

    // 在锁外发射信号，避免死锁
    for (int i = 0; i < startedItemIds.size(); ++i)
    {
        const QString& itemId = startedItemIds[i];
        ProgressStatus oldStatus = oldStatuses[i];
        emit statusChanged(itemId, oldStatus, ProgressStatus::Running);
        emit itemStarted(itemId);
        emit itemUpdated(itemId);
    }
}

void ProgressManager::pauseAllItems()
{
    QStringList pausedItemIds;

    // 在锁的作用域内暂停项目
    {
        QMutexLocker locker(&m_mutex);

        for (auto item : m_items.values())
        {
            if (item->status() == ProgressStatus::Running)
            {
                item->pause();
                pausedItemIds.append(item->id());
            }
        }

        updateStatistics();
    }

    // 在锁外发射信号，避免死锁
    for (const QString& itemId : pausedItemIds)
    {
        emit statusChanged(itemId, ProgressStatus::Running, ProgressStatus::Paused);
        emit itemPaused(itemId);
        emit itemUpdated(itemId);
    }
}

void ProgressManager::cancelAllItems()
{
    QStringList cancelledItemIds;
    QList<ProgressStatus> oldStatuses;

    // 在锁的作用域内取消项目
    {
        QMutexLocker locker(&m_mutex);

        for (auto item : m_items.values())
        {
            if (item->status() == ProgressStatus::Running ||
                item->status() == ProgressStatus::Paused)
            {
                ProgressStatus oldStatus = item->status();
                item->cancel();
                cancelledItemIds.append(item->id());
                oldStatuses.append(oldStatus);
            }
        }

        updateStatistics();
    }

    // 在锁外发射信号，避免死锁
    for (int i = 0; i < cancelledItemIds.size(); ++i)
    {
        const QString& itemId = cancelledItemIds[i];
        ProgressStatus oldStatus = oldStatuses[i];
        emit statusChanged(itemId, oldStatus, ProgressStatus::Cancelled);
        emit itemCancelled(itemId);
        emit itemUpdated(itemId);
    }
}

void ProgressManager::clearCompletedItems()
{
    QMutexLocker locker(&m_mutex);

    QStringList toRemove;
    for (auto item : m_items.values())
    {
        if (item->status() == ProgressStatus::Completed)
        {
            toRemove.append(item->id());
        }
    }

    locker.unlock();

    for (const QString& itemId : toRemove)
    {
        removeProgressItem(itemId);
    }
}

void ProgressManager::clearErrorItems()
{
    QMutexLocker locker(&m_mutex);

    QStringList toRemove;
    for (auto item : m_items.values())
    {
        if (item->status() == ProgressStatus::Error)
        {
            toRemove.append(item->id());
        }
    }

    locker.unlock();

    for (const QString& itemId : toRemove)
    {
        removeProgressItem(itemId);
    }
}

// 统计信息方法
int ProgressManager::getTotalCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_totalCount;
}

int ProgressManager::getRunningCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_runningCount;
}

int ProgressManager::getPausedCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_pausedCount;
}

int ProgressManager::getCompletedCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_completedCount;
}

int ProgressManager::getErrorCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_errorCount;
}

int ProgressManager::getCancelledCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_cancelledCount;
}

// 查找方法
QList<ProgressItemPtr> ProgressManager::findItemsByTitle(const QString& title) const
{
    QMutexLocker locker(&m_mutex);
    QList<ProgressItemPtr> result;

    for (auto item : m_items.values())
    {
        if (item->title().contains(title, Qt::CaseInsensitive))
        {
            result.append(item);
        }
    }

    return result;
}

QList<ProgressItemPtr> ProgressManager::findItemsByStatus(ProgressStatus status) const
{
    QMutexLocker locker(&m_mutex);
    QList<ProgressItemPtr> result;

    for (auto item : m_items.values())
    {
        if (item->status() == status)
        {
            result.append(item);
        }
    }

    return result;
}

ProgressItemPtr ProgressManager::findItemByTitle(const QString& title) const
{
    QMutexLocker locker(&m_mutex);

    for (auto item : m_items.values())
    {
        if (item->title() == title)
        {
            return item;
        }
    }

    return nullptr;
}

// 公共槽函数
void ProgressManager::onStartRequested(const QString& itemId)
{
    startProgress(itemId);
}

void ProgressManager::onPauseRequested(const QString& itemId)
{
    pauseProgress(itemId);
}

void ProgressManager::onResumeRequested(const QString& itemId)
{
    resumeProgress(itemId);
}

void ProgressManager::onCancelRequested(const QString& itemId)
{
    cancelProgress(itemId);
}

void ProgressManager::onRemoveRequested(const QString& itemId)
{
    removeProgressItem(itemId);
}

// 私有槽函数
void ProgressManager::onStatisticsTimer()
{
    updateStatistics();
    emitStatisticsChanged();
}

void ProgressManager::processProgressUpdate(const QString& itemId, int current, const QString& message)
{
    updateProgress(itemId, current, message);
}

// 私有方法
void ProgressManager::initializeManager()
{
    // 初始化统计定时器
    m_statisticsTimer = new QTimer(this);
    connect(m_statisticsTimer, &QTimer::timeout, this, &ProgressManager::onStatisticsTimer);
    m_statisticsTimer->start(STATISTICS_UPDATE_INTERVAL);
}

void ProgressManager::updateStatistics()
{
    m_totalCount = m_items.size();
    m_runningCount = 0;
    m_pausedCount = 0;
    m_completedCount = 0;
    m_errorCount = 0;
    m_cancelledCount = 0;

    for (auto item : m_items.values())
    {
        switch (item->status())
        {
        case ProgressStatus::Running:
            m_runningCount++;
            break;
        case ProgressStatus::Paused:
            m_pausedCount++;
            break;
        case ProgressStatus::Completed:
            m_completedCount++;
            break;
        case ProgressStatus::Error:
            m_errorCount++;
            break;
        case ProgressStatus::Cancelled:
            m_cancelledCount++;
            break;
        default:
            break;
        }
    }
}

void ProgressManager::emitStatisticsChanged()
{
    emit statisticsChanged(m_totalCount, m_runningCount, m_pausedCount,
                           m_completedCount, m_errorCount, m_cancelledCount);
}

bool ProgressManager::isValidItemId(const QString& itemId) const
{
    return m_items.contains(itemId);
}

void ProgressManager::connectItemSignals(ProgressItemPtr item)
{
    // 这里可以连接进度项的内部信号，如果ProgressItem有信号的话
    // 目前ProgressItem是纯数据类，没有信号
}

void ProgressManager::disconnectItemSignals(ProgressItemPtr item)
{
    // 断开进度项的信号连接
    // 目前ProgressItem是纯数据类，没有信号需要断开
}

void ProgressManager::notifyItemUpdated(const QString& itemId)
{
    emit itemUpdated(itemId);
}

// updateProgressInternal 方法已被删除，因为它违反了锁外信号发射的原则

void ProgressManager::setStatusInternal(const QString& itemId, ProgressStatus status, const QString& message)
{
    ProgressStatus oldStatus = ProgressStatus::NotStarted;
    bool shouldEmitMessage = false;
    bool shouldEmitStatusChanged = false;
    bool shouldEmitStarted = false;
    bool shouldEmitResumed = false;
    bool shouldEmitPaused = false;
    bool shouldEmitCompleted = false;
    bool shouldEmitCancelled = false;
    bool shouldEmitError = false;
    QString errorMessage;

    // 在锁的作用域内更新状态
    {
        QMutexLocker locker(&m_mutex);

        if (m_items.contains(itemId))
        {
            ProgressItemPtr item = m_items[itemId];
            oldStatus = item->status();

            if (oldStatus != status)
            {
                item->setStatus(status);

                if (!message.isEmpty())
                {
                    item->setMessage(message);
                    shouldEmitMessage = true;
                }

                shouldEmitStatusChanged = true;

                // 确定需要发送的特定状态信号
                switch (status)
                {
                case ProgressStatus::Running:
                    if (oldStatus == ProgressStatus::Paused)
                    {
                        shouldEmitResumed = true;
                    }
                    else
                    {
                        shouldEmitStarted = true;
                    }
                    break;
                case ProgressStatus::Paused:
                    shouldEmitPaused = true;
                    break;
                case ProgressStatus::Completed:
                    shouldEmitCompleted = true;
                    break;
                case ProgressStatus::Cancelled:
                    shouldEmitCancelled = true;
                    break;
                case ProgressStatus::Error:
                    shouldEmitError = true;
                    errorMessage = message;
                    break;
                default:
                    break;
                }

                updateStatistics();
            }
        }
    }

    // 在锁外发射所有信号，避免死锁
    if (shouldEmitMessage)
    {
        emit messageUpdated(itemId, message);
    }
    if (shouldEmitStatusChanged)
    {
        emit statusChanged(itemId, oldStatus, status);
    }
    if (shouldEmitStarted)
    {
        emit itemStarted(itemId);
    }
    if (shouldEmitResumed)
    {
        emit itemResumed(itemId);
    }
    if (shouldEmitPaused)
    {
        emit itemPaused(itemId);
    }
    if (shouldEmitCompleted)
    {
        emit itemCompleted(itemId);
    }
    if (shouldEmitCancelled)
    {
        emit itemCancelled(itemId);
    }
    if (shouldEmitError)
    {
        emit itemError(itemId, errorMessage);
    }
    if (shouldEmitStatusChanged)
    {
        emit itemUpdated(itemId);
    }
}
