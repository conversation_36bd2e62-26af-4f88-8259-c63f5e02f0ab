#include "ProgressWidget.h"
#include <QApplication>
#include <QMessageBox>
#include <QInputDialog>
#include <QDateTime>
#include <QDebug>
#include <QStyle>

ProgressWidget::ProgressWidget(QWidget *parent)
    : QWidget(parent)
    , m_progressManager(ProgressManager::instance())
{
    setupUI();
    setupToolBar();
    setupMainContent();
    connectSignals();
    
    // 初始化模拟定时器
    m_simulationTimer = new QTimer(this);
    connect(m_simulationTimer, &QTimer::timeout, this, &ProgressWidget::onSimulationTimer);
}

ProgressWidget::~ProgressWidget()
{
    if (m_simulationTimer && m_simulationTimer->isActive()) {
        m_simulationTimer->stop();
    }
}

QString ProgressWidget::addProgressItem(const QString& title, 
                                       const QString& message,
                                       int total,
                                       const QString& parentId)
{
    return m_progressManager->createProgressItem(title, message, total, parentId);
}

void ProgressWidget::removeProgressItem(const QString& itemId)
{
    m_progressManager->removeProgressItem(itemId);
}

void ProgressWidget::updateProgressItem(const QString& itemId, int current, const QString& message)
{
    m_progressManager->updateProgress(itemId, current, message);
}

void ProgressWidget::addTestProgressItems()
{
    // 创建一些简单的测试进度项，避免复杂的父子关系
    QString task1 = addProgressItem("Download Files", "Downloading required files...", 100);
    QString task2 = addProgressItem("Compile Project", "Compiling source code...", 50);
    QString task3 = addProgressItem("Run Tests", "Running unit tests...", 200);
    QString task4 = addProgressItem("Package App", "Creating installer package...", 30);

    // 启动一些任务
    m_progressManager->startProgress(task1);
    m_progressManager->startProgress(task2);

    // 保存用于模拟的项目ID
    m_simulationItems.clear();
    m_simulationItems << task1 << task2 << task3 << task4;
}

void ProgressWidget::simulateProgress()
{
    if (m_simulationItems.isEmpty()) {
        addTestProgressItems();
    }
    
    if (!m_simulationTimer->isActive()) {
        m_simulationTimer->start(SIMULATION_INTERVAL);
        m_simulateButton->setText("Stop Simulation");
    } else {
        m_simulationTimer->stop();
        m_simulateButton->setText("Simulate Progress");
    }
}

// 工具栏操作槽函数
void ProgressWidget::onClearCompleted() const
{
    m_progressManager->clearCompletedItems();
}

void ProgressWidget::onClearAll()
{
    int ret = QMessageBox::question(this, "Clear All", 
                                   "Are you sure you want to clear all progress items?",
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        m_progressManager->clearAllItems();
        m_simulationItems.clear();
        if (m_simulationTimer->isActive()) {
            m_simulationTimer->stop();
            m_simulateButton->setText("Simulate Progress");
        }
    }
}

void ProgressWidget::onStartAll()
{
    m_progressManager->startAllItems();
}

void ProgressWidget::onPauseAll()
{
    m_progressManager->pauseAllItems();
}

void ProgressWidget::onCancelAll()
{
    int ret = QMessageBox::question(this, "Cancel All", 
                                   "Are you sure you want to cancel all running progress items?",
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        m_progressManager->cancelAllItems();
    }
}

void ProgressWidget::onExpandAll()
{
    m_progressList->expandAll();
}

void ProgressWidget::onCollapseAll()
{
    m_progressList->collapseAll();
}

void ProgressWidget::onRefresh()
{
    // 刷新显示
    for (auto item : m_progressManager->getAllItems()) {
        m_progressList->updateProgressItem(item->id());
    }
}

void ProgressWidget::onNewProgressItem()
{
    createNewItemDialog();
}

// 进度管理器信号处理槽函数
void ProgressWidget::onItemCreated(const QString& itemId)
{
    ProgressItemPtr item = m_progressManager->getProgressItem(itemId);
    if (item) {
        m_progressList->addProgressItem(item);
    }
}

void ProgressWidget::onItemRemoved(const QString& itemId)
{
    m_progressList->removeProgressItem(itemId);
}

void ProgressWidget::onItemUpdated(const QString& itemId)
{
    m_progressList->updateProgressItem(itemId);
}

void ProgressWidget::onProgressUpdated(const QString& itemId, int current, int total, double percentage)
{
    Q_UNUSED(current)
    Q_UNUSED(total)
    Q_UNUSED(percentage)
    m_progressList->updateProgressItem(itemId);
}

void ProgressWidget::onStatusChanged(const QString& itemId, ProgressStatus oldStatus, ProgressStatus newStatus)
{
    Q_UNUSED(oldStatus)
    m_progressList->updateProgressItem(itemId);
    
    // 在详情面板中显示状态变化
    ProgressItemPtr item = m_progressManager->getProgressItem(itemId);
    if (item) {
        QString statusText;
        switch (newStatus) {
            case ProgressStatus::Running:
                statusText = "Started";
                break;
            case ProgressStatus::Paused:
                statusText = "Paused";
                break;
            case ProgressStatus::Completed:
                statusText = "Completed";
                break;
            case ProgressStatus::Error:
                statusText = "Error";
                break;
            case ProgressStatus::Cancelled:
                statusText = "Cancelled";
                break;
            default:
                statusText = "Status Changed";
                break;
        }
        
        QString logEntry = QString("[%1] %2: %3\n")
                          .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                          .arg(item->title())
                          .arg(statusText);
        m_detailsPanel->append(logEntry);
    }
}

void ProgressWidget::onStatisticsChanged(int total, int running, int paused, int completed, int error, int cancelled)
{
    Q_UNUSED(paused)
    Q_UNUSED(cancelled)
    
    QString stats = QString("Total: %1 | Running: %2 | Completed: %3 | Error: %4")
                   .arg(total).arg(running).arg(completed).arg(error);
    m_statisticsLabel->setText(stats);
}

// 列表组件信号处理槽函数
void ProgressWidget::onStartRequested(const QString& itemId)
{
    m_progressManager->startProgress(itemId);
}

void ProgressWidget::onPauseRequested(const QString& itemId)
{
    m_progressManager->pauseProgress(itemId);
}

void ProgressWidget::onResumeRequested(const QString& itemId)
{
    m_progressManager->resumeProgress(itemId);
}

void ProgressWidget::onCancelRequested(const QString& itemId)
{
    m_progressManager->cancelProgress(itemId);
}

void ProgressWidget::onRemoveRequested(const QString& itemId)
{
    m_progressManager->removeProgressItem(itemId);
}

void ProgressWidget::onItemDoubleClicked(const QString& itemId)
{
    showItemDetails(itemId);
}

// 私有槽函数
void ProgressWidget::onSimulationTimer()
{
    // 模拟进度更新
    for (const QString& itemId : m_simulationItems) {
        ProgressItemPtr item = m_progressManager->getProgressItem(itemId);
        if (item && item->status() == ProgressStatus::Running) {
            int current = item->current();
            int total = item->total();
            
            if (current < total) {
                int increment = QRandomGenerator::global()->bounded(1, 6); // 随机增加1-5
                current = qMin(current + increment, total);
                
                QString message = QString("Processing... (%1/%2)").arg(current).arg(total);
                m_progressManager->updateProgress(itemId, current, message);
                
                if (current >= total) {
                    m_progressManager->completeProgress(itemId);
                }
            }
        }
    }
    
    // 检查是否所有任务都完成了
    bool allCompleted = true;
    for (const QString& itemId : m_simulationItems) {
        ProgressItemPtr item = m_progressManager->getProgressItem(itemId);
        if (item && item->status() != ProgressStatus::Completed) {
            allCompleted = false;
            break;
        }
    }
    
    if (allCompleted) {
        m_simulationTimer->stop();
        m_simulateButton->setText("Simulate Progress");
    }
}

// 私有方法实现
void ProgressWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);

    setLayout(m_mainLayout);
}

void ProgressWidget::setupToolBar()
{
    // 工具栏布局
    m_toolBarLayout = new QHBoxLayout();
    m_toolBarLayout->setSpacing(10);

    // 创建按钮
    m_newButton = new QPushButton("New Task", this);
    m_newButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_FileIcon));

    m_clearCompletedButton = new QPushButton("Clear Completed", this);
    m_clearCompletedButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_TrashIcon));

    m_clearAllButton = new QPushButton("Clear All", this);
    m_clearAllButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_DialogDiscardButton));

    m_startAllButton = new QPushButton("Start All", this);
    m_startAllButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_MediaPlay));

    m_pauseAllButton = new QPushButton("Pause All", this);
    m_pauseAllButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_MediaPause));

    m_cancelAllButton = new QPushButton("Cancel All", this);
    m_cancelAllButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_MediaStop));

    m_expandAllButton = new QPushButton("Expand All", this);
    m_expandAllButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_ArrowDown));

    m_collapseAllButton = new QPushButton("Collapse All", this);
    m_collapseAllButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_ArrowUp));

    m_refreshButton = new QPushButton("Refresh", this);
    m_refreshButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_BrowserReload));

    m_simulateButton = new QPushButton("Simulate Progress", this);
    m_simulateButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_MediaPlay));

    // 统计信息标签
    m_statisticsLabel = new QLabel("Total: 0 | Running: 0 | Completed: 0 | Error: 0", this);
    m_statisticsLabel->setStyleSheet("color: #666; font-size: 11px; padding: 5px;");

    // 添加到布局
    m_toolBarLayout->addWidget(m_newButton);
    m_toolBarLayout->addWidget(m_clearCompletedButton);
    m_toolBarLayout->addWidget(m_clearAllButton);
    m_toolBarLayout->addSpacing(20);
    m_toolBarLayout->addWidget(m_startAllButton);
    m_toolBarLayout->addWidget(m_pauseAllButton);
    m_toolBarLayout->addWidget(m_cancelAllButton);
    m_toolBarLayout->addSpacing(20);
    m_toolBarLayout->addWidget(m_expandAllButton);
    m_toolBarLayout->addWidget(m_collapseAllButton);
    m_toolBarLayout->addWidget(m_refreshButton);
    m_toolBarLayout->addSpacing(20);
    m_toolBarLayout->addWidget(m_simulateButton);
    m_toolBarLayout->addStretch();
    m_toolBarLayout->addWidget(m_statisticsLabel);

    m_mainLayout->addLayout(m_toolBarLayout);
}

void ProgressWidget::setupMainContent()
{
    // 创建分割器
    m_splitter = new QSplitter(Qt::Horizontal, this);

    // 创建进度列表
    m_progressList = new ProgressListWidget(this);
    m_splitter->addWidget(m_progressList);

    // 创建详情面板
    m_detailsPanel = new QTextEdit(this);
    m_detailsPanel->setReadOnly(true);
    m_detailsPanel->setMaximumWidth(300);
    m_detailsPanel->setPlaceholderText("Progress details and logs will appear here...");
    m_splitter->addWidget(m_detailsPanel);

    // 设置分割器比例
    m_splitter->setStretchFactor(0, 3);
    m_splitter->setStretchFactor(1, 1);

    m_mainLayout->addWidget(m_splitter, 1);
}

void ProgressWidget::connectSignals()
{
    // 连接工具栏按钮信号
    connect(m_newButton, &QPushButton::clicked, this, &ProgressWidget::onNewProgressItem);
    connect(m_clearCompletedButton, &QPushButton::clicked, this, &ProgressWidget::onClearCompleted);
    connect(m_clearAllButton, &QPushButton::clicked, this, &ProgressWidget::onClearAll);
    connect(m_startAllButton, &QPushButton::clicked, this, &ProgressWidget::onStartAll);
    connect(m_pauseAllButton, &QPushButton::clicked, this, &ProgressWidget::onPauseAll);
    connect(m_cancelAllButton, &QPushButton::clicked, this, &ProgressWidget::onCancelAll);
    connect(m_expandAllButton, &QPushButton::clicked, this, &ProgressWidget::onExpandAll);
    connect(m_collapseAllButton, &QPushButton::clicked, this, &ProgressWidget::onCollapseAll);
    connect(m_refreshButton, &QPushButton::clicked, this, &ProgressWidget::onRefresh);
    connect(m_simulateButton, &QPushButton::clicked, this, &ProgressWidget::simulateProgress);

    // 连接进度管理器信号
    connect(m_progressManager, &ProgressManager::itemCreated,
            this, &ProgressWidget::onItemCreated);
    connect(m_progressManager, &ProgressManager::itemRemoved,
            this, &ProgressWidget::onItemRemoved);
    connect(m_progressManager, &ProgressManager::itemUpdated,
            this, &ProgressWidget::onItemUpdated);
    connect(m_progressManager, &ProgressManager::progressUpdated,
            this, &ProgressWidget::onProgressUpdated);
    connect(m_progressManager, &ProgressManager::statusChanged,
            this, &ProgressWidget::onStatusChanged);
    connect(m_progressManager, &ProgressManager::statisticsChanged,
            this, &ProgressWidget::onStatisticsChanged);

    // 连接进度列表信号
    connect(m_progressList, &ProgressListWidget::startRequested,
            this, &ProgressWidget::onStartRequested);
    connect(m_progressList, &ProgressListWidget::pauseRequested,
            this, &ProgressWidget::onPauseRequested);
    connect(m_progressList, &ProgressListWidget::resumeRequested,
            this, &ProgressWidget::onResumeRequested);
    connect(m_progressList, &ProgressListWidget::cancelRequested,
            this, &ProgressWidget::onCancelRequested);
    connect(m_progressList, &ProgressListWidget::removeRequested,
            this, &ProgressWidget::onRemoveRequested);
    connect(m_progressList, &ProgressListWidget::itemDoubleClicked,
            this, &ProgressWidget::onItemDoubleClicked);
}

void ProgressWidget::showItemDetails(const QString& itemId)
{
    ProgressItemPtr item = m_progressManager->getProgressItem(itemId);
    if (!item) return;

    QString details = QString(
        "=== Progress Item Details ===\n"
        "ID: %1\n"
        "Title: %2\n"
        "Message: %3\n"
        "Progress: %4/%5 (%6%)\n"
        "Status: %7\n"
        "Start Time: %8\n"
        "End Time: %9\n"
        "Elapsed: %10\n"
        "Is Parent: %11\n"
        "Children Count: %12\n"
        "=============================\n\n"
    ).arg(item->id())
     .arg(item->title())
     .arg(item->message().isEmpty() ? "N/A" : item->message())
     .arg(item->current())
     .arg(item->total())
     .arg(QString::number(item->progressPercentage(), 'f', 1))
     .arg(static_cast<int>(item->status()))
     .arg(item->getFormattedStartTime())
     .arg(item->getFormattedEndTime())
     .arg(item->getElapsedTime())
     .arg(item->isParent() ? "Yes" : "No")
     .arg(item->children().size());

    m_detailsPanel->append(details);
}

void ProgressWidget::createNewItemDialog()
{
    bool ok;
    QString title = QInputDialog::getText(this, "New Progress Item",
                                         "Enter task title:", QLineEdit::Normal,
                                         "New Task", &ok);
    if (ok && !title.isEmpty()) {
        QString message = QInputDialog::getText(this, "New Progress Item",
                                               "Enter initial message (optional):", QLineEdit::Normal,
                                               "", &ok);
        if (ok) {
            int total = QInputDialog::getInt(this, "New Progress Item",
                                           "Enter total steps:", 100, 1, 10000, 1, &ok);
            if (ok) {
                addProgressItem(title, message, total);
            }
        }
    }
}
