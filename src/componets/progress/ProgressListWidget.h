#ifndef PROGRESSLISTWIDGET_H
#define PROGRESSLISTWIDGET_H

#include <QScrollArea>
#include <QComboBox>
#include <QLineEdit>
#include <QMap>
#include "ProgressItem.h"
#include "ProgressItemWidget.h"

/**
 * @brief 进度状态过滤器枚举
 */
enum class ProgressFilter {
    All,        // 全部
    Running,    // 运行中
    Paused,     // 暂停
    Completed,  // 已完成
    Error,      // 错误
    Active      // 活跃的（运行中+暂停）
};

/**
 * @brief 进度列表组件
 * 管理多个进度项的显示，支持层级结构、过滤、搜索等功能
 */
class ProgressListWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ProgressListWidget(QWidget *parent = nullptr);
    
    // 进度项管理
    void addProgressItem(ProgressItemPtr item);
    void removeProgressItem(const QString& itemId);
    void updateProgressItem(const QString& itemId);
    void clearAllItems();
    
    // 获取进度项
    ProgressItemPtr getProgressItem(const QString& itemId) const;
    QList<ProgressItemPtr> getAllItems() const;
    QList<ProgressItemPtr> getFilteredItems() const;
    
    // 过滤和搜索
    void setFilter(ProgressFilter filter);
    ProgressFilter getFilter() const { return m_currentFilter; }
    void setSearchText(const QString& text);
    QString getSearchText() const { return m_searchText; }
    
    // 展开/折叠
    void expandAll();
    void collapseAll();
    void expandItem(const QString& itemId);
    void collapseItem(const QString& itemId);
    
    // 统计信息
    int getTotalCount() const;
    int getRunningCount() const;
    int getCompletedCount() const;
    int getErrorCount() const;

signals:
    // 进度项操作信号
    void startRequested(const QString& itemId);
    void pauseRequested(const QString& itemId);
    void resumeRequested(const QString& itemId);
    void cancelRequested(const QString& itemId);
    void removeRequested(const QString& itemId);
    
    // 进度项事件信号
    void itemDoubleClicked(const QString& itemId);
    void itemSelectionChanged(const QString& itemId);
    
    // 统计信息变化信号
    void countChanged(int total, int running, int completed, int error);

public slots:
    // 工具栏操作
    void onClearCompleted();
    void onClearAll();
    void onExpandAll();
    void onCollapseAll();
    
    // 过滤和搜索
    void onFilterChanged(int index);
    void onSearchTextChanged(const QString& text);

private slots:
    void onItemStartRequested(const QString& itemId);
    void onItemPauseRequested(const QString& itemId);
    void onItemResumeRequested(const QString& itemId);
    void onItemCancelRequested(const QString& itemId);
    void onItemRemoveRequested(const QString& itemId);
    void onItemExpandToggled(const QString& itemId, bool expanded);
    void onItemDoubleClicked(const QString& itemId);
    void onRefreshTimer();

private:
    void setupUI();
    void setupToolBar();
    void setupScrollArea();
    void refreshDisplay();
    void updateItemVisibility();
    void updateStatistics();
    void addItemWidget(ProgressItemPtr item, int indentLevel = 0);
    void removeItemWidget(const QString& itemId);
    void updateItemWidget(const QString& itemId);
    void addItemToLayoutRecursive(ProgressItemPtr item, int indentLevel);
    bool shouldShowItem(ProgressItemPtr item) const;
    bool matchesSearchText(ProgressItemPtr item) const;
    void setItemExpanded(const QString& itemId, bool expanded);
    bool isItemExpanded(const QString& itemId) const;
    
    // 数据
    QMap<QString, ProgressItemPtr> m_items;
    QMap<QString, ProgressItemWidget*> m_itemWidgets;
    QMap<QString, bool> m_expandedStates;
    ProgressFilter m_currentFilter;
    QString m_searchText;
    
    // UI组件
    QVBoxLayout* m_mainLayout;
    
    // 工具栏
    QHBoxLayout* m_toolBarLayout;
    QComboBox* m_filterComboBox;
    QLineEdit* m_searchLineEdit;
    QPushButton* m_expandAllButton;
    QPushButton* m_collapseAllButton;
    QPushButton* m_clearCompletedButton;
    QPushButton* m_clearAllButton;
    
    // 统计信息
    QLabel* m_statisticsLabel;
    
    // 滚动区域
    QScrollArea* m_scrollArea;
    QWidget* m_scrollContent;
    QVBoxLayout* m_scrollLayout;
    
    // 空状态显示
    QLabel* m_emptyLabel;
    
    // 定时器
    QTimer* m_refreshTimer;
    
    // 常量
    static const int REFRESH_INTERVAL = 1000; // 1秒刷新一次
};

#endif // PROGRESSLISTWIDGET_H
