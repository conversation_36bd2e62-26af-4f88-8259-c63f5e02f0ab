#ifndef PROGRESSWIDGET_H
#define PROGRESSWIDGET_H

#include <QSplitter>
#include <QTextEdit>
#include <QRandomGenerator>
#include "ProgressListWidget.h"
#include "ProgressManager.h"
#include "ProgressItem.h"

/**
 * @brief 进度管理组件
 * 适合嵌入到标签页或其他容器中的进度管理界面
 * 不包含菜单栏、工具栏等QMainWindow特有的组件
 */
class ProgressWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ProgressWidget(QWidget *parent = nullptr);
    ~ProgressWidget() override;
    
    // 进度项管理
    QString addProgressItem(const QString& title, 
                           const QString& message = QString(),
                           int total = 100,
                           const QString& parentId = QString());
    
    void removeProgressItem(const QString& itemId);
    void updateProgressItem(const QString& itemId, int current, const QString& message = QString());
    
    // 便利方法
    void addTestProgressItems();
    void simulateProgress();

public slots:
    // 工具栏操作
    void onClearCompleted() const;
    void onClearAll();
    void onStartAll();
    void onPauseAll();
    void onCancelAll();
    void onExpandAll();
    void onCollapseAll();
    void onRefresh();
    void onNewProgressItem();
    
    // 进度管理器信号处理
    void onItemCreated(const QString& itemId);
    void onItemRemoved(const QString& itemId);
    void onItemUpdated(const QString& itemId);
    void onProgressUpdated(const QString& itemId, int current, int total, double percentage);
    void onStatusChanged(const QString& itemId, ProgressStatus oldStatus, ProgressStatus newStatus);
    void onStatisticsChanged(int total, int running, int paused, int completed, int error, int cancelled);
    
    // 列表组件信号处理
    void onStartRequested(const QString& itemId);
    void onPauseRequested(const QString& itemId);
    void onResumeRequested(const QString& itemId);
    void onCancelRequested(const QString& itemId);
    void onRemoveRequested(const QString& itemId);
    void onItemDoubleClicked(const QString& itemId);

private slots:
    void onSimulationTimer();

private:
    void setupUI();
    void setupToolBar();
    void setupMainContent();
    void connectSignals();
    void showItemDetails(const QString& itemId);
    void createNewItemDialog();
    
    // UI组件
    QVBoxLayout* m_mainLayout;
    
    // 工具栏区域
    QHBoxLayout* m_toolBarLayout;
    QPushButton* m_newButton;
    QPushButton* m_clearCompletedButton;
    QPushButton* m_clearAllButton;
    QPushButton* m_startAllButton;
    QPushButton* m_pauseAllButton;
    QPushButton* m_cancelAllButton;
    QPushButton* m_expandAllButton;
    QPushButton* m_collapseAllButton;
    QPushButton* m_refreshButton;
    QPushButton* m_simulateButton;
    
    // 统计信息
    QLabel* m_statisticsLabel;
    
    // 主内容区域
    QSplitter* m_splitter;
    ProgressListWidget* m_progressList;
    QTextEdit* m_detailsPanel;
    
    // 进度管理器
    ProgressManager* m_progressManager;
    
    // 模拟定时器
    QTimer* m_simulationTimer;
    QStringList m_simulationItems;
    
    // 常量
    static const int SIMULATION_INTERVAL = 500; // 500ms
};

#endif // PROGRESSWIDGET_H
