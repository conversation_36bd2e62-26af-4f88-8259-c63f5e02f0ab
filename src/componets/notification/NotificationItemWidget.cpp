#include "NotificationItemWidget.h"
#include <QApplication>
#include <QStyle>
#include <QFont>

NotificationItemWidget::NotificationItemWidget(const NotificationItem& item, QWidget *parent)
    : QWidget(parent)
    , m_item(item)
    , m_mainLayout(nullptr)
    , m_contentLayout(nullptr)
    , m_statusIconLabel(nullptr)
    , m_projectLabel(nullptr)
    , m_titleLabel(nullptr)
    , m_contentLabel(nullptr)
    , m_timeLabel(nullptr)
    , m_actionButton(nullptr)
    , m_actionMenu(nullptr)
    , m_deleteAction(nullptr)
{
    setupUI();
    setupActionMenu();
    updateUI();
}

void NotificationItemWidget::setupUI()
{
    // 创建主布局
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setContentsMargins(12, 8, 12, 8);
    m_mainLayout->setSpacing(12);
    
    // 状态图标
    m_statusIconLabel = new QLabel();
    m_statusIconLabel->setFixedSize(24, 24);
    m_statusIconLabel->setScaledContents(true);
    
    // 内容布局
    m_contentLayout = new QVBoxLayout();
    m_contentLayout->setSpacing(4);
    
    // 标题行布局
    QHBoxLayout* titleLayout = new QHBoxLayout();
    titleLayout->setSpacing(8);
    
    // 项目标签
    m_projectLabel = new QLabel();
    QFont projectFont = m_projectLabel->font();
    projectFont.setPointSize(projectFont.pointSize() - 1);
    projectFont.setBold(true);
    m_projectLabel->setFont(projectFont);
    m_projectLabel->setStyleSheet("color: #666666; background-color: #f0f0f0; padding: 2px 6px; border-radius: 3px;");
    
    // 标题标签
    m_titleLabel = new QLabel();
    QFont titleFont = m_titleLabel->font();
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    
    // 时间标签
    m_timeLabel = new QLabel();
    QFont timeFont = m_timeLabel->font();
    timeFont.setPointSize(timeFont.pointSize() - 1);
    m_timeLabel->setFont(timeFont);
    m_timeLabel->setStyleSheet("color: #888888;");
    
    // 操作按钮
    m_actionButton = new QPushButton();
    m_actionButton->setIcon(QApplication::style()->standardIcon(QStyle::SP_ArrowDown));
    m_actionButton->setFixedSize(24, 24);
    m_actionButton->setFlat(true);
    m_actionButton->setToolTip("操作");
    
    // 连接操作按钮信号
    connect(m_actionButton, &QPushButton::clicked, this, &NotificationItemWidget::onActionButtonClicked);
    
    // 组装标题行
    titleLayout->addWidget(m_projectLabel);
    titleLayout->addWidget(m_titleLabel);
    titleLayout->addStretch();
    titleLayout->addWidget(m_timeLabel);
    titleLayout->addWidget(m_actionButton);
    
    // 内容标签
    m_contentLabel = new QLabel();
    m_contentLabel->setWordWrap(true);
    m_contentLabel->setStyleSheet("color: #555555;");
    QFont contentFont = m_contentLabel->font();
    contentFont.setPointSize(contentFont.pointSize() - 1);
    m_contentLabel->setFont(contentFont);
    
    // 组装内容布局
    m_contentLayout->addLayout(titleLayout);
    m_contentLayout->addWidget(m_contentLabel);
    
    // 组装主布局
    m_mainLayout->addWidget(m_statusIconLabel);
    m_mainLayout->addLayout(m_contentLayout, 1);
    
    // 设置整体样式
    setStyleSheet("NotificationItemWidget { "
                  "background-color: white; "
                  "border: 1px solid #e0e0e0; "
                  "border-radius: 6px; "
                  "margin: 2px; "
                  "} "
                  "NotificationItemWidget:hover { "
                  "background-color: #f8f8f8; "
                  "border-color: #d0d0d0; "
                  "}");
}

void NotificationItemWidget::setupActionMenu()
{
    m_actionMenu = new QMenu(this);
    
    // 删除操作
    m_deleteAction = new QAction("删除", this);
    m_deleteAction->setIcon(QApplication::style()->standardIcon(QStyle::SP_TrashIcon));
    connect(m_deleteAction, &QAction::triggered, this, &NotificationItemWidget::onDeleteAction);
    
    m_actionMenu->addAction(m_deleteAction);
}

void NotificationItemWidget::updateUI()
{
    // 更新状态图标
    m_statusIconLabel->setPixmap(m_item.getStatusIcon().pixmap(24, 24));
    
    // 更新项目标签
    m_projectLabel->setText(m_item.project());
    
    // 更新标题
    m_titleLabel->setText(m_item.title());
    
    // 更新内容
    m_contentLabel->setText(m_item.content());
    
    // 更新时间
    m_timeLabel->setText(m_item.getFormattedTime());
    
    // 根据状态设置边框颜色
    QString borderColor = m_item.getStatusColor();
    setStyleSheet(QString("NotificationItemWidget { "
                         "background-color: white; "
                         "border-left: 4px solid %1; "
                         "border-top: 1px solid #e0e0e0; "
                         "border-right: 1px solid #e0e0e0; "
                         "border-bottom: 1px solid #e0e0e0; "
                         "border-radius: 6px; "
                         "margin: 2px; "
                         "} "
                         "NotificationItemWidget:hover { "
                         "background-color: #f8f8f8; "
                         "border-top-color: #d0d0d0; "
                         "border-right-color: #d0d0d0; "
                         "border-bottom-color: #d0d0d0; "
                         "}").arg(borderColor));
}

void NotificationItemWidget::updateNotificationItem(const NotificationItem& item)
{
    m_item = item;
    updateUI();
}

void NotificationItemWidget::onActionButtonClicked()
{
    // 在按钮下方显示菜单
    QPoint pos = m_actionButton->mapToGlobal(QPoint(0, m_actionButton->height()));
    m_actionMenu->exec(pos);
}

void NotificationItemWidget::onDeleteAction()
{
    emit deleteRequested(this);
}
