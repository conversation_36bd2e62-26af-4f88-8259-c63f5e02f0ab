#ifndef BALLOONNOTIFICATIONMANAGER_H
#define BALLOONNOTIFICATIONMANAGER_H

#include <QObject>
#include <QList>
#include "BalloonNotification.h"
#include "NotificationItem.h"

/**
 * @brief 气球通知管理器
 * 管理多个气球通知的显示位置和生命周期
 */
class BalloonNotificationManager : public QObject
{
    Q_OBJECT

public:
    explicit BalloonNotificationManager(QObject *parent = nullptr);
    
    // 显示气球通知
    void showBalloonNotification(const NotificationItem& item);
    
    // 显示粘性气球通知
    void showStickyBalloonNotification(const NotificationItem& item);
    
    // 清除所有气球通知
    void clearAllBalloons();
    
    // 获取当前气球数量
    int getBalloonCount() const { return m_balloons.count(); }

signals:
    // 气球通知被点击
    void balloonClicked(const NotificationItem& item);

private slots:
    void onBalloonClosed(BalloonNotification* balloon);
    void onBalloonClicked(const NotificationItem& item);
    void repositionBalloons();

private:
    QPoint calculateNextPosition();
    void addBalloon(BalloonNotification* balloon);
    void removeBalloon(BalloonNotification* balloon);
    
    QList<BalloonNotification*> m_balloons;
    QTimer* m_repositionTimer;
    
    // 显示位置配置
    static constexpr int BALLOON_MARGIN = 10;
    static constexpr int BALLOON_SPACING = 10;
    static constexpr int MAX_BALLOONS = 5; // 最大同时显示的气球数量
};

#endif // BALLOONNOTIFICATIONMANAGER_H
