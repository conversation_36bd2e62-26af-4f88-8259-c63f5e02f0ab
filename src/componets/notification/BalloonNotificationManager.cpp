#include "BalloonNotificationManager.h"
#include <QApplication>
#include <QDesktopWidget>
#include <QScreen>

BalloonNotificationManager::BalloonNotificationManager(QObject* parent)
    : QObject(parent)
      , m_repositionTimer(nullptr)
{
    // 创建重新定位定时器
    m_repositionTimer = new QTimer(this);
    m_repositionTimer->setSingleShot(true);
    m_repositionTimer->setInterval(100);
    connect(m_repositionTimer, &QTimer::timeout, this, &BalloonNotificationManager::repositionBalloons);
}

void BalloonNotificationManager::showBalloonNotification(const NotificationItem& item)
{
    // 如果已经达到最大数量，移除最旧的气球
    if (m_balloons.count() >= MAX_BALLOONS)
    {
        BalloonNotification* oldestBalloon = m_balloons.first();
        oldestBalloon->hideBalloon();
    }

    // 创建新的气球通知
    const auto balloon = new BalloonNotification(item, false);
    addBalloon(balloon);
}

void BalloonNotificationManager::showStickyBalloonNotification(const NotificationItem& item)
{
    // 粘性气球不受最大数量限制，但如果太多会自动清理旧的
    if (m_balloons.count() >= MAX_BALLOONS * 2)
    {
        // 清理一半的旧气球
        for (int i = 0; i < MAX_BALLOONS; ++i)
        {
            if (!m_balloons.isEmpty())
            {
                BalloonNotification* oldBalloon = m_balloons.first();
                oldBalloon->hideBalloon();
            }
        }
    }

    // 创建新的粘性气球通知
    const auto balloon = new BalloonNotification(item, true);
    addBalloon(balloon);
}

void BalloonNotificationManager::clearAllBalloons()
{
    // 隐藏所有气球
    for (BalloonNotification* balloon : m_balloons)
    {
        balloon->hideBalloon();
    }
}

void BalloonNotificationManager::addBalloon(BalloonNotification* balloon)
{
    // 连接信号
    connect(balloon, &BalloonNotification::notificationClosed,
            this, &BalloonNotificationManager::onBalloonClosed);
    connect(balloon, &BalloonNotification::notificationClicked,
            this, &BalloonNotificationManager::onBalloonClicked);

    // 添加到列表
    m_balloons.append(balloon);

    // 设置位置并显示
    balloon->setPosition(calculateNextPosition());
    balloon->showBalloon();

    // 延迟重新定位所有气球
    m_repositionTimer->start();
}

void BalloonNotificationManager::removeBalloon(BalloonNotification* balloon)
{
    m_balloons.removeOne(balloon);

    // 重新定位剩余的气球
    m_repositionTimer->start();
}

QPoint BalloonNotificationManager::calculateNextPosition()
{
    // 获取屏幕尺寸
    const QScreen* screen = QApplication::primaryScreen();
    const QRect screenGeometry = screen->availableGeometry();

    // 计算起始位置（屏幕右下角）
    int x = screenGeometry.right() - 320 - BALLOON_MARGIN; // 320是气球宽度
    int y = screenGeometry.bottom() - BALLOON_MARGIN;

    // 为每个现有气球向上偏移
    for (BalloonNotification* balloon : m_balloons)
    {
        y -= balloon->height() + BALLOON_SPACING;
    }

    // 确保不会超出屏幕顶部
    if (y < screenGeometry.top() + BALLOON_MARGIN)
    {
        y = screenGeometry.top() + BALLOON_MARGIN;
    }

    return {x, y};
}

void BalloonNotificationManager::onBalloonClosed(BalloonNotification* balloon)
{
    removeBalloon(balloon);
}

void BalloonNotificationManager::onBalloonClicked(const NotificationItem& item)
{
    emit balloonClicked(item);
}

void BalloonNotificationManager::repositionBalloons()
{
    // 重新计算所有气球的位置
    const QScreen* screen = QApplication::primaryScreen();
    const QRect screenGeometry = screen->availableGeometry();

    int x = screenGeometry.right() - 320 - BALLOON_MARGIN;
    int y = screenGeometry.bottom() - BALLOON_MARGIN;

    // 从底部开始向上排列
    for (int i = m_balloons.count() - 1; i >= 0; --i)
    {
        BalloonNotification* balloon = m_balloons[i];
        y -= balloon->height() + BALLOON_SPACING;

        // 确保不会超出屏幕顶部
        if (y < screenGeometry.top() + BALLOON_MARGIN)
        {
            y = screenGeometry.top() + BALLOON_MARGIN;
        }

        balloon->setPosition(QPoint(x, y));
    }
}
