#include "NotificationItem.h"
#include <QApplication>
#include <QStyle>

NotificationItem::NotificationItem()
    : m_status(NotificationStatus::Normal)
      , m_type(NotificationType::Silent)
      , m_timestamp(QDateTime::currentDateTime())
{
}

NotificationItem::NotificationItem(const QString& project,
                                   const QString& title,
                                   const QString& content,
                                   NotificationStatus status,
                                   NotificationType type)
    : m_project(project)
      , m_title(title)
      , m_content(content)
      , m_status(status)
      , m_type(type)
      , m_timestamp(QDateTime::currentDateTime())
{
}

QIcon NotificationItem::getStatusIcon() const
{
    QStyle* style = QApplication::style();

    switch (m_status)
    {
    case NotificationStatus::Error:
        return style->standardIcon(QStyle::SP_MessageBoxCritical);
    case NotificationStatus::Warning:
        return style->standardIcon(QStyle::SP_MessageBoxWarning);
    case NotificationStatus::Normal:
    default:
        return style->standardIcon(QStyle::SP_MessageBoxInformation);
    }
}

QString NotificationItem::getStatusColor() const
{
    switch (m_status)
    {
    case NotificationStatus::Error:
        return "#FF4444"; // 红色
    case NotificationStatus::Warning:
        return "#FFA500"; // 橙色
    case NotificationStatus::Normal:
    default:
        return "#4CAF50"; // 绿色
    }
}

QString NotificationItem::getFormattedTime() const
{
    QDateTime now = QDateTime::currentDateTime();
    qint64 secondsAgo = m_timestamp.secsTo(now);

    if (secondsAgo < 60)
    {
        return QString("%1秒前").arg(secondsAgo);
    }
    if (secondsAgo < 3600)
    {
        return QString("%1分钟前").arg(secondsAgo / 60);
    }
    if (secondsAgo < 86400)
    {
        return QString("%1小时前").arg(secondsAgo / 3600);
    }
    if (secondsAgo < 604800)
    {
        return QString("%1天前").arg(secondsAgo / 86400);
    }
    return m_timestamp.toString("yyyy-MM-dd hh:mm");
}
