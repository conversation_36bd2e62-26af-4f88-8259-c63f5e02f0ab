# Qt通知UI组件演示说明

## 🎯 功能演示

### 1. 主通知窗口
启动程序后，您将看到主通知窗口，包含：
- **工具栏**：过滤器、清空按钮、测试按钮
- **通知列表**：时间线方式显示的通知项
- **状态栏**：显示通知数量和气球数量

### 2. 通知类型演示

#### 📋 列表通知
- 显示在主窗口的滚动列表中
- 包含项目标签、标题、内容、时间
- 左侧彩色边框表示状态（绿色=普通，橙色=警告，红色=错误）
- 右上角操作按钮可删除通知

#### 🎈 气球通知
点击工具栏的"气球通知"按钮：
- 在屏幕右下角弹出气球窗口
- 5秒后自动消失
- 鼠标悬停时暂停消失计时
- 点击气球可将通知添加到主列表

#### 📌 粘性气球通知
点击工具栏的"粘性气球"按钮：
- 在屏幕右下角弹出粘性气球窗口
- 10秒后自动消失（比普通气球停留更久）
- 通常用于重要提醒
- 同样支持点击添加到主列表

### 3. 交互功能演示

#### 🔍 过滤功能
使用工具栏的过滤下拉框：
- **全部通知**：显示所有通知
- **普通**：只显示普通状态的通知
- **警告**：只显示警告状态的通知
- **错误**：只显示错误状态的通知

#### 🗑️ 删除功能
- 点击通知项右上角的操作按钮（下拉箭头）
- 选择"删除"菜单项
- 通知将从列表中移除

#### ➕ 添加测试通知
- **添加测试通知**：向主列表添加随机通知
- **气球通知**：显示随机气球通知
- **粘性气球**：显示随机粘性气球通知

#### 🧹 清空功能
点击"清空所有"按钮清空主列表中的所有通知

### 4. 视觉效果演示

#### 🎨 状态指示
- **普通通知**：绿色左边框 + 信息图标
- **警告通知**：橙色左边框 + 警告图标
- **错误通知**：红色左边框 + 错误图标

#### ✨ 动画效果
- 气球通知淡入淡出动画
- 鼠标悬停高亮效果
- 平滑的滚动和布局变化

#### 📱 响应式设计
- 窗口可调整大小
- 内容自适应布局
- 气球通知自动定位

### 5. 高级功能演示

#### 🔄 实时更新
- 时间显示自动更新（如"5分钟前"）
- 每分钟刷新一次时间显示

#### 📊 状态统计
- 状态栏显示当前通知数量
- 显示活跃的气球通知数量

#### 🎯 智能管理
- 气球通知自动堆叠排列
- 超过最大数量时自动清理旧通知
- 防止屏幕被过多气球占满

## 🚀 使用建议

1. **启动程序**：运行后会自动显示一些示例通知
2. **测试气球**：点击气球按钮查看弹出效果
3. **交互体验**：尝试悬停、点击、删除等操作
4. **过滤测试**：使用过滤器查看不同状态的通知
5. **集成开发**：参考代码结构集成到您的项目中

## 💡 开发提示

- 气球通知适合即时提醒
- 粘性气球适合重要警告
- 主列表适合历史记录
- 可根据需要调整自动消失时间
- 支持自定义样式和主题

## 🔧 自定义配置

可以通过修改常量来调整行为：
- `AUTO_HIDE_DURATION`：普通气球显示时间
- `STICKY_AUTO_HIDE_DURATION`：粘性气球显示时间
- `MAX_BALLOONS`：最大同时显示气球数量
- `BALLOON_WIDTH`：气球宽度
- `BALLOON_SPACING`：气球间距
