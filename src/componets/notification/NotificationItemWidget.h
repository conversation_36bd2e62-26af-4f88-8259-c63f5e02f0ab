#ifndef NOTIFICATIONITEMWIDGET_H
#define NOTIFICATIONITEMWIDGET_H

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QMenu>
#include <QAction>
#include "NotificationItem.h"

/**
 * @brief 通知项UI组件
 * 显示单个通知项，包含状态图标、标题、操作按钮、时间等
 */
class NotificationItemWidget : public QWidget
{
    Q_OBJECT

public:
    explicit NotificationItemWidget(const NotificationItem& item, QWidget *parent = nullptr);
    
    // 获取通知项数据
    const NotificationItem& getNotificationItem() const { return m_item; }
    
    // 更新通知项数据
    void updateNotificationItem(const NotificationItem& item);

signals:
    // 删除通知信号
    void deleteRequested(NotificationItemWidget* widget);

private slots:
    // 操作按钮点击槽函数
    void onActionButtonClicked();
    
    // 删除操作槽函数
    void onDeleteAction();

private:
    void setupUI();
    void updateUI();
    void setupActionMenu();
    
    NotificationItem m_item;
    
    // UI组件
    QHBoxLayout* m_mainLayout;
    QVBoxLayout* m_contentLayout;
    
    QLabel* m_statusIconLabel;      // 状态图标
    QLabel* m_projectLabel;         // 项目标签
    QLabel* m_titleLabel;           // 标题标签
    QLabel* m_contentLabel;         // 内容标签
    QLabel* m_timeLabel;            // 时间标签
    QPushButton* m_actionButton;    // 操作按钮
    
    QMenu* m_actionMenu;            // 操作菜单
    QAction* m_deleteAction;        // 删除操作
};

#endif // NOTIFICATIONITEMWIDGET_H
