#include "NotificationWindow.h"
#include <QApplication>
#include <QStyle>
#include <QRandomGenerator>
#include <QToolBar>
#include <QStatusBar>

NotificationWindow::NotificationWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_toolBar(nullptr)
    , m_clearAllAction(nullptr)
    , m_addTestAction(nullptr)
    , m_balloonTestAction(nullptr)
    , m_stickyBalloonTestAction(nullptr)
    , m_filterComboBox(nullptr)
    , m_notificationList(nullptr)
    , m_balloonManager(nullptr)
    , m_statusBar(nullptr)
    , m_countLabel(nullptr)
{
    setupUI();
    setupToolBar();
    setupStatusBar();

    // 创建气球通知管理器
    m_balloonManager = new BalloonNotificationManager(this);
    connect(m_balloonManager, &BalloonNotificationManager::balloonClicked,
            this, &NotificationWindow::onBalloonClicked);
    
    // 设置窗口属性
    setWindowTitle("通知中心");
    setMinimumSize(600, 400);
    resize(800, 600);
    
    // 添加一些测试通知
    addTestNotifications();
}

void NotificationWindow::setupUI()
{
    // 创建中央组件
    m_centralWidget = new QWidget();
    setCentralWidget(m_centralWidget);
    
    // 创建主布局
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    // 创建通知列表
    m_notificationList = new NotificationListWidget();
    
    // 连接信号
    connect(m_notificationList, &NotificationListWidget::notificationCountChanged,
            this, &NotificationWindow::onNotificationCountChanged);
    
    // 添加到布局
    m_mainLayout->addWidget(m_notificationList);
}

void NotificationWindow::setupToolBar()
{
    m_toolBar = addToolBar("主工具栏");
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    
    // 过滤下拉框
    m_filterComboBox = new QComboBox();
    m_filterComboBox->addItem("全部通知", -1);
    m_filterComboBox->addItem("普通", static_cast<int>(NotificationStatus::Normal));
    m_filterComboBox->addItem("警告", static_cast<int>(NotificationStatus::Warning));
    m_filterComboBox->addItem("错误", static_cast<int>(NotificationStatus::Error));
    
    connect(m_filterComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &NotificationWindow::onFilterChanged);
    
    m_toolBar->addWidget(new QLabel("过滤: "));
    m_toolBar->addWidget(m_filterComboBox);
    m_toolBar->addSeparator();
    
    // 清空所有通知操作
    m_clearAllAction = new QAction("清空所有", this);
    m_clearAllAction->setIcon(QApplication::style()->standardIcon(QStyle::SP_TrashIcon));
    m_clearAllAction->setToolTip("清空所有通知");
    connect(m_clearAllAction, &QAction::triggered, this, &NotificationWindow::onClearAllClicked);
    m_toolBar->addAction(m_clearAllAction);
    
    // 添加测试通知操作
    m_addTestAction = new QAction("添加测试通知", this);
    m_addTestAction->setIcon(QApplication::style()->standardIcon(QStyle::SP_FileIcon));
    m_addTestAction->setToolTip("添加一些测试通知");
    connect(m_addTestAction, &QAction::triggered, this, &NotificationWindow::onAddTestNotificationClicked);
    m_toolBar->addAction(m_addTestAction);

    // 气球通知测试操作
    m_balloonTestAction = new QAction("气球通知", this);
    m_balloonTestAction->setIcon(QApplication::style()->standardIcon(QStyle::SP_MessageBoxInformation));
    m_balloonTestAction->setToolTip("显示气球通知");
    connect(m_balloonTestAction, &QAction::triggered, this, &NotificationWindow::onShowBalloonTestClicked);
    m_toolBar->addAction(m_balloonTestAction);

    // 粘性气球通知测试操作
    m_stickyBalloonTestAction = new QAction("粘性气球", this);
    m_stickyBalloonTestAction->setIcon(QApplication::style()->standardIcon(QStyle::SP_MessageBoxWarning));
    m_stickyBalloonTestAction->setToolTip("显示粘性气球通知");
    connect(m_stickyBalloonTestAction, &QAction::triggered, this, &NotificationWindow::onShowStickyBalloonTestClicked);
    m_toolBar->addAction(m_stickyBalloonTestAction);
}

void NotificationWindow::setupStatusBar()
{
    m_statusBar = statusBar();
    
    m_countLabel = new QLabel("通知数量: 0");
    m_statusBar->addPermanentWidget(m_countLabel);
}

void NotificationWindow::addNotification(const NotificationItem& item) const
{
    m_notificationList->addNotification(item);
}

void NotificationWindow::showBalloonNotification(const NotificationItem& item) const
{
    m_balloonManager->showBalloonNotification(item);
}

void NotificationWindow::showStickyBalloonNotification(const NotificationItem& item) const
{
    m_balloonManager->showStickyBalloonNotification(item);
}

void NotificationWindow::addTestNotifications() const
{
    // 添加一些测试通知
    createTestNotification("项目A", "构建成功", "项目A已成功构建完成，所有测试通过。", NotificationStatus::Normal);
    createTestNotification("项目B", "编译警告", "项目B编译时发现3个警告，建议检查代码。", NotificationStatus::Warning);
    createTestNotification("项目C", "构建失败", "项目C构建失败，请检查依赖项和配置。", NotificationStatus::Error);
    createTestNotification("系统", "内存使用率高", "当前系统内存使用率已达到85%，建议关闭不必要的程序。", NotificationStatus::Warning);
    createTestNotification("网络", "连接正常", "网络连接已恢复正常，所有服务可用。", NotificationStatus::Normal);

    // 同时显示一些气球通知示例
    NotificationItem balloonItem("气球测试", "这是一个气球通知", "气球通知会在5秒后自动消失", NotificationStatus::Normal, NotificationType::Balloon);
    m_balloonManager->showBalloonNotification(balloonItem);

    NotificationItem stickyItem("粘性气球", "这是一个粘性气球通知", "粘性气球通知会停留更长时间", NotificationStatus::Warning, NotificationType::Sticky);
    m_balloonManager->showStickyBalloonNotification(stickyItem);
}

void NotificationWindow::createTestNotification(const QString& project, 
                                               const QString& title, 
                                               const QString& content,
                                               NotificationStatus status) const
{
    NotificationItem item(project, title, content, status, NotificationType::Silent);
    addNotification(item);
}

void NotificationWindow::onClearAllClicked() const
{
    m_notificationList->clearAllNotifications();
}

void NotificationWindow::onFilterChanged(int index) const
{
    int filterValue = m_filterComboBox->itemData(index).toInt();
    
    if (filterValue == -1) {
        // 显示所有通知
        m_notificationList->showAllNotifications();
    } else {
        // 按状态过滤
        auto status = static_cast<NotificationStatus>(filterValue);
        m_notificationList->filterByStatus(status);
    }
}

void NotificationWindow::onAddTestNotificationClicked() const
{
    // 随机添加一个测试通知
    QStringList projects = {"项目A", "项目B", "项目C", "系统", "网络"};
    QStringList titles = {"构建完成", "测试通过", "部署成功", "更新可用", "备份完成"};
    QStringList contents = {
        "所有任务已成功完成。",
        "检测到新的更新版本。",
        "系统运行正常。",
        "操作已成功执行。",
        "所有检查项目都通过了。"
    };
    
    QList<NotificationStatus> statuses = {
        NotificationStatus::Normal,
        NotificationStatus::Warning,
        NotificationStatus::Error
    };

    const int projectIndex = QRandomGenerator::global()->bounded(projects.size());
    const int titleIndex = QRandomGenerator::global()->bounded(titles.size());
    const int contentIndex = QRandomGenerator::global()->bounded(contents.size());
    const int statusIndex = QRandomGenerator::global()->bounded(statuses.size());
    
    createTestNotification(projects[projectIndex], 
                          titles[titleIndex], 
                          contents[contentIndex],
                          statuses[statusIndex]);
}

void NotificationWindow::onShowBalloonTestClicked() const
{
    // 创建随机气球通知
    QStringList titles = {"构建完成", "测试通过", "部署成功", "更新可用", "任务完成"};
    QStringList contents = {
        "所有任务已成功完成，可以进行下一步操作。",
        "检测到新的更新版本，建议及时更新。",
        "系统运行正常，所有服务可用。",
        "操作已成功执行，请查看结果。",
        "所有检查项目都通过了验证。"
    };

    const int titleIndex = QRandomGenerator::global()->bounded(titles.size());
    const int contentIndex = QRandomGenerator::global()->bounded(contents.size());

    NotificationItem item("气球通知", titles[titleIndex], contents[contentIndex],
                         NotificationStatus::Normal, NotificationType::Balloon);
    m_balloonManager->showBalloonNotification(item);
}

void NotificationWindow::onShowStickyBalloonTestClicked() const
{
    // 创建随机粘性气球通知
    QStringList titles = {"重要提醒", "系统警告", "安全提示", "更新通知", "备份提醒"};
    QStringList contents = {
        "发现重要安全更新，建议立即安装。",
        "系统资源使用率较高，请注意监控。",
        "检测到异常登录尝试，请检查安全设置。",
        "有新的功能更新可用，点击查看详情。",
        "定期备份提醒：建议备份重要数据。"
    };

    const int titleIndex = QRandomGenerator::global()->bounded(titles.size());
    const int contentIndex = QRandomGenerator::global()->bounded(contents.size());

    NotificationItem item("粘性气球", titles[titleIndex], contents[contentIndex],
                         NotificationStatus::Warning, NotificationType::Sticky);
    m_balloonManager->showStickyBalloonNotification(item);
}

void NotificationWindow::onNotificationCountChanged(const int count) const
{
    m_countLabel->setText(QString("通知数量: %1 | 气球数量: %2")
                         .arg(count)
                         .arg(m_balloonManager->getBalloonCount()));
}

void NotificationWindow::onBalloonClicked(const NotificationItem& item) const
{
    // 当气球通知被点击时，将其添加到主通知列表中
    m_notificationList->addNotification(item);
}
