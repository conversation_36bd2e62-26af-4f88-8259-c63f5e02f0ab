#include "BalloonNotification.h"
#include <QPainter>
#include <QPainterPath>
#include <QApplication>
#include <QStyle>
#include <QDesktopWidget>
#include <QScreen>

BalloonNotification::BalloonNotification(const NotificationItem& item, 
                                       bool isSticky, 
                                       QWidget *parent)
    : QWidget(parent)
    , m_item(item)
    , m_isSticky(isSticky)
    , m_isHovered(false)
    , m_mainLayout(nullptr)
    , m_headerLayout(nullptr)
    , m_statusIconLabel(nullptr)
    , m_titleLabel(nullptr)
    , m_closeButton(nullptr)
    , m_contentLabel(nullptr)
    , m_projectLabel(nullptr)
    , m_timeLabel(nullptr)
    , m_autoHideTimer(nullptr)
    , m_fadeAnimation(nullptr)
    , m_opacityEffect(nullptr)
{
    setupUI();
    setupAnimations();
    
    // 设置窗口属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_ShowWithoutActivating);
    
    setFixedWidth(BALLOON_WIDTH);
    setMinimumHeight(BALLOON_MIN_HEIGHT);
}

void BalloonNotification::setupUI()
{
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(15, 15, 15, 15);
    m_mainLayout->setSpacing(8);
    
    // 头部布局（状态图标 + 标题 + 关闭按钮）
    m_headerLayout = new QHBoxLayout();
    m_headerLayout->setSpacing(8);
    
    // 状态图标
    m_statusIconLabel = new QLabel();
    m_statusIconLabel->setFixedSize(20, 20);
    m_statusIconLabel->setPixmap(m_item.getStatusIcon().pixmap(20, 20));
    
    // 标题
    m_titleLabel = new QLabel(m_item.title());
    QFont titleFont = m_titleLabel->font();
    titleFont.setBold(true);
    titleFont.setPointSize(titleFont.pointSize() + 1);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setStyleSheet("color: #333333;");
    
    // 关闭按钮
    m_closeButton = new QPushButton("×");
    m_closeButton->setFixedSize(20, 20);
    m_closeButton->setFlat(true);
    m_closeButton->setStyleSheet(
        "QPushButton { "
        "border: none; "
        "background-color: transparent; "
        "color: #666666; "
        "font-size: 16px; "
        "font-weight: bold; "
        "} "
        "QPushButton:hover { "
        "background-color: #ff4444; "
        "color: white; "
        "border-radius: 10px; "
        "}"
    );
    connect(m_closeButton, &QPushButton::clicked, this, &BalloonNotification::onCloseButtonClicked);
    
    // 组装头部
    m_headerLayout->addWidget(m_statusIconLabel);
    m_headerLayout->addWidget(m_titleLabel);
    m_headerLayout->addStretch();
    m_headerLayout->addWidget(m_closeButton);
    
    // 项目标签
    m_projectLabel = new QLabel(m_item.project());
    QFont projectFont = m_projectLabel->font();
    projectFont.setPointSize(projectFont.pointSize() - 1);
    projectFont.setBold(true);
    m_projectLabel->setFont(projectFont);
    m_projectLabel->setStyleSheet(
        "color: #666666; "
        "background-color: #f0f0f0; "
        "padding: 2px 6px; "
        "border-radius: 3px;"
    );
    m_projectLabel->setAlignment(Qt::AlignCenter);
    m_projectLabel->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
    
    // 内容标签
    m_contentLabel = new QLabel(m_item.content());
    m_contentLabel->setWordWrap(true);
    m_contentLabel->setStyleSheet("color: #555555; line-height: 1.4;");
    QFont contentFont = m_contentLabel->font();
    contentFont.setPointSize(contentFont.pointSize());
    m_contentLabel->setFont(contentFont);
    
    // 时间标签
    m_timeLabel = new QLabel(m_item.getFormattedTime());
    QFont timeFont = m_timeLabel->font();
    timeFont.setPointSize(timeFont.pointSize() - 1);
    m_timeLabel->setFont(timeFont);
    m_timeLabel->setStyleSheet("color: #888888;");
    m_timeLabel->setAlignment(Qt::AlignRight);
    
    // 组装主布局
    m_mainLayout->addLayout(m_headerLayout);
    m_mainLayout->addWidget(m_projectLabel);
    m_mainLayout->addWidget(m_contentLabel);
    m_mainLayout->addWidget(m_timeLabel);
    
    updateStyleSheet();
}

void BalloonNotification::setupAnimations()
{
    // 透明度效果
    m_opacityEffect = new QGraphicsOpacityEffect(this);
    setGraphicsEffect(m_opacityEffect);
    
    // 淡入淡出动画
    m_fadeAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_fadeAnimation->setDuration(300);
    
    connect(m_fadeAnimation, &QPropertyAnimation::finished, 
            this, [this]() {
                if (m_opacityEffect->opacity() == 0.0) {
                    onFadeOutFinished();
                } else {
                    onFadeInFinished();
                }
            });
    
    // 自动隐藏定时器
    m_autoHideTimer = new QTimer(this);
    m_autoHideTimer->setSingleShot(true);
    connect(m_autoHideTimer, &QTimer::timeout, this, &BalloonNotification::onAutoHideTimeout);
}

void BalloonNotification::showBalloon()
{
    show();
    startFadeInAnimation();
    
    // 设置自动隐藏（如果不是粘性的或者鼠标悬停）
    if (!m_isHovered) {
        int duration = m_isSticky ? STICKY_AUTO_HIDE_DURATION : AUTO_HIDE_DURATION;
        m_autoHideTimer->start(duration);
    }
}

void BalloonNotification::hideBalloon()
{
    m_autoHideTimer->stop();
    startFadeOutAnimation();
}

void BalloonNotification::setPosition(const QPoint& position)
{
    move(position);
}

void BalloonNotification::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制气球背景
    QPainterPath path;
    QRect rect = this->rect().adjusted(5, 5, -5, -5);
    path.addRoundedRect(rect, 8, 8);
    
    // 绘制阴影
    painter.setPen(Qt::NoPen);
    painter.setBrush(QColor(0, 0, 0, 30));
    painter.drawPath(path.translated(2, 2));
    
    // 绘制背景
    QLinearGradient gradient(rect.topLeft(), rect.bottomLeft());
    gradient.setColorAt(0, QColor(255, 255, 255, 250));
    gradient.setColorAt(1, QColor(248, 248, 248, 250));
    painter.setBrush(gradient);
    painter.drawPath(path);
    
    // 绘制边框
    painter.setPen(QPen(QColor(m_item.getStatusColor()), 2));
    painter.setBrush(Qt::NoBrush);
    painter.drawPath(path);
}

void BalloonNotification::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit notificationClicked(m_item);
        hideBalloon();
    }
    QWidget::mousePressEvent(event);
}

void BalloonNotification::enterEvent(QEvent* event)
{
    m_isHovered = true;
    m_autoHideTimer->stop(); // 鼠标悬停时停止自动隐藏
    updateStyleSheet();
    QWidget::enterEvent(event);
}

void BalloonNotification::leaveEvent(QEvent* event)
{
    m_isHovered = false;
    // 鼠标离开后重新开始自动隐藏计时
    if (!m_isSticky) {
        m_autoHideTimer->start(AUTO_HIDE_DURATION);
    }
    updateStyleSheet();
    QWidget::leaveEvent(event);
}

void BalloonNotification::onCloseButtonClicked()
{
    hideBalloon();
}

void BalloonNotification::onAutoHideTimeout()
{
    if (!m_isHovered) {
        hideBalloon();
    }
}

void BalloonNotification::onFadeInFinished()
{
    // 淡入完成
}

void BalloonNotification::onFadeOutFinished()
{
    emit notificationClosed(this);
    deleteLater();
}

void BalloonNotification::startFadeInAnimation() const
{
    m_fadeAnimation->setStartValue(0.0);
    m_fadeAnimation->setEndValue(0.95);
    m_fadeAnimation->start();
}

void BalloonNotification::startFadeOutAnimation() const
{
    m_fadeAnimation->setStartValue(m_opacityEffect->opacity());
    m_fadeAnimation->setEndValue(0.0);
    m_fadeAnimation->start();
}

void BalloonNotification::updateStyleSheet()
{
    QString hoverStyle = m_isHovered ? "background-color: rgba(255, 255, 255, 0.98);" : "";
    setStyleSheet(QString("BalloonNotification { %1 }").arg(hoverStyle));
}
