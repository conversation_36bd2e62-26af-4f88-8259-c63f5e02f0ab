#include "NotificationListWidget.h"
#include <QScrollBar>
#include <QTimer>

NotificationListWidget::NotificationListWidget(QWidget *parent)
    : QWidget(parent)
    , m_scroll<PERSON>rea(nullptr)
    , m_contentWidget(nullptr)
    , m_contentLayout(nullptr)
    , m_timeUpdateTimer(nullptr)
    , m_currentFilter(NotificationStatus::Normal)
    , m_showAll(true)
{
    setupUI();
    
    // 设置定时器，每分钟更新一次时间显示
    m_timeUpdateTimer = new QTimer(this);
    connect(m_timeUpdateTimer, &QTimer::timeout, this, &NotificationListWidget::updateTimeDisplay);
    m_timeUpdateTimer->start(60000); // 60秒
}

void NotificationListWidget::setupUI()
{
    auto* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    
    // 创建滚动区域
    m_scrollArea = new QScrollArea();
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    
    // 创建内容容器
    m_contentWidget = new QWidget();
    m_contentLayout = new QVBoxLayout(m_contentWidget);
    m_contentLayout->setContentsMargins(8, 8, 8, 8);
    m_contentLayout->setSpacing(8);
    m_contentLayout->addStretch(); // 在底部添加弹性空间
    
    m_scrollArea->setWidget(m_contentWidget);
    mainLayout->addWidget(m_scrollArea);
    
    // 设置样式
    setStyleSheet("QScrollArea { "
                  "border: none; "
                  "background-color: #f5f5f5; "
                  "} "
                  "QScrollArea > QWidget > QWidget { "
                  "background-color: #f5f5f5; "
                  "}");
}

void NotificationListWidget::addNotification(const NotificationItem& item)
{
    // 创建通知项组件
    NotificationItemWidget* widget = new NotificationItemWidget(item, m_contentWidget);
    
    // 连接删除信号
    connect(widget, &NotificationItemWidget::deleteRequested, 
            this, &NotificationListWidget::onNotificationDeleteRequested);
    
    // 添加到列表
    m_notificationWidgets.append(widget);
    
    // 插入到布局中（在stretch之前）
    int insertIndex = m_contentLayout->count() - 1; // stretch在最后
    m_contentLayout->insertWidget(insertIndex, widget);
    
    // 滚动到顶部显示新通知
    QTimer::singleShot(10, [this]() {
        m_scrollArea->verticalScrollBar()->setValue(0);
    });
    
    // 发出通知数量变化信号
    emit notificationCountChanged(m_notificationWidgets.count());
}

void NotificationListWidget::removeNotification(NotificationItemWidget* widget)
{
    if (!widget || !m_notificationWidgets.contains(widget)) {
        return;
    }
    
    // 从列表中移除
    m_notificationWidgets.removeOne(widget);
    
    // 从布局中移除
    m_contentLayout->removeWidget(widget);
    
    // 删除组件
    widget->deleteLater();
    
    // 发出通知数量变化信号
    emit notificationCountChanged(m_notificationWidgets.count());
}

void NotificationListWidget::clearAllNotifications()
{
    // 删除所有通知组件
    for (NotificationItemWidget* widget : m_notificationWidgets) {
        m_contentLayout->removeWidget(widget);
        widget->deleteLater();
    }
    
    m_notificationWidgets.clear();
    
    // 发出通知数量变化信号
    emit notificationCountChanged(0);
}

int NotificationListWidget::getNotificationCount() const
{
    return m_notificationWidgets.count();
}

void NotificationListWidget::filterByStatus(NotificationStatus status)
{
    m_currentFilter = status;
    m_showAll = false;
    
    for (NotificationItemWidget* widget : m_notificationWidgets) {
        bool shouldShow = (widget->getNotificationItem().status() == status);
        widget->setVisible(shouldShow);
    }
}

void NotificationListWidget::showAllNotifications()
{
    m_showAll = true;
    
    for (NotificationItemWidget* widget : m_notificationWidgets) {
        widget->setVisible(true);
    }
}

void NotificationListWidget::onNotificationDeleteRequested(NotificationItemWidget* widget)
{
    removeNotification(widget);
}

void NotificationListWidget::updateTimeDisplay()
{
    // 更新所有通知项的时间显示
    for (NotificationItemWidget* widget : m_notificationWidgets) {
        widget->updateNotificationItem(widget->getNotificationItem());
    }
}
