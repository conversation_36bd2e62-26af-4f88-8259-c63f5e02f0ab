#ifndef NOTIFICATIONWINDOW_H
#define NOTIFICATIONWINDOW_H

#include <QMainWindow>
#include <QComboBox>
#include "NotificationListWidget.h"
#include "NotificationItem.h"
#include "BalloonNotificationManager.h"

/**
 * @brief 主通知窗口
 * 整合所有通知组件，提供完整的通知管理界面
 */
class NotificationWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit NotificationWindow(QWidget *parent = nullptr);
    
    // 添加通知
    void addNotification(const NotificationItem& item) const;
    
    // 添加测试通知（用于演示）
    void addTestNotifications() const;

    // 显示气球通知
    void showBalloonNotification(const NotificationItem& item) const;

    // 显示粘性气球通知
    void showStickyBalloonNotification(const NotificationItem& item) const;

private slots:
    // 工具栏操作槽函数
    void onClearAllClicked() const;
    void onFilterChanged(int index) const;
    void onAddTestNotificationClicked() const;
    void onShowBalloonTestClicked() const;
    void onShowStickyBalloonTestClicked() const;

    // 通知数量变化槽函数
    void onNotificationCountChanged(int count) const;

    // 气球通知槽函数
    void onBalloonClicked(const NotificationItem& item) const;

private:
    void setupUI();
    void setupToolBar();
    void setupStatusBar();
    void createTestNotification(const QString& project, 
                               const QString& title, 
                               const QString& content,
                               NotificationStatus status) const;
    
    // UI组件
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    
    QToolBar* m_toolBar;
    QAction* m_clearAllAction;
    QAction* m_addTestAction;
    QAction* m_balloonTestAction;
    QAction* m_stickyBalloonTestAction;
    QComboBox* m_filterComboBox;
    
    NotificationListWidget* m_notificationList;
    BalloonNotificationManager* m_balloonManager;

    QStatusBar* m_statusBar;
    QLabel* m_countLabel;
};

#endif // NOTIFICATIONWINDOW_H
