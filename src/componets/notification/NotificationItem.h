#ifndef NOTIFICATIONITEM_H
#define NOTIFICATIONITEM_H

#include <QString>
#include <QDateTime>
#include <QIcon>

/**
 * @brief 通知状态枚举
 */
enum class NotificationStatus
{
    Normal, // 普通
    Warning, // 警告
    Error // 错误
};

/**
 * @brief 通知方式枚举
 */
enum class NotificationType
{
    Silent, // 无弹窗
    Balloon, // 气球
    Sticky // 粘性气球
};

/**
 * @brief 通知项数据模型
 */
class NotificationItem
{
public:
    NotificationItem();
    NotificationItem(const QString& project,
                     const QString& title,
                     const QString& content,
                     NotificationStatus status = NotificationStatus::Normal,
                     NotificationType type = NotificationType::Silent);

    // Getters
    const QString& project() const { return m_project; }
    const QString& title() const { return m_title; }
    const QString& content() const { return m_content; }
    const NotificationStatus& status() const { return m_status; }
    const NotificationType& type() const { return m_type; }
    const QDateTime& timestamp() const { return m_timestamp; }

    // Setters
    void setProject(const QString& project) { m_project = project; }
    void setTitle(const QString& title) { m_title = title; }
    void setContent(const QString& content) { m_content = content; }
    void setStatus(NotificationStatus status) { m_status = status; }
    void setType(NotificationType type) { m_type = type; }
    void setTimestamp(const QDateTime& timestamp) { m_timestamp = timestamp; }

    // 获取状态图标
    QIcon getStatusIcon() const;

    // 获取状态颜色
    QString getStatusColor() const;

    // 获取格式化的时间字符串
    QString getFormattedTime() const;

private:
    QString m_project; // 项目名称
    QString m_title; // 标题
    QString m_content; // 内容
    NotificationStatus m_status; // 状态
    NotificationType m_type; // 通知方式
    QDateTime m_timestamp; // 时间戳
};

#endif // NOTIFICATIONITEM_H
