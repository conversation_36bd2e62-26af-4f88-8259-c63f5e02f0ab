#ifndef BALLOONNOTIFICATION_H
#define BALLOONNOTIFICATION_H

#include <QLabel>
#include <QPushButton>
#include <QHBoxLayout>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QMouseEvent>
#include "NotificationItem.h"

/**
 * @brief 气球通知组件
 * 实现弹出式气球通知效果，支持自动消失和粘性显示
 */
class BalloonNotification : public QWidget
{
    Q_OBJECT

public:
    explicit BalloonNotification(const NotificationItem& item, 
                                bool isSticky = false, 
                                QWidget *parent = nullptr);
    
    // 显示气球通知
    void showBalloon();
    
    // 隐藏气球通知
    void hideBalloon();
    
    // 设置显示位置
    void setPosition(const QPoint& position);
    
    // 获取通知项
    const NotificationItem& getNotificationItem() const { return m_item; }

signals:
    // 通知被点击
    void notificationClicked(const NotificationItem& item);
    
    // 通知被关闭
    void notificationClosed(BalloonNotification* balloon);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void enterEvent(QEvent* event) override;
    void leaveEvent(QEvent* event) override;

private slots:
    void onCloseButtonClicked();
    void onAutoHideTimeout();
    void onFadeInFinished();
    void onFadeOutFinished();

private:
    void setupUI();
    void setupAnimations();
    void startFadeInAnimation() const;
    void startFadeOutAnimation() const;
    void updateStyleSheet();
    
    NotificationItem m_item;
    bool m_isSticky;
    bool m_isHovered;
    
    // UI组件
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_headerLayout;
    QLabel* m_statusIconLabel;
    QLabel* m_titleLabel;
    QPushButton* m_closeButton;
    QLabel* m_contentLabel;
    QLabel* m_projectLabel;
    QLabel* m_timeLabel;
    
    // 动画和定时器
    QTimer* m_autoHideTimer;
    QPropertyAnimation* m_fadeAnimation;
    QGraphicsOpacityEffect* m_opacityEffect;
    
    // 常量
    static constexpr int BALLOON_WIDTH = 320;
    static constexpr int BALLOON_MIN_HEIGHT = 100;
    static constexpr int AUTO_HIDE_DURATION = 5000; // 5秒
    static constexpr int STICKY_AUTO_HIDE_DURATION = 10000; // 10秒
};

#endif // BALLOONNOTIFICATION_H
