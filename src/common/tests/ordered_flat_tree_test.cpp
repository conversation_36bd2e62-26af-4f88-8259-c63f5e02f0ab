#include <iostream>
#include <cassert>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <atomic>
#include "../OrderedFlatTree.h"

using namespace common;

/**
 * @brief OrderedFlatTree的基础功能测试
 */
class OrderedFlatTreeTest {
public:
    static void runAllTests() {
        std::cout << "开始运行有序扁平化树测试..." << std::endl;
        
        testBasicOperations();
        testOrderedTraversal();
        testRangeQuery();
        testDescendants();
        testMultiThreading();
        testPerformance();
        
        std::cout << "所有有序扁平化树测试通过！" << std::endl;
    }

private:
    static void testBasicOperations() {
        std::cout << "测试基本操作..." << std::endl;
        
        OrderedFlatTree<int, std::string> tree;
        
        // 测试空树
        assert(tree.empty());
        assert(tree.size() == 0);
        
        // 测试插入根节点
        assert(tree.insertRoot(1, "root"));
        assert(!tree.empty());
        assert(tree.size() == 1);
        assert(tree.contains(1));
        
        // 测试重复插入根节点
        assert(!tree.insertRoot(1, "duplicate"));
        assert(tree.size() == 1);
        
        // 测试插入子节点
        assert(tree.insertChild(1, 2, "child1"));
        assert(tree.insertChild(1, 3, "child2"));
        assert(tree.size() == 3);
        
        // 测试查找
        auto data = tree.findNode(2);
        assert(data.has_value());
        assert(*data == "child1");
        
        auto missing = tree.findNode(999);
        assert(!missing.has_value());
        
        // 测试包含检查
        assert(tree.contains(1));
        assert(tree.contains(2));
        assert(!tree.contains(999));
        
        std::cout << "基本操作测试通过" << std::endl;
    }
    
    static void testOrderedTraversal() {
        std::cout << "测试有序遍历..." << std::endl;
        
        OrderedFlatTree<int, std::string> tree;
        
        // 插入无序的键
        std::vector<int> keys = {5, 2, 8, 1, 3, 7, 9};
        tree.insertRoot(keys[0], "root_" + std::to_string(keys[0]));
        
        for (size_t i = 1; i < keys.size(); ++i) {
            tree.insertChild(keys[0], keys[i], "data_" + std::to_string(keys[i]));
        }
        
        // 测试有序遍历
        std::vector<int> ordered_result;
        tree.traverseOrdered([&ordered_result](const int& key, const std::string& data) {
            ordered_result.push_back(key);
        });
        
        // 验证结果是有序的
        std::vector<int> expected = {1, 2, 3, 5, 7, 8, 9};
        assert(ordered_result == expected);
        
        std::cout << "有序遍历测试通过" << std::endl;
    }
    
    static void testRangeQuery() {
        std::cout << "测试范围查询..." << std::endl;
        
        OrderedFlatTree<int, std::string> tree;
        tree.insertRoot(5, "root");
        
        // 插入更多节点
        for (int i = 1; i <= 10; ++i) {
            if (i != 5) {
                tree.insertChild(5, i, "data_" + std::to_string(i));
            }
        }
        
        // 测试范围查询 [3, 7]
        std::vector<int> range_result;
        tree.rangeQuery(3, 7, [&range_result](const int& key, const std::string& data) {
            range_result.push_back(key);
        });
        
        // 验证结果
        std::vector<int> expected = {3, 4, 5, 6, 7};
        assert(range_result == expected);
        
        std::cout << "范围查询测试通过" << std::endl;
    }
    
    static void testDescendants() {
        std::cout << "测试后代查找..." << std::endl;
        
        OrderedFlatTree<int, std::string> tree;
        setupSampleTree(tree);
        
        // 测试BFS后代查找
        auto descendants = tree.getDescendantsBFS(1);
        std::vector<int> expected = {2, 3, 4, 5, 6};
        assert(descendants == expected);
        
        descendants = tree.getDescendantsBFS(3);
        expected = {5, 6};
        assert(descendants == expected);
        
        descendants = tree.getDescendantsBFS(4);
        assert(descendants.empty());
        
        // 测试DFS后代查找
        descendants = tree.getDescendantsDFS(1);
        expected = {2, 4, 3, 5, 6};
        assert(descendants == expected);
        
        std::cout << "后代查找测试通过" << std::endl;
    }
    
    static void testMultiThreading() {
        std::cout << "测试多线程安全..." << std::endl;
        
        OrderedFlatTree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        const int num_threads = 4;
        const int operations_per_thread = 100;
        
        std::vector<std::thread> threads;
        std::atomic<int> success_count{0};
        
        // 并发插入测试
        for (int t = 0; t < num_threads; ++t) {
            threads.emplace_back([&tree, t, operations_per_thread, &success_count]() {
                for (int i = 0; i < operations_per_thread; ++i) {
                    int key = t * operations_per_thread + i + 1;
                    if (tree.insertChild(0, key, "data_" + std::to_string(key))) {
                        success_count.fetch_add(1);
                    }
                }
            });
        }
        
        for (auto& thread : threads) {
            thread.join();
        }
        
        assert(success_count.load() == num_threads * operations_per_thread);
        assert(tree.size() == static_cast<size_t>(num_threads * operations_per_thread + 1));
        
        // 测试并发查找
        threads.clear();
        std::atomic<int> found_count{0};
        
        for (int t = 0; t < num_threads; ++t) {
            threads.emplace_back([&tree, t, operations_per_thread, &found_count]() {
                for (int i = 0; i < operations_per_thread; ++i) {
                    int key = t * operations_per_thread + i + 1;
                    if (tree.findNode(key).has_value()) {
                        found_count.fetch_add(1);
                    }
                }
            });
        }
        
        for (auto& thread : threads) {
            thread.join();
        }
        
        assert(found_count.load() == num_threads * operations_per_thread);
        
        std::cout << "多线程安全测试通过" << std::endl;
    }
    
    static void testPerformance() {
        std::cout << "测试性能..." << std::endl;
        
        const int num_nodes = 10000;
        
        OrderedFlatTree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        auto start = std::chrono::high_resolution_clock::now();
        
        // 构建大树
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;  // 构建近似平衡的二叉树
            tree.insertChild(parent, i, "node_" + std::to_string(i));
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        assert(tree.size() == static_cast<size_t>(num_nodes));
        std::cout << "插入 " << num_nodes << " 个节点耗时: " << duration.count() << "ms" << std::endl;
        
        // 测试有序遍历性能
        start = std::chrono::high_resolution_clock::now();
        
        std::atomic<int> count{0};
        tree.traverseOrdered([&count](const int& key, const std::string& data) {
            count.fetch_add(1);
        });
        
        end = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        assert(count.load() == num_nodes);
        std::cout << "有序遍历 " << num_nodes << " 个节点耗时: " << duration.count() << "ms" << std::endl;
        
        // 测试范围查询性能
        start = std::chrono::high_resolution_clock::now();
        
        std::atomic<int> range_count{0};
        tree.rangeQuery(num_nodes / 4, num_nodes * 3 / 4, 
            [&range_count](const int& key, const std::string& data) {
                range_count.fetch_add(1);
            });
        
        end = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        std::cout << "范围查询 " << range_count.load() << " 个节点耗时: " << duration.count() << "ms" << std::endl;
        
        // 测试后代查找性能
        start = std::chrono::high_resolution_clock::now();
        
        auto descendants = tree.getDescendantsBFS(0);
        
        end = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        assert(descendants.size() == static_cast<size_t>(num_nodes - 1));
        std::cout << "查找根节点的所有后代耗时: " << duration.count() << "ms" << std::endl;
        
        // 测试统计信息
        auto stats = tree.getStats();
        std::cout << "树统计信息:" << std::endl;
        std::cout << "  总节点数: " << stats.total_nodes << std::endl;
        std::cout << "  有效节点数: " << stats.valid_nodes << std::endl;
        std::cout << "  根节点数: " << stats.root_nodes << std::endl;
        std::cout << "  叶子节点数: " << stats.leaf_nodes << std::endl;
        std::cout << "  内部节点数: " << stats.internal_nodes << std::endl;
        
        // 测试深度
        std::cout << "  最大深度: " << tree.getMaxDepth() << std::endl;
        
        std::cout << "性能测试通过" << std::endl;
    }
    
    static void setupSampleTree(OrderedFlatTree<int, std::string>& tree) {
        tree.insertRoot(1, "root");
        tree.insertChild(1, 2, "child1");
        tree.insertChild(1, 3, "child2");
        tree.insertChild(2, 4, "grandchild1");
        tree.insertChild(3, 5, "grandchild2");
        tree.insertChild(3, 6, "grandchild3");
    }
};

int main() {
    try {
        OrderedFlatTreeTest::runAllTests();
        std::cout << "\n=== 所有有序扁平化树测试成功完成 ===" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "测试失败: 未知错误" << std::endl;
        return 1;
    }
}
