#include <iostream>
#include <chrono>
#include <vector>
#include <random>
#include <iomanip>
#include <thread>
#include <atomic>
#include <algorithm>
#include <fstream>
#include "../Tree.h"
#include "../ThreadSafeTree.h"
#include "../FlatTree.h"
#include "../OrderedFlatTree.h"

using namespace common;
using namespace std::chrono;

/**
 * @brief 四种树结构的全面性能对比测试（公平对比 - 都支持多线程）
 */
class FourTreeComparison {
public:
    struct TestResult {
        std::string test_name;
        int test_size;
        double original_time_ms;
        double threadsafe_time_ms;
        double flat_time_ms;
        double ordered_time_ms;
        std::string winner;
        std::string notes;
    };

    static void runAllComparisons() {
        std::cout << "=== 四种树结构公平性能对比测试（都支持多线程） ===" << std::endl;
        printSystemInfo();
        
        std::vector<TestResult> results;
        std::vector<int> test_sizes = {100, 500, 1000, 5000, 10000, 50000};
        
        for (int size : test_sizes) {
            std::cout << "\n测试规模: " << size << " 个节点" << std::endl;
            std::cout << std::string(70, '=') << std::endl;
            
            results.push_back(testInsertion(size));
            results.push_back(testRandomSearch(size));
            results.push_back(testSequentialSearch(size));
            results.push_back(testTraversal(size));
            results.push_back(testDescendantSearch(size));
        }
        
        // 多线程专项测试
        std::cout << "\n多线程专项测试" << std::endl;
        std::cout << std::string(70, '=') << std::endl;
        results.push_back(testMultiThreadInsertion());
        results.push_back(testMultiThreadSearch());
        results.push_back(testParallelBatchOperations());
        
        generateDetailedReport(results);
        generateSummaryReport(results);
    }

private:
    static void printSystemInfo() {
        std::cout << "测试环境信息:" << std::endl;
        std::cout << "  CPU核心数: " << std::thread::hardware_concurrency() << std::endl;
        std::cout << "  编译器优化: -O3 -DNDEBUG" << std::endl;
        std::cout << "  C++标准: C++17" << std::endl;
        std::cout << "  测试特点: 所有树结构都支持多线程，公平对比" << std::endl;
        std::cout << std::endl;
    }
    
    static TestResult testInsertion(int num_nodes) {
        TestResult result;
        result.test_name = "插入操作";
        result.test_size = num_nodes;
        
        // 测试原始Tree（单线程基准）
        auto start = high_resolution_clock::now();
        {
            Tree<int, std::string> tree;
            tree.insertRoot(0, "root");
            
            for (int i = 1; i < num_nodes; ++i) {
                int parent = (i - 1) / 2;
                tree.insertChild(parent, i, "data_" + std::to_string(i));
            }
        }
        auto end = high_resolution_clock::now();
        result.original_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试ThreadSafeTree
        start = high_resolution_clock::now();
        {
            ThreadSafeTree<int, std::string> tree;
            tree.insertRoot(0, "root");
            
            for (int i = 1; i < num_nodes; ++i) {
                int parent = (i - 1) / 2;
                tree.insertChild(parent, i, "data_" + std::to_string(i));
            }
        }
        end = high_resolution_clock::now();
        result.threadsafe_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试FlatTree (unordered_map)
        start = high_resolution_clock::now();
        {
            FlatTree<int, std::string> tree;
            tree.insertRoot(0, "root");
            
            for (int i = 1; i < num_nodes; ++i) {
                int parent = (i - 1) / 2;
                tree.insertChild(parent, i, "data_" + std::to_string(i));
            }
        }
        end = high_resolution_clock::now();
        result.flat_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试OrderedFlatTree (std::map)
        start = high_resolution_clock::now();
        {
            OrderedFlatTree<int, std::string> tree;
            tree.insertRoot(0, "root");
            
            for (int i = 1; i < num_nodes; ++i) {
                int parent = (i - 1) / 2;
                tree.insertChild(parent, i, "data_" + std::to_string(i));
            }
        }
        end = high_resolution_clock::now();
        result.ordered_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 确定获胜者
        double min_time = std::min({result.original_time_ms, result.threadsafe_time_ms, 
                                   result.flat_time_ms, result.ordered_time_ms});
        if (min_time == result.original_time_ms) result.winner = "原始树";
        else if (min_time == result.threadsafe_time_ms) result.winner = "线程安全树";
        else if (min_time == result.flat_time_ms) result.winner = "扁平树(hash)";
        else result.winner = "扁平树(ordered)";
        
        std::cout << "插入测试: 原始=" << std::fixed << std::setprecision(2) 
                  << result.original_time_ms << "ms, 线程安全=" << result.threadsafe_time_ms
                  << "ms, 扁平(hash)=" << result.flat_time_ms 
                  << "ms, 扁平(ordered)=" << result.ordered_time_ms << "ms [" 
                  << result.winner << " 获胜]" << std::endl;
        
        return result;
    }
    
    static TestResult testRandomSearch(int num_nodes) {
        TestResult result;
        result.test_name = "随机查找";
        result.test_size = num_nodes;
        
        // 预先构建四种树
        Tree<int, std::string> original_tree;
        ThreadSafeTree<int, std::string> threadsafe_tree;
        FlatTree<int, std::string> flat_tree;
        OrderedFlatTree<int, std::string> ordered_tree;
        
        buildSampleTrees(original_tree, threadsafe_tree, flat_tree, ordered_tree, num_nodes);
        
        // 生成随机查找序列
        std::vector<int> search_keys = generateRandomKeys(num_nodes / 10, num_nodes);
        
        // 测试原始Tree
        auto start = high_resolution_clock::now();
        for (int key : search_keys) {
            auto node = original_tree.findNode(key);
            (void)node;
        }
        auto end = high_resolution_clock::now();
        result.original_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试ThreadSafeTree
        start = high_resolution_clock::now();
        for (int key : search_keys) {
            auto node = threadsafe_tree.findNode(key);
            (void)node;
        }
        end = high_resolution_clock::now();
        result.threadsafe_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试FlatTree
        start = high_resolution_clock::now();
        for (int key : search_keys) {
            auto data = flat_tree.findNode(key);
            (void)data;
        }
        end = high_resolution_clock::now();
        result.flat_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试OrderedFlatTree
        start = high_resolution_clock::now();
        for (int key : search_keys) {
            auto data = ordered_tree.findNode(key);
            (void)data;
        }
        end = high_resolution_clock::now();
        result.ordered_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 确定获胜者
        double min_time = std::min({result.original_time_ms, result.threadsafe_time_ms, 
                                   result.flat_time_ms, result.ordered_time_ms});
        if (min_time == result.original_time_ms) result.winner = "原始树";
        else if (min_time == result.threadsafe_time_ms) result.winner = "线程安全树";
        else if (min_time == result.flat_time_ms) result.winner = "扁平树(hash)";
        else result.winner = "扁平树(ordered)";
        
        std::cout << "随机查找: 原始=" << std::fixed << std::setprecision(2) 
                  << result.original_time_ms << "ms, 线程安全=" << result.threadsafe_time_ms
                  << "ms, 扁平(hash)=" << result.flat_time_ms 
                  << "ms, 扁平(ordered)=" << result.ordered_time_ms << "ms [" 
                  << result.winner << " 获胜]" << std::endl;
        
        return result;
    }
    
    static TestResult testSequentialSearch(int num_nodes) {
        TestResult result;
        result.test_name = "顺序查找";
        result.test_size = num_nodes;
        
        // 预先构建四种树
        Tree<int, std::string> original_tree;
        ThreadSafeTree<int, std::string> threadsafe_tree;
        FlatTree<int, std::string> flat_tree;
        OrderedFlatTree<int, std::string> ordered_tree;
        
        buildSampleTrees(original_tree, threadsafe_tree, flat_tree, ordered_tree, num_nodes);
        
        // 生成顺序查找序列
        std::vector<int> search_keys;
        for (int i = 0; i < num_nodes; i += 10) {
            search_keys.push_back(i);
        }
        
        // 测试所有四种树
        auto start = high_resolution_clock::now();
        for (int key : search_keys) {
            auto node = original_tree.findNode(key);
            (void)node;
        }
        auto end = high_resolution_clock::now();
        result.original_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        start = high_resolution_clock::now();
        for (int key : search_keys) {
            auto node = threadsafe_tree.findNode(key);
            (void)node;
        }
        end = high_resolution_clock::now();
        result.threadsafe_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        start = high_resolution_clock::now();
        for (int key : search_keys) {
            auto data = flat_tree.findNode(key);
            (void)data;
        }
        end = high_resolution_clock::now();
        result.flat_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        start = high_resolution_clock::now();
        for (int key : search_keys) {
            auto data = ordered_tree.findNode(key);
            (void)data;
        }
        end = high_resolution_clock::now();
        result.ordered_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 确定获胜者
        double min_time = std::min({result.original_time_ms, result.threadsafe_time_ms, 
                                   result.flat_time_ms, result.ordered_time_ms});
        if (min_time == result.original_time_ms) result.winner = "原始树";
        else if (min_time == result.threadsafe_time_ms) result.winner = "线程安全树";
        else if (min_time == result.flat_time_ms) result.winner = "扁平树(hash)";
        else result.winner = "扁平树(ordered)";
        
        std::cout << "顺序查找: 原始=" << std::fixed << std::setprecision(2) 
                  << result.original_time_ms << "ms, 线程安全=" << result.threadsafe_time_ms
                  << "ms, 扁平(hash)=" << result.flat_time_ms 
                  << "ms, 扁平(ordered)=" << result.ordered_time_ms << "ms [" 
                  << result.winner << " 获胜]" << std::endl;
        
        return result;
    }
    
    static TestResult testTraversal(int num_nodes) {
        TestResult result;
        result.test_name = "BFS遍历";
        result.test_size = num_nodes;
        
        // 预先构建四种树
        Tree<int, std::string> original_tree;
        ThreadSafeTree<int, std::string> threadsafe_tree;
        FlatTree<int, std::string> flat_tree;
        OrderedFlatTree<int, std::string> ordered_tree;
        
        buildSampleTrees(original_tree, threadsafe_tree, flat_tree, ordered_tree, num_nodes);
        
        // 测试原始Tree
        std::atomic<int> count1{0};
        auto start = high_resolution_clock::now();
        original_tree.traverseBFS([&count1](const auto& node) {
            count1.fetch_add(1);
        });
        auto end = high_resolution_clock::now();
        result.original_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试ThreadSafeTree
        std::atomic<int> count2{0};
        start = high_resolution_clock::now();
        threadsafe_tree.traverseBFS([&count2](const auto& node) {
            count2.fetch_add(1);
        });
        end = high_resolution_clock::now();
        result.threadsafe_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试FlatTree
        std::atomic<int> count3{0};
        start = high_resolution_clock::now();
        flat_tree.traverseBFS([&count3](const int& key, const std::string& data) {
            count3.fetch_add(1);
        });
        end = high_resolution_clock::now();
        result.flat_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试OrderedFlatTree
        std::atomic<int> count4{0};
        start = high_resolution_clock::now();
        ordered_tree.traverseBFS([&count4](const int& key, const std::string& data) {
            count4.fetch_add(1);
        });
        end = high_resolution_clock::now();
        result.ordered_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 确定获胜者
        double min_time = std::min({result.original_time_ms, result.threadsafe_time_ms, 
                                   result.flat_time_ms, result.ordered_time_ms});
        if (min_time == result.original_time_ms) result.winner = "原始树";
        else if (min_time == result.threadsafe_time_ms) result.winner = "线程安全树";
        else if (min_time == result.flat_time_ms) result.winner = "扁平树(hash)";
        else result.winner = "扁平树(ordered)";
        
        std::cout << "BFS遍历: 原始=" << std::fixed << std::setprecision(2) 
                  << result.original_time_ms << "ms, 线程安全=" << result.threadsafe_time_ms
                  << "ms, 扁平(hash)=" << result.flat_time_ms 
                  << "ms, 扁平(ordered)=" << result.ordered_time_ms << "ms [" 
                  << result.winner << " 获胜]" << std::endl;
        
        return result;
    }
    
    static TestResult testDescendantSearch(int num_nodes) {
        TestResult result;
        result.test_name = "后代查找";
        result.test_size = num_nodes;
        
        // 预先构建四种树
        Tree<int, std::string> original_tree;
        ThreadSafeTree<int, std::string> threadsafe_tree;
        FlatTree<int, std::string> flat_tree;
        OrderedFlatTree<int, std::string> ordered_tree;
        
        buildSampleTrees(original_tree, threadsafe_tree, flat_tree, ordered_tree, num_nodes);
        
        // 测试所有四种树
        auto start = high_resolution_clock::now();
        auto descendants1 = original_tree.getDescendantsBFS(0);
        auto end = high_resolution_clock::now();
        result.original_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        start = high_resolution_clock::now();
        auto descendants2 = threadsafe_tree.getDescendantsBFS(0);
        end = high_resolution_clock::now();
        result.threadsafe_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        start = high_resolution_clock::now();
        auto descendants3 = flat_tree.getDescendantsBFS(0);
        end = high_resolution_clock::now();
        result.flat_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        start = high_resolution_clock::now();
        auto descendants4 = ordered_tree.getDescendantsBFS(0);
        end = high_resolution_clock::now();
        result.ordered_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 确定获胜者
        double min_time = std::min({result.original_time_ms, result.threadsafe_time_ms, 
                                   result.flat_time_ms, result.ordered_time_ms});
        if (min_time == result.original_time_ms) result.winner = "原始树";
        else if (min_time == result.threadsafe_time_ms) result.winner = "线程安全树";
        else if (min_time == result.flat_time_ms) result.winner = "扁平树(hash)";
        else result.winner = "扁平树(ordered)";
        
        std::cout << "后代查找: 原始=" << std::fixed << std::setprecision(2) 
                  << result.original_time_ms << "ms, 线程安全=" << result.threadsafe_time_ms
                  << "ms, 扁平(hash)=" << result.flat_time_ms 
                  << "ms, 扁平(ordered)=" << result.ordered_time_ms << "ms [" 
                  << result.winner << " 获胜]" << std::endl;
        
        return result;
    }
    
    static TestResult testMultiThreadInsertion() {
        TestResult result;
        result.test_name = "多线程插入";
        result.test_size = 8000;  // 8线程 × 1000操作
        
        const int num_threads = std::thread::hardware_concurrency();
        const int operations_per_thread = 1000;
        
        // 测试ThreadSafeTree的多线程插入
        ThreadSafeTree<int, std::string> threadsafe_tree;
        threadsafe_tree.insertRoot(0, "root");
        
        auto start = high_resolution_clock::now();
        {
            std::vector<std::thread> threads;
            std::atomic<int> success_count{0};
            
            for (int t = 0; t < num_threads; ++t) {
                threads.emplace_back([&threadsafe_tree, &success_count, t, operations_per_thread]() {
                    for (int i = 0; i < operations_per_thread; ++i) {
                        int key = t * operations_per_thread + i + 1;
                        if (threadsafe_tree.insertChild(0, key, "data_" + std::to_string(key))) {
                            success_count.fetch_add(1);
                        }
                    }
                });
            }
            for (auto& thread : threads) {
                thread.join();
            }
        }
        auto end = high_resolution_clock::now();
        result.threadsafe_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试FlatTree的多线程插入
        FlatTree<int, std::string> flat_tree;
        flat_tree.insertRoot(0, "root");
        
        start = high_resolution_clock::now();
        {
            std::vector<std::thread> threads;
            std::atomic<int> success_count{0};
            
            for (int t = 0; t < num_threads; ++t) {
                threads.emplace_back([&flat_tree, &success_count, t, operations_per_thread]() {
                    for (int i = 0; i < operations_per_thread; ++i) {
                        int key = t * operations_per_thread + i + 1;
                        if (flat_tree.insertChild(0, key, "data_" + std::to_string(key))) {
                            success_count.fetch_add(1);
                        }
                    }
                });
            }
            for (auto& thread : threads) {
                thread.join();
            }
        }
        end = high_resolution_clock::now();
        result.flat_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试OrderedFlatTree的多线程插入
        OrderedFlatTree<int, std::string> ordered_tree;
        ordered_tree.insertRoot(0, "root");
        
        start = high_resolution_clock::now();
        {
            std::vector<std::thread> threads;
            std::atomic<int> success_count{0};
            
            for (int t = 0; t < num_threads; ++t) {
                threads.emplace_back([&ordered_tree, &success_count, t, operations_per_thread]() {
                    for (int i = 0; i < operations_per_thread; ++i) {
                        int key = t * operations_per_thread + i + 1;
                        if (ordered_tree.insertChild(0, key, "data_" + std::to_string(key))) {
                            success_count.fetch_add(1);
                        }
                    }
                });
            }
            for (auto& thread : threads) {
                thread.join();
            }
        }
        end = high_resolution_clock::now();
        result.ordered_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        result.original_time_ms = -1;  // 原始树不支持多线程
        result.winner = "N/A";
        
        // 确定获胜者（排除原始树）
        double min_time = std::min({result.threadsafe_time_ms, result.flat_time_ms, result.ordered_time_ms});
        if (min_time == result.threadsafe_time_ms) result.winner = "线程安全树";
        else if (min_time == result.flat_time_ms) result.winner = "扁平树(hash)";
        else result.winner = "扁平树(ordered)";
        
        result.notes = "原始树不支持多线程";
        
        std::cout << "多线程插入: 线程安全=" << std::fixed << std::setprecision(2) 
                  << result.threadsafe_time_ms << "ms, 扁平(hash)=" << result.flat_time_ms 
                  << "ms, 扁平(ordered)=" << result.ordered_time_ms << "ms [" 
                  << result.winner << " 获胜]" << std::endl;
        
        return result;
    }
    
    static TestResult testMultiThreadSearch() {
        TestResult result;
        result.test_name = "多线程查找";
        result.test_size = 10000;
        
        // 预先构建树
        ThreadSafeTree<int, std::string> threadsafe_tree;
        FlatTree<int, std::string> flat_tree;
        OrderedFlatTree<int, std::string> ordered_tree;
        
        // 构建测试数据
        threadsafe_tree.insertRoot(0, "root");
        flat_tree.insertRoot(0, "root");
        ordered_tree.insertRoot(0, "root");
        
        for (int i = 1; i < 10000; ++i) {
            threadsafe_tree.insertChild(0, i, "data_" + std::to_string(i));
            flat_tree.insertChild(0, i, "data_" + std::to_string(i));
            ordered_tree.insertChild(0, i, "data_" + std::to_string(i));
        }
        
        const int num_threads = std::thread::hardware_concurrency();
        const int searches_per_thread = 1000;
        
        // 测试ThreadSafeTree并发查找
        auto start = high_resolution_clock::now();
        {
            std::vector<std::thread> threads;
            std::atomic<int> found_count{0};
            
            for (int t = 0; t < num_threads; ++t) {
                threads.emplace_back([&threadsafe_tree, &found_count, t, searches_per_thread]() {
                    for (int i = 0; i < searches_per_thread; ++i) {
                        int key = (t * searches_per_thread + i) % 10000;
                        if (threadsafe_tree.findNode(key)) {
                            found_count.fetch_add(1);
                        }
                    }
                });
            }
            for (auto& thread : threads) {
                thread.join();
            }
        }
        auto end = high_resolution_clock::now();
        result.threadsafe_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试FlatTree并发查找
        start = high_resolution_clock::now();
        {
            std::vector<std::thread> threads;
            std::atomic<int> found_count{0};
            
            for (int t = 0; t < num_threads; ++t) {
                threads.emplace_back([&flat_tree, &found_count, t, searches_per_thread]() {
                    for (int i = 0; i < searches_per_thread; ++i) {
                        int key = (t * searches_per_thread + i) % 10000;
                        if (flat_tree.findNode(key)) {
                            found_count.fetch_add(1);
                        }
                    }
                });
            }
            for (auto& thread : threads) {
                thread.join();
            }
        }
        end = high_resolution_clock::now();
        result.flat_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试OrderedFlatTree并发查找
        start = high_resolution_clock::now();
        {
            std::vector<std::thread> threads;
            std::atomic<int> found_count{0};
            
            for (int t = 0; t < num_threads; ++t) {
                threads.emplace_back([&ordered_tree, &found_count, t, searches_per_thread]() {
                    for (int i = 0; i < searches_per_thread; ++i) {
                        int key = (t * searches_per_thread + i) % 10000;
                        if (ordered_tree.findNode(key)) {
                            found_count.fetch_add(1);
                        }
                    }
                });
            }
            for (auto& thread : threads) {
                thread.join();
            }
        }
        end = high_resolution_clock::now();
        result.ordered_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        result.original_time_ms = -1;  // 原始树不支持多线程
        
        // 确定获胜者
        double min_time = std::min({result.threadsafe_time_ms, result.flat_time_ms, result.ordered_time_ms});
        if (min_time == result.threadsafe_time_ms) result.winner = "线程安全树";
        else if (min_time == result.flat_time_ms) result.winner = "扁平树(hash)";
        else result.winner = "扁平树(ordered)";
        
        result.notes = "原始树不支持多线程";
        
        std::cout << "多线程查找: 线程安全=" << std::fixed << std::setprecision(2) 
                  << result.threadsafe_time_ms << "ms, 扁平(hash)=" << result.flat_time_ms 
                  << "ms, 扁平(ordered)=" << result.ordered_time_ms << "ms [" 
                  << result.winner << " 获胜]" << std::endl;
        
        return result;
    }
    
    static TestResult testParallelBatchOperations() {
        TestResult result;
        result.test_name = "并行批量操作";
        result.test_size = 10000;
        
        const int batch_size = 10000;
        
        // 准备批量数据
        std::vector<std::tuple<int, int, std::string>> batch_data;
        for (int i = 1; i <= batch_size; ++i) {
            batch_data.emplace_back(0, i, "data_" + std::to_string(i));
        }
        
        // 测试ThreadSafeTree并行批量插入
        ThreadSafeTree<int, std::string> threadsafe_tree;
        threadsafe_tree.insertRoot(0, "root");
        
        auto start = high_resolution_clock::now();
        size_t inserted1 = threadsafe_tree.parallelBatchInsert(batch_data);
        auto end = high_resolution_clock::now();
        result.threadsafe_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试FlatTree并行批量插入
        FlatTree<int, std::string> flat_tree;
        flat_tree.insertRoot(0, "root");
        
        start = high_resolution_clock::now();
        size_t inserted2 = flat_tree.parallelBatchInsert(batch_data);
        end = high_resolution_clock::now();
        result.flat_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 测试OrderedFlatTree并行批量插入
        OrderedFlatTree<int, std::string> ordered_tree;
        ordered_tree.insertRoot(0, "root");
        
        start = high_resolution_clock::now();
        size_t inserted3 = ordered_tree.parallelBatchInsert(batch_data);
        end = high_resolution_clock::now();
        result.ordered_time_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        result.original_time_ms = -1;  // 原始树不支持并行批量操作
        
        // 确定获胜者
        double min_time = std::min({result.threadsafe_time_ms, result.flat_time_ms, result.ordered_time_ms});
        if (min_time == result.threadsafe_time_ms) result.winner = "线程安全树";
        else if (min_time == result.flat_time_ms) result.winner = "扁平树(hash)";
        else result.winner = "扁平树(ordered)";
        
        result.notes = "原始树不支持并行批量操作";
        
        std::cout << "并行批量操作: 线程安全=" << std::fixed << std::setprecision(2) 
                  << result.threadsafe_time_ms << "ms, 扁平(hash)=" << result.flat_time_ms 
                  << "ms, 扁平(ordered)=" << result.ordered_time_ms << "ms [" 
                  << result.winner << " 获胜]" << std::endl;
        
        return result;
    }
    
    static void buildSampleTrees(Tree<int, std::string>& original_tree,
                                ThreadSafeTree<int, std::string>& threadsafe_tree,
                                FlatTree<int, std::string>& flat_tree,
                                OrderedFlatTree<int, std::string>& ordered_tree,
                                int num_nodes) {
        // 构建原始树
        original_tree.insertRoot(0, "root");
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;
            original_tree.insertChild(parent, i, "data_" + std::to_string(i));
        }
        
        // 构建线程安全树
        threadsafe_tree.insertRoot(0, "root");
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;
            threadsafe_tree.insertChild(parent, i, "data_" + std::to_string(i));
        }
        
        // 构建扁平树
        flat_tree.insertRoot(0, "root");
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;
            flat_tree.insertChild(parent, i, "data_" + std::to_string(i));
        }
        
        // 构建有序扁平树
        ordered_tree.insertRoot(0, "root");
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;
            ordered_tree.insertChild(parent, i, "data_" + std::to_string(i));
        }
    }
    
    static std::vector<int> generateRandomKeys(int count, int max_value) {
        std::vector<int> keys;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, max_value - 1);
        
        for (int i = 0; i < count; ++i) {
            keys.push_back(dis(gen));
        }
        
        return keys;
    }
    
    static void generateDetailedReport(const std::vector<TestResult>& results) {
        std::ofstream report("four_tree_detailed_report.md");
        
        report << "# 四种树结构详细性能对比报告（公平测试）\n\n";
        report << "## 测试环境\n";
        report << "- CPU核心数: " << std::thread::hardware_concurrency() << "\n";
        report << "- 编译优化: -O3 -DNDEBUG\n";
        report << "- C++标准: C++17\n";
        report << "- 测试特点: 所有树结构都支持多线程，公平对比\n\n";
        
        report << "## 详细测试结果\n\n";
        report << "| 测试项目 | 规模 | 原始树(ms) | 线程安全树(ms) | 扁平树hash(ms) | 扁平树ordered(ms) | 获胜者 | 备注 |\n";
        report << "|---------|------|------------|----------------|----------------|-------------------|--------|------|\n";
        
        for (const auto& result : results) {
            report << "| " << result.test_name << " | " << result.test_size << " | ";
            
            if (result.original_time_ms >= 0) {
                report << std::fixed << std::setprecision(2) << result.original_time_ms;
            } else {
                report << "N/A";
            }
            report << " | ";
            
            if (result.threadsafe_time_ms >= 0) {
                report << std::fixed << std::setprecision(2) << result.threadsafe_time_ms;
            } else {
                report << "N/A";
            }
            report << " | ";
            
            if (result.flat_time_ms >= 0) {
                report << std::fixed << std::setprecision(2) << result.flat_time_ms;
            } else {
                report << "N/A";
            }
            report << " | ";
            
            if (result.ordered_time_ms >= 0) {
                report << std::fixed << std::setprecision(2) << result.ordered_time_ms;
            } else {
                report << "N/A";
            }
            report << " | " << result.winner << " | " << result.notes << " |\n";
        }
        
        report.close();
        std::cout << "\n详细报告已生成: four_tree_detailed_report.md" << std::endl;
    }
    
    static void generateSummaryReport(const std::vector<TestResult>& results) {
        std::cout << "\n=== 四种树结构性能对比总结（公平测试） ===" << std::endl;
        
        // 统计各树结构的获胜次数
        int original_wins = 0, threadsafe_wins = 0, flat_wins = 0, ordered_wins = 0;
        
        for (const auto& result : results) {
            if (result.winner == "原始树") original_wins++;
            else if (result.winner == "线程安全树") threadsafe_wins++;
            else if (result.winner == "扁平树(hash)") flat_wins++;
            else if (result.winner == "扁平树(ordered)") ordered_wins++;
        }
        
        std::cout << "\n获胜统计:" << std::endl;
        std::cout << "  原始树: " << original_wins << " 次获胜" << std::endl;
        std::cout << "  线程安全树: " << threadsafe_wins << " 次获胜" << std::endl;
        std::cout << "  扁平树(hash): " << flat_wins << " 次获胜" << std::endl;
        std::cout << "  扁平树(ordered): " << ordered_wins << " 次获胜" << std::endl;
        
        std::cout << "\n=== 公平对比结论 ===" << std::endl;
        std::cout << "1. 原始树: 单线程性能最佳，但不支持多线程" << std::endl;
        std::cout << "2. 线程安全树: 在保持原始树优势的同时支持多线程" << std::endl;
        std::cout << "3. 扁平树(hash): 大规模查找和某些多线程场景表现优异" << std::endl;
        std::cout << "4. 扁平树(ordered): 提供有序性和范围查询的独特功能" << std::endl;
    }
};

int main() {
    try {
        FourTreeComparison::runAllComparisons();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
