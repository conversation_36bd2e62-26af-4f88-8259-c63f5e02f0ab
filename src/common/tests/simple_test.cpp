#include <iostream>
#include <cassert>
#include <string>
#include <vector>
#include <algorithm>
#include "../Tree.h"

using namespace common;

/**
 * @brief 简单的测试函数，不依赖Qt
 */
class SimpleTreeTest {
public:
    static void runAllTests() {
        std::cout << "开始运行树形结构测试..." << std::endl;
        
        testBasicOperations();
        testTraversal();
        testDescendants();
        testIterators();
        testNodeOperations();
        
        std::cout << "所有测试通过！" << std::endl;
    }

private:
    static void testBasicOperations() {
        std::cout << "测试基本操作..." << std::endl;
        
        Tree<int, std::string> tree;
        
        // 测试空树
        assert(tree.empty());
        assert(tree.size() == 0);
        assert(tree.getRoot() == nullptr);
        
        // 测试插入根节点
        auto root = tree.insertRoot(1, "root");
        assert(root != nullptr);
        assert(root->getKey() == 1);
        assert(root->getData() == "root");
        assert(!tree.empty());
        assert(tree.size() == 1);
        
        // 测试插入子节点
        auto child1 = tree.insertChild(1, 2, "child1");
        auto child2 = tree.insertChild(1, 3, "child2");
        assert(tree.size() == 3);
        
        // 测试查找
        auto found = tree.findNode(2);
        assert(found != nullptr);
        assert(found->getKey() == 2);
        
        auto notFound = tree.findNode(999);
        assert(notFound == nullptr);
        
        // 测试包含检查
        assert(tree.contains(1));
        assert(tree.contains(2));
        assert(!tree.contains(999));
        
        std::cout << "基本操作测试通过" << std::endl;
    }
    
    static void testTraversal() {
        std::cout << "测试遍历操作..." << std::endl;
        
        Tree<int, std::string> tree;
        setupSampleTree(tree);
        
        // 测试BFS遍历
        std::vector<int> bfsResult;
        tree.traverseBFS([&bfsResult](const auto& node) {
            bfsResult.push_back(node->getKey());
        });
        
        std::vector<int> expectedBFS = {1, 2, 3, 4, 5, 6};
        assert(bfsResult == expectedBFS);
        
        // 测试DFS遍历
        std::vector<int> dfsResult;
        tree.traverseDFS([&dfsResult](const auto& node) {
            dfsResult.push_back(node->getKey());
        });
        
        std::vector<int> expectedDFS = {1, 2, 4, 3, 5, 6};
        assert(dfsResult == expectedDFS);
        
        std::cout << "遍历操作测试通过" << std::endl;
    }
    
    static void testDescendants() {
        std::cout << "测试后代查找..." << std::endl;
        
        Tree<int, std::string> tree;
        setupSampleTree(tree);
        
        // 测试BFS后代查找
        auto descendants = tree.getDescendantsBFS(1);
        std::vector<int> expected = {2, 3, 4, 5, 6};
        assert(descendants == expected);
        
        descendants = tree.getDescendantsBFS(3);
        expected = {5, 6};
        assert(descendants == expected);
        
        descendants = tree.getDescendantsBFS(4);
        assert(descendants.empty());
        
        // 测试DFS后代查找
        descendants = tree.getDescendantsDFS(1);
        expected = {2, 4, 3, 5, 6};
        assert(descendants == expected);
        
        std::cout << "后代查找测试通过" << std::endl;
    }
    
    static void testIterators() {
        std::cout << "测试迭代器..." << std::endl;
        
        Tree<int, std::string> tree;
        setupSampleTree(tree);
        
        // 测试BFS迭代器
        std::vector<int> bfsKeys;
        for (auto it = tree.beginBFS(); it != tree.endBFS(); ++it) {
            bfsKeys.push_back((*it)->getKey());
        }
        
        std::vector<int> expectedBFS = {1, 2, 3, 4, 5, 6};
        assert(bfsKeys == expectedBFS);
        
        // 测试DFS迭代器
        std::vector<int> dfsKeys;
        for (auto it = tree.beginDFS(); it != tree.endDFS(); ++it) {
            dfsKeys.push_back((*it)->getKey());
        }
        
        std::vector<int> expectedDFS = {1, 2, 4, 3, 5, 6};
        assert(dfsKeys == expectedDFS);
        
        std::cout << "迭代器测试通过" << std::endl;
    }
    
    static void testNodeOperations() {
        std::cout << "测试节点操作..." << std::endl;
        
        Tree<int, std::string> tree;
        setupSampleTree(tree);
        
        auto root = tree.findNode(1);
        auto child2 = tree.findNode(2);
        auto grandchild4 = tree.findNode(4);
        
        // 测试节点属性
        assert(root->isRoot());
        assert(!root->isLeaf());
        assert(root->getChildrenCount() == 2);
        
        assert(!child2->isRoot());
        assert(!child2->isLeaf());
        assert(child2->getChildrenCount() == 1);
        
        assert(!grandchild4->isRoot());
        assert(grandchild4->isLeaf());
        assert(grandchild4->getChildrenCount() == 0);
        
        // 测试父子关系
        assert(child2->getParent() == root);
        assert(grandchild4->getParent() == child2);
        
        // 测试深度
        assert(tree.getDepth(1) == 0);
        assert(tree.getDepth(2) == 1);
        assert(tree.getDepth(4) == 2);
        
        // 测试最大深度
        assert(tree.getMaxDepth() == 2);
        
        std::cout << "节点操作测试通过" << std::endl;
    }
    
    static void setupSampleTree(Tree<int, std::string>& tree) {
        tree.insertRoot(1, "root");
        tree.insertChild(1, 2, "child1");
        tree.insertChild(1, 3, "child2");
        tree.insertChild(2, 4, "grandchild1");
        tree.insertChild(3, 5, "grandchild2");
        tree.insertChild(3, 6, "grandchild3");
    }
};

int main() {
    try {
        SimpleTreeTest::runAllTests();
        std::cout << "\n=== 所有测试成功完成 ===" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "测试失败: 未知错误" << std::endl;
        return 1;
    }
}
