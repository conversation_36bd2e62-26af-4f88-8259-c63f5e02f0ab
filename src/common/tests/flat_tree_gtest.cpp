#include <gtest/gtest.h>
#include "../FlatTree.h"
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include <random>
#include <algorithm>

using namespace common;

class FlatTreeTest : public ::testing::Test {
protected:
    void SetUp() override {
        tree = std::make_unique<FlatTree<int, std::string>>();
    }

    void TearDown() override {
        tree.reset();
    }

    void setupSampleTree() {
        tree->insertRoot(1, "root");
        tree->insertChild(1, 2, "child1");
        tree->insertChild(1, 3, "child2");
        tree->insertChild(2, 4, "grandchild1");
        tree->insertChild(2, 5, "grandchild2");
        tree->insertChild(3, 6, "grandchild3");
        tree->insertChild(3, 7, "grandchild4");
        tree->insertChild(4, 8, "great_grandchild1");
    }

    std::unique_ptr<FlatTree<int, std::string>> tree;
};

// 基本操作测试
TEST_F(FlatTreeTest, BasicOperations) {
    // 测试空树
    EXPECT_TRUE(tree->empty());
    EXPECT_EQ(tree->size(), 0);

    // 测试插入根节点
    EXPECT_TRUE(tree->insertRoot(1, "root"));
    EXPECT_FALSE(tree->empty());
    EXPECT_EQ(tree->size(), 1);
    EXPECT_TRUE(tree->contains(1));

    // 测试重复插入根节点
    EXPECT_FALSE(tree->insertRoot(1, "duplicate_root"));
    EXPECT_EQ(tree->size(), 1);

    // 测试插入子节点
    EXPECT_TRUE(tree->insertChild(1, 2, "child"));
    EXPECT_EQ(tree->size(), 2);
    EXPECT_TRUE(tree->contains(2));

    // 测试插入到不存在的父节点
    EXPECT_FALSE(tree->insertChild(999, 3, "orphan"));
    EXPECT_EQ(tree->size(), 2);

    // 测试重复键
    EXPECT_FALSE(tree->insertChild(1, 2, "duplicate"));
    EXPECT_EQ(tree->size(), 2);
}

// 查找操作测试
TEST_F(FlatTreeTest, FindOperations) {
    setupSampleTree();

    // 测试查找存在的节点
    auto data = tree->findNode(3);
    ASSERT_TRUE(data.has_value());
    EXPECT_EQ(*data, "child2");

    // 测试查找不存在的节点
    auto missing = tree->findNode(999);
    EXPECT_FALSE(missing.has_value());

    // 测试contains
    EXPECT_TRUE(tree->contains(1));
    EXPECT_TRUE(tree->contains(8));
    EXPECT_FALSE(tree->contains(999));
}

// 删除操作测试
TEST_F(FlatTreeTest, RemoveOperations) {
    setupSampleTree();
    size_t originalSize = tree->size();

    // 删除叶子节点
    EXPECT_TRUE(tree->removeNode(8));
    EXPECT_EQ(tree->size(), originalSize - 1);
    EXPECT_FALSE(tree->contains(8));

    // 删除有子节点的节点（应该删除整个子树）
    EXPECT_TRUE(tree->removeNode(3));
    EXPECT_FALSE(tree->contains(3));
    EXPECT_FALSE(tree->contains(6));
    EXPECT_FALSE(tree->contains(7));

    // 删除不存在的节点
    EXPECT_FALSE(tree->removeNode(999));

    // 删除根节点
    EXPECT_TRUE(tree->removeNode(1));
    EXPECT_FALSE(tree->contains(1));
    EXPECT_FALSE(tree->contains(2));
    EXPECT_FALSE(tree->contains(4));
    EXPECT_FALSE(tree->contains(5));
}

// 后代查找测试
TEST_F(FlatTreeTest, DescendantOperations) {
    setupSampleTree();

    // 测试BFS后代查找
    auto descendantsBFS = tree->getDescendantsBFS(1);
    std::vector<int> expectedBFS = {2, 3, 4, 5, 6, 7, 8};
    EXPECT_EQ(descendantsBFS, expectedBFS);

    auto descendantsBFS3 = tree->getDescendantsBFS(3);
    std::vector<int> expectedBFS3 = {6, 7};
    EXPECT_EQ(descendantsBFS3, expectedBFS3);

    // 测试DFS后代查找
    auto descendantsDFS = tree->getDescendantsDFS(1);
    std::vector<int> expectedDFS = {2, 4, 8, 5, 3, 6, 7};
    EXPECT_EQ(descendantsDFS, expectedDFS);

    // 测试叶子节点的后代
    auto leafDescendants = tree->getDescendantsBFS(8);
    EXPECT_TRUE(leafDescendants.empty());

    // 测试不存在节点的后代
    auto missingDescendants = tree->getDescendantsBFS(999);
    EXPECT_TRUE(missingDescendants.empty());
}

// 遍历操作测试
TEST_F(FlatTreeTest, TraversalOperations) {
    setupSampleTree();

    // 测试BFS遍历
    std::vector<int> bfsResult;
    tree->traverseBFS([&bfsResult](const int& key, const std::string& data) {
        bfsResult.push_back(key);
    });

    std::vector<int> expectedBFS = {1, 2, 3, 4, 5, 6, 7, 8};
    EXPECT_EQ(bfsResult, expectedBFS);

    // 测试DFS遍历
    std::vector<int> dfsResult;
    tree->traverseDFS([&dfsResult](const int& key, const std::string& data) {
        dfsResult.push_back(key);
    });

    std::vector<int> expectedDFS = {1, 2, 4, 8, 5, 3, 6, 7};
    EXPECT_EQ(dfsResult, expectedDFS);
}

// 深度和统计测试
TEST_F(FlatTreeTest, DepthAndStats) {
    setupSampleTree();

    // 测试节点深度
    EXPECT_EQ(tree->getDepth(1), 0);  // root
    EXPECT_EQ(tree->getDepth(2), 1);  // child
    EXPECT_EQ(tree->getDepth(4), 2);  // grandchild
    EXPECT_EQ(tree->getDepth(8), 3);  // great-grandchild
    EXPECT_EQ(tree->getDepth(999), -1);  // non-existent

    // 测试最大深度
    EXPECT_EQ(tree->getMaxDepth(), 3);

    // 测试统计信息
    auto stats = tree->getStats();
    EXPECT_EQ(stats.total_nodes, 8);
    EXPECT_EQ(stats.root_nodes, 1);
}

// 清空和压缩测试
TEST_F(FlatTreeTest, ClearAndCompact) {
    setupSampleTree();
    EXPECT_FALSE(tree->empty());

    // 测试清空
    tree->clear();
    EXPECT_TRUE(tree->empty());
    EXPECT_EQ(tree->size(), 0);

    // 重新设置树并测试压缩
    setupSampleTree();
    tree->removeNode(3);  // 软删除
    
    size_t compacted = tree->compact();
    EXPECT_GT(compacted, 0);  // 应该清理了一些节点
}

// 多线程安全测试
TEST_F(FlatTreeTest, ThreadSafety) {
    const int num_threads = 4;
    const int operations_per_thread = 100;
    
    tree->insertRoot(0, "root");
    
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    // 并发插入测试
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &success_count]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                int key = t * operations_per_thread + i + 1;
                if (tree->insertChild(0, key, "data_" + std::to_string(key))) {
                    success_count.fetch_add(1);
                }
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_EQ(success_count.load(), num_threads * operations_per_thread);
    EXPECT_EQ(tree->size(), static_cast<size_t>(num_threads * operations_per_thread + 1));
}

// 并行操作测试
TEST_F(FlatTreeTest, ParallelOperations) {
    tree->insertRoot(0, "root");
    
    // 准备批量插入数据
    std::vector<std::tuple<int, int, std::string>> batch_data;
    for (int i = 1; i <= 1000; ++i) {
        batch_data.emplace_back(0, i, "data_" + std::to_string(i));
    }
    
    // 测试并行批量插入
    size_t inserted = tree->parallelBatchInsert(batch_data);
    EXPECT_EQ(inserted, 1000);
    EXPECT_EQ(tree->size(), 1001);  // 包括根节点
    
    // 测试并行批量查找
    std::vector<int> keys_to_find;
    for (int i = 1; i <= 1000; i += 10) {
        keys_to_find.push_back(i);
    }
    
    auto found_data = tree->parallelBatchFind(keys_to_find);
    EXPECT_EQ(found_data.size(), keys_to_find.size());
    
    for (int key : keys_to_find) {
        EXPECT_TRUE(found_data.find(key) != found_data.end());
        EXPECT_EQ(found_data[key], "data_" + std::to_string(key));
    }
}

// 边界条件测试
TEST_F(FlatTreeTest, EdgeCases) {
    // 空树操作
    EXPECT_FALSE(tree->removeNode(1));
    EXPECT_FALSE(tree->findNode(1).has_value());
    EXPECT_EQ(tree->getMaxDepth(), -1);
    
    auto emptyDescendants = tree->getDescendantsBFS(1);
    EXPECT_TRUE(emptyDescendants.empty());
    
    // 单节点树
    tree->insertRoot(42, "single");
    EXPECT_EQ(tree->size(), 1);
    EXPECT_EQ(tree->getMaxDepth(), 0);
    EXPECT_EQ(tree->getDepth(42), 0);
    
    auto singleDescendants = tree->getDescendantsBFS(42);
    EXPECT_TRUE(singleDescendants.empty());
}

// 性能压力测试
TEST_F(FlatTreeTest, StressTest) {
    const int num_nodes = 10000;
    
    // 构建大树
    tree->insertRoot(0, "root");
    
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 1; i < num_nodes; ++i) {
        int parent = (i - 1) / 2;  // 构建近似平衡的二叉树
        tree->insertChild(parent, i, "node_" + std::to_string(i));
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    EXPECT_EQ(tree->size(), static_cast<size_t>(num_nodes));
    
    // 性能应该在合理范围内（这个阈值可能需要根据硬件调整）
    EXPECT_LT(duration.count(), 1000);  // 应该在1秒内完成
    
    // 测试大树的遍历性能
    start = std::chrono::high_resolution_clock::now();
    
    std::atomic<int> count{0};
    tree->traverseBFS([&count](const int& key, const std::string& data) {
        count.fetch_add(1);
    });
    
    end = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    EXPECT_EQ(count.load(), num_nodes);
    EXPECT_LT(duration.count(), 500);  // 遍历应该在0.5秒内完成
}
