#include <iostream>
#include <cassert>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <atomic>
#include "../FlatTree.h"

using namespace common;

/**
 * @brief 简单的FlatTree测试，不依赖外部框架
 */
class SimpleFlatTreeTest {
public:
    static void runAllTests() {
        std::cout << "开始运行扁平化树测试..." << std::endl;
        
        testBasicOperations();
        testTraversal();
        testDescendants();
        testMultiThreading();
        testParallelOperations();
        testPerformance();
        
        std::cout << "所有扁平化树测试通过！" << std::endl;
    }

private:
    static void testBasicOperations() {
        std::cout << "测试基本操作..." << std::endl;
        
        FlatTree<int, std::string> tree;
        
        // 测试空树
        assert(tree.empty());
        assert(tree.size() == 0);
        
        // 测试插入根节点
        assert(tree.insertRoot(1, "root"));
        assert(!tree.empty());
        assert(tree.size() == 1);
        assert(tree.contains(1));
        
        // 测试重复插入根节点
        assert(!tree.insertRoot(1, "duplicate"));
        assert(tree.size() == 1);
        
        // 测试插入子节点
        assert(tree.insertChild(1, 2, "child1"));
        assert(tree.insertChild(1, 3, "child2"));
        assert(tree.size() == 3);
        
        // 测试查找
        auto data = tree.findNode(2);
        assert(data.has_value());
        assert(*data == "child1");
        
        auto missing = tree.findNode(999);
        assert(!missing.has_value());
        
        // 测试包含检查
        assert(tree.contains(1));
        assert(tree.contains(2));
        assert(!tree.contains(999));
        
        std::cout << "基本操作测试通过" << std::endl;
    }
    
    static void testTraversal() {
        std::cout << "测试遍历操作..." << std::endl;
        
        FlatTree<int, std::string> tree;
        setupSampleTree(tree);
        
        // 测试BFS遍历
        std::vector<int> bfsResult;
        tree.traverseBFS([&bfsResult](const int& key, const std::string& data) {
            bfsResult.push_back(key);
        });
        
        std::vector<int> expectedBFS = {1, 2, 3, 4, 5, 6};
        assert(bfsResult == expectedBFS);
        
        // 测试DFS遍历
        std::vector<int> dfsResult;
        tree.traverseDFS([&dfsResult](const int& key, const std::string& data) {
            dfsResult.push_back(key);
        });
        
        std::vector<int> expectedDFS = {1, 2, 4, 3, 5, 6};
        assert(dfsResult == expectedDFS);
        
        std::cout << "遍历操作测试通过" << std::endl;
    }
    
    static void testDescendants() {
        std::cout << "测试后代查找..." << std::endl;
        
        FlatTree<int, std::string> tree;
        setupSampleTree(tree);
        
        // 测试BFS后代查找
        auto descendants = tree.getDescendantsBFS(1);
        std::vector<int> expected = {2, 3, 4, 5, 6};
        assert(descendants == expected);
        
        descendants = tree.getDescendantsBFS(3);
        expected = {5, 6};
        assert(descendants == expected);
        
        descendants = tree.getDescendantsBFS(4);
        assert(descendants.empty());
        
        // 测试DFS后代查找
        descendants = tree.getDescendantsDFS(1);
        expected = {2, 4, 3, 5, 6};
        assert(descendants == expected);
        
        std::cout << "后代查找测试通过" << std::endl;
    }
    
    static void testMultiThreading() {
        std::cout << "测试多线程安全..." << std::endl;
        
        FlatTree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        const int num_threads = 4;
        const int operations_per_thread = 100;
        
        std::vector<std::thread> threads;
        std::atomic<int> success_count{0};
        
        // 并发插入测试
        for (int t = 0; t < num_threads; ++t) {
            threads.emplace_back([&tree, t, operations_per_thread, &success_count]() {
                for (int i = 0; i < operations_per_thread; ++i) {
                    int key = t * operations_per_thread + i + 1;
                    if (tree.insertChild(0, key, "data_" + std::to_string(key))) {
                        success_count.fetch_add(1);
                    }
                }
            });
        }
        
        for (auto& thread : threads) {
            thread.join();
        }
        
        assert(success_count.load() == num_threads * operations_per_thread);
        assert(tree.size() == static_cast<size_t>(num_threads * operations_per_thread + 1));
        
        std::cout << "多线程安全测试通过" << std::endl;
    }
    
    static void testParallelOperations() {
        std::cout << "测试并行操作..." << std::endl;
        
        FlatTree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        // 准备批量插入数据
        std::vector<std::tuple<int, int, std::string>> batch_data;
        for (int i = 1; i <= 100; ++i) {
            batch_data.emplace_back(0, i, "data_" + std::to_string(i));
        }
        
        // 测试并行批量插入
        size_t inserted = tree.parallelBatchInsert(batch_data);
        assert(inserted == 100);
        assert(tree.size() == 101);  // 包括根节点
        
        // 测试并行批量查找
        std::vector<int> keys_to_find;
        for (int i = 1; i <= 100; i += 10) {
            keys_to_find.push_back(i);
        }
        
        auto found_data = tree.parallelBatchFind(keys_to_find);
        assert(found_data.size() == keys_to_find.size());
        
        for (int key : keys_to_find) {
            assert(found_data.find(key) != found_data.end());
            assert(found_data[key] == "data_" + std::to_string(key));
        }
        
        std::cout << "并行操作测试通过" << std::endl;
    }
    
    static void testPerformance() {
        std::cout << "测试性能..." << std::endl;
        
        const int num_nodes = 10000;
        
        FlatTree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        auto start = std::chrono::high_resolution_clock::now();
        
        // 构建大树
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;  // 构建近似平衡的二叉树
            tree.insertChild(parent, i, "node_" + std::to_string(i));
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        assert(tree.size() == static_cast<size_t>(num_nodes));
        std::cout << "插入 " << num_nodes << " 个节点耗时: " << duration.count() << "ms" << std::endl;
        
        // 测试遍历性能
        start = std::chrono::high_resolution_clock::now();
        
        std::atomic<int> count{0};
        tree.traverseBFS([&count](const int& key, const std::string& data) {
            count.fetch_add(1);
        });
        
        end = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        assert(count.load() == num_nodes);
        std::cout << "遍历 " << num_nodes << " 个节点耗时: " << duration.count() << "ms" << std::endl;
        
        // 测试后代查找性能
        start = std::chrono::high_resolution_clock::now();
        
        auto descendants = tree.getDescendantsBFS(0);
        
        end = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        assert(descendants.size() == static_cast<size_t>(num_nodes - 1));
        std::cout << "查找根节点的所有后代耗时: " << duration.count() << "ms" << std::endl;
        
        // 测试统计信息
        auto stats = tree.getStats();
        std::cout << "树统计信息:" << std::endl;
        std::cout << "  总节点数: " << stats.total_nodes << std::endl;
        std::cout << "  根节点数: " << stats.root_nodes << std::endl;
        std::cout << "  叶子节点数: " << stats.leaf_nodes << std::endl;
        std::cout << "  内部节点数: " << stats.internal_nodes << std::endl;
        
        // 测试深度
        std::cout << "  最大深度: " << tree.getMaxDepth() << std::endl;
        
        std::cout << "性能测试通过" << std::endl;
    }
    
    static void setupSampleTree(FlatTree<int, std::string>& tree) {
        tree.insertRoot(1, "root");
        tree.insertChild(1, 2, "child1");
        tree.insertChild(1, 3, "child2");
        tree.insertChild(2, 4, "grandchild1");
        tree.insertChild(3, 5, "grandchild2");
        tree.insertChild(3, 6, "grandchild3");
    }
};

int main() {
    try {
        SimpleFlatTreeTest::runAllTests();
        std::cout << "\n=== 所有扁平化树测试成功完成 ===" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "测试失败: 未知错误" << std::endl;
        return 1;
    }
}
