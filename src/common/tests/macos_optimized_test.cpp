#include <iostream>
#include <chrono>
#include <vector>
#include <random>
#include <iomanip>
#include <cassert>
#include "../FlatTreeMacOS.h"
#include "../FlatTree.h"
#include "../Tree.h"

using namespace common;
using namespace std::chrono;

/**
 * @brief macOS优化版本的专项测试
 */
class MacOSOptimizedTest {
public:
    static void runAllTests() {
        std::cout << "=== macOS优化版树形结构测试 ===" << std::endl;
        
        testBasicFunctionality();
        testGCDOperations();
        testPerformanceComparison();
        testMemoryOptimizations();
        testAsyncOperations();
        
        std::cout << "=== 所有macOS优化测试完成 ===" << std::endl;
    }

private:
    static void testBasicFunctionality() {
        std::cout << "\n1. 基础功能测试..." << std::endl;
        
        FlatTreeMacOS<int, std::string> tree;
        
        // 基本操作测试
        assert(tree.insertRoot(1, "root"));
        assert(tree.insertChild(1, 2, "child1"));
        assert(tree.insertChild(1, 3, "child2"));
        
        auto data = tree.findNode(2);
        assert(data.has_value() && *data == "child1");
        
        assert(tree.size() == 3);
        
        // 获取macOS统计信息
        auto stats = tree.getMacOSStats();
        std::cout << "  CPU核心数: " << stats.cpu_count << std::endl;
        std::cout << "  缓存行大小: " << stats.cache_line_size << " bytes" << std::endl;
        std::cout << "  估算内存使用: " << stats.memory_usage_bytes << " bytes" << std::endl;
        
        std::cout << "  ✅ 基础功能测试通过" << std::endl;
    }
    
    static void testGCDOperations() {
        std::cout << "\n2. GCD并行操作测试..." << std::endl;
        
        FlatTreeMacOS<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        // 准备测试数据
        const int batch_size = 10000;
        std::vector<std::tuple<int, int, std::string>> batch_data;
        for (int i = 1; i <= batch_size; ++i) {
            batch_data.emplace_back(0, i, "data_" + std::to_string(i));
        }
        
        // 测试GCD并行插入
        auto start = high_resolution_clock::now();
        size_t inserted = tree.gcdParallelBatchInsert(batch_data);
        auto end = high_resolution_clock::now();
        auto gcd_insert_time = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        assert(inserted == batch_size);
        assert(tree.size() == batch_size + 1);
        
        std::cout << "  GCD并行插入 " << batch_size << " 个节点耗时: " 
                  << std::fixed << std::setprecision(2) << gcd_insert_time << "ms" << std::endl;
        
        // 测试GCD并行查找
        std::vector<int> search_keys;
        for (int i = 1; i <= batch_size; i += 100) {
            search_keys.push_back(i);
        }
        
        start = high_resolution_clock::now();
        auto results = tree.gcdParallelBatchFind(search_keys);
        end = high_resolution_clock::now();
        auto gcd_find_time = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        assert(results.size() == search_keys.size());
        
        std::cout << "  GCD并行查找 " << search_keys.size() << " 个节点耗时: " 
                  << std::fixed << std::setprecision(2) << gcd_find_time << "ms" << std::endl;
        
        // 测试GCD并行遍历
        std::atomic<int> traverse_count{0};
        start = high_resolution_clock::now();
        tree.gcdParallelTraverse([&traverse_count](const int& key, const std::string& data) {
            traverse_count.fetch_add(1);
        });
        end = high_resolution_clock::now();
        auto gcd_traverse_time = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        std::cout << "  GCD并行遍历 " << traverse_count.load() << " 个节点耗时: " 
                  << std::fixed << std::setprecision(2) << gcd_traverse_time << "ms" << std::endl;
        
        std::cout << "  ✅ GCD并行操作测试通过" << std::endl;
    }
    
    static void testPerformanceComparison() {
        std::cout << "\n3. 性能对比测试..." << std::endl;
        
        const int test_size = 50000;
        
        // 原始Tree性能
        auto start = high_resolution_clock::now();
        {
            Tree<int, std::string> original_tree;
            original_tree.insertRoot(0, "root");
            for (int i = 1; i < test_size; ++i) {
                original_tree.insertChild((i-1)/2, i, "data_" + std::to_string(i));
            }
        }
        auto end = high_resolution_clock::now();
        auto original_time = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // 标准FlatTree性能
        start = high_resolution_clock::now();
        {
            FlatTree<int, std::string> flat_tree;
            flat_tree.insertRoot(0, "root");
            for (int i = 1; i < test_size; ++i) {
                flat_tree.insertChild((i-1)/2, i, "data_" + std::to_string(i));
            }
        }
        end = high_resolution_clock::now();
        auto flat_time = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        // macOS优化FlatTree性能
        start = high_resolution_clock::now();
        {
            FlatTreeMacOS<int, std::string> macos_tree;
            macos_tree.insertRoot(0, "root");
            macos_tree.reserveCapacity(test_size);  // 预分配内存
            
            for (int i = 1; i < test_size; ++i) {
                macos_tree.fastInsertChild((i-1)/2, i, "data_" + std::to_string(i));
            }
        }
        end = high_resolution_clock::now();
        auto macos_time = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        std::cout << "  插入 " << test_size << " 个节点性能对比:" << std::endl;
        std::cout << "    原始Tree:     " << std::fixed << std::setprecision(2) << original_time << "ms" << std::endl;
        std::cout << "    标准FlatTree: " << flat_time << "ms (加速 " << original_time/flat_time << "x)" << std::endl;
        std::cout << "    macOS优化:    " << macos_time << "ms (加速 " << original_time/macos_time << "x)" << std::endl;
        
        std::cout << "  ✅ 性能对比测试完成" << std::endl;
    }
    
    static void testMemoryOptimizations() {
        std::cout << "\n4. 内存优化测试..." << std::endl;
        
        FlatTreeMacOS<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        // 测试内存预分配
        const int capacity = 100000;
        tree.reserveCapacity(capacity);
        
        // 测试缓存友好的批量插入
        std::vector<std::tuple<int, int, std::string>> batch_data;
        for (int i = 1; i <= 10000; ++i) {
            batch_data.emplace_back(0, i, "data_" + std::to_string(i));
        }
        
        auto start = high_resolution_clock::now();
        size_t inserted = tree.cacheOptimizedBatchInsert(batch_data.begin(), batch_data.end());
        auto end = high_resolution_clock::now();
        auto cache_optimized_time = duration_cast<microseconds>(end - start).count() / 1000.0;
        
        assert(inserted == 10000);
        
        std::cout << "  缓存优化批量插入耗时: " << std::fixed << std::setprecision(2) 
                  << cache_optimized_time << "ms" << std::endl;
        
        // 获取内存统计
        auto stats = tree.getMacOSStats();
        std::cout << "  当前内存使用: " << stats.memory_usage_bytes / 1024 << " KB" << std::endl;
        
        std::cout << "  ✅ 内存优化测试通过" << std::endl;
    }
    
    static void testAsyncOperations() {
        std::cout << "\n5. 异步操作测试..." << std::endl;
        
        FlatTreeMacOS<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        // 准备异步操作数据
        std::vector<std::tuple<int, int, std::string>> async_data;
        for (int i = 1; i <= 5000; ++i) {
            async_data.emplace_back(0, i, "async_data_" + std::to_string(i));
        }
        
        std::cout << "  开始异步批量操作..." << std::endl;
        
        auto start = high_resolution_clock::now();
        
        // 使用信号量等待异步操作完成
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
        
        tree.asyncBatchOperation(async_data, [&](size_t success_count) {
            auto end = high_resolution_clock::now();
            auto async_time = duration_cast<microseconds>(end - start).count() / 1000.0;
            
            std::cout << "  异步操作完成: 成功插入 " << success_count << " 个节点" << std::endl;
            std::cout << "  异步操作耗时: " << std::fixed << std::setprecision(2) 
                      << async_time << "ms" << std::endl;
            
            dispatch_semaphore_signal(semaphore);
        });
        
        // 等待异步操作完成
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        dispatch_release(semaphore);
        
        assert(tree.size() == 5001);  // 包括根节点
        
        std::cout << "  ✅ 异步操作测试通过" << std::endl;
    }
};

int main() {
    try {
        MacOSOptimizedTest::runAllTests();
        std::cout << "\n🎉 所有macOS优化测试成功完成！" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 测试失败: 未知错误" << std::endl;
        return 1;
    }
}
