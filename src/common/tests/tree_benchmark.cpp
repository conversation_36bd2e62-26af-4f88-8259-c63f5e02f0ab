#include <benchmark/benchmark.h>
#include "../Tree.h"
#include "../FlatTree.h"
#include <string>
#include <vector>
#include <random>
#include <algorithm>

using namespace common;

// 测试数据生成器
class TestDataGenerator {
public:
    static std::vector<int> generateSequentialKeys(int count) {
        std::vector<int> keys;
        keys.reserve(count);
        for (int i = 0; i < count; ++i) {
            keys.push_back(i);
        }
        return keys;
    }
    
    static std::vector<int> generateRandomKeys(int count, int seed = 42) {
        std::vector<int> keys = generateSequentialKeys(count);
        std::mt19937 gen(seed);
        std::shuffle(keys.begin(), keys.end(), gen);
        return keys;
    }
    
    static std::vector<std::tuple<int, int, std::string>> generateBatchInsertData(int count) {
        std::vector<std::tuple<int, int, std::string>> data;
        data.reserve(count);
        for (int i = 1; i < count; ++i) {
            int parent = (i - 1) / 2;  // 构建近似平衡的二叉树
            data.emplace_back(parent, i, "data_" + std::to_string(i));
        }
        return data;
    }
};

// 原始Tree实现的基准测试
static void BM_OriginalTree_Insert(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    for (auto _ : state) {
        Tree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        benchmark::DoNotOptimize(tree);
        
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;
            tree.insertChild(parent, i, "data_" + std::to_string(i));
        }
        
        benchmark::ClobberMemory();
    }
    
    state.SetComplexityN(num_nodes);
}

// 扁平化Tree实现的基准测试
static void BM_FlatTree_Insert(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    for (auto _ : state) {
        FlatTree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        benchmark::DoNotOptimize(tree);
        
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;
            tree.insertChild(parent, i, "data_" + std::to_string(i));
        }
        
        benchmark::ClobberMemory();
    }
    
    state.SetComplexityN(num_nodes);
}

// 原始Tree查找基准测试
static void BM_OriginalTree_Find(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    // 预先构建树
    Tree<int, std::string> tree;
    tree.insertRoot(0, "root");
    for (int i = 1; i < num_nodes; ++i) {
        int parent = (i - 1) / 2;
        tree.insertChild(parent, i, "data_" + std::to_string(i));
    }
    
    auto keys = TestDataGenerator::generateRandomKeys(num_nodes);
    
    for (auto _ : state) {
        for (int key : keys) {
            auto node = tree.findNode(key);
            benchmark::DoNotOptimize(node);
        }
    }
    
    state.SetComplexityN(num_nodes);
}

// 扁平化Tree查找基准测试
static void BM_FlatTree_Find(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    // 预先构建树
    FlatTree<int, std::string> tree;
    tree.insertRoot(0, "root");
    for (int i = 1; i < num_nodes; ++i) {
        int parent = (i - 1) / 2;
        tree.insertChild(parent, i, "data_" + std::to_string(i));
    }
    
    auto keys = TestDataGenerator::generateRandomKeys(num_nodes);
    
    for (auto _ : state) {
        for (int key : keys) {
            auto data = tree.findNode(key);
            benchmark::DoNotOptimize(data);
        }
    }
    
    state.SetComplexityN(num_nodes);
}

// 原始Tree BFS遍历基准测试
static void BM_OriginalTree_TraverseBFS(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    // 预先构建树
    Tree<int, std::string> tree;
    tree.insertRoot(0, "root");
    for (int i = 1; i < num_nodes; ++i) {
        int parent = (i - 1) / 2;
        tree.insertChild(parent, i, "data_" + std::to_string(i));
    }
    
    for (auto _ : state) {
        int count = 0;
        tree.traverseBFS([&count](const auto& node) {
            count++;
            benchmark::DoNotOptimize(count);
        });
        benchmark::DoNotOptimize(count);
    }
    
    state.SetComplexityN(num_nodes);
}

// 扁平化Tree BFS遍历基准测试
static void BM_FlatTree_TraverseBFS(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    // 预先构建树
    FlatTree<int, std::string> tree;
    tree.insertRoot(0, "root");
    for (int i = 1; i < num_nodes; ++i) {
        int parent = (i - 1) / 2;
        tree.insertChild(parent, i, "data_" + std::to_string(i));
    }
    
    for (auto _ : state) {
        int count = 0;
        tree.traverseBFS([&count](const int& key, const std::string& data) {
            count++;
            benchmark::DoNotOptimize(count);
        });
        benchmark::DoNotOptimize(count);
    }
    
    state.SetComplexityN(num_nodes);
}

// 后代查找基准测试 - 原始Tree
static void BM_OriginalTree_GetDescendants(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    // 预先构建树
    Tree<int, std::string> tree;
    tree.insertRoot(0, "root");
    for (int i = 1; i < num_nodes; ++i) {
        int parent = (i - 1) / 2;
        tree.insertChild(parent, i, "data_" + std::to_string(i));
    }
    
    for (auto _ : state) {
        auto descendants = tree.getDescendantsBFS(0);
        benchmark::DoNotOptimize(descendants);
    }
    
    state.SetComplexityN(num_nodes);
}

// 后代查找基准测试 - 扁平化Tree
static void BM_FlatTree_GetDescendants(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    // 预先构建树
    FlatTree<int, std::string> tree;
    tree.insertRoot(0, "root");
    for (int i = 1; i < num_nodes; ++i) {
        int parent = (i - 1) / 2;
        tree.insertChild(parent, i, "data_" + std::to_string(i));
    }
    
    for (auto _ : state) {
        auto descendants = tree.getDescendantsBFS(0);
        benchmark::DoNotOptimize(descendants);
    }
    
    state.SetComplexityN(num_nodes);
}

// 扁平化Tree并行批量插入基准测试
static void BM_FlatTree_ParallelBatchInsert(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    for (auto _ : state) {
        FlatTree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        auto batch_data = TestDataGenerator::generateBatchInsertData(num_nodes);
        
        benchmark::DoNotOptimize(tree);
        benchmark::DoNotOptimize(batch_data);
        
        auto inserted = tree.parallelBatchInsert(batch_data);
        benchmark::DoNotOptimize(inserted);
        
        benchmark::ClobberMemory();
    }
    
    state.SetComplexityN(num_nodes);
}

// 扁平化Tree并行批量查找基准测试
static void BM_FlatTree_ParallelBatchFind(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    // 预先构建树
    FlatTree<int, std::string> tree;
    tree.insertRoot(0, "root");
    for (int i = 1; i < num_nodes; ++i) {
        int parent = (i - 1) / 2;
        tree.insertChild(parent, i, "data_" + std::to_string(i));
    }
    
    auto keys = TestDataGenerator::generateRandomKeys(num_nodes);
    
    for (auto _ : state) {
        auto results = tree.parallelBatchFind(keys);
        benchmark::DoNotOptimize(results);
    }
    
    state.SetComplexityN(num_nodes);
}

// 内存使用对比测试
static void BM_OriginalTree_MemoryUsage(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    for (auto _ : state) {
        state.PauseTiming();
        
        Tree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        state.ResumeTiming();
        
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;
            tree.insertChild(parent, i, "data_" + std::to_string(i));
        }
        
        benchmark::DoNotOptimize(tree);
        benchmark::ClobberMemory();
    }
    
    state.SetComplexityN(num_nodes);
}

static void BM_FlatTree_MemoryUsage(benchmark::State& state) {
    const int num_nodes = state.range(0);
    
    for (auto _ : state) {
        state.PauseTiming();
        
        FlatTree<int, std::string> tree;
        tree.insertRoot(0, "root");
        
        state.ResumeTiming();
        
        for (int i = 1; i < num_nodes; ++i) {
            int parent = (i - 1) / 2;
            tree.insertChild(parent, i, "data_" + std::to_string(i));
        }
        
        benchmark::DoNotOptimize(tree);
        benchmark::ClobberMemory();
    }
    
    state.SetComplexityN(num_nodes);
}

// 注册基准测试
BENCHMARK(BM_OriginalTree_Insert)->Range(100, 10000)->Complexity();
BENCHMARK(BM_FlatTree_Insert)->Range(100, 10000)->Complexity();

BENCHMARK(BM_OriginalTree_Find)->Range(100, 10000)->Complexity();
BENCHMARK(BM_FlatTree_Find)->Range(100, 10000)->Complexity();

BENCHMARK(BM_OriginalTree_TraverseBFS)->Range(100, 10000)->Complexity();
BENCHMARK(BM_FlatTree_TraverseBFS)->Range(100, 10000)->Complexity();

BENCHMARK(BM_OriginalTree_GetDescendants)->Range(100, 10000)->Complexity();
BENCHMARK(BM_FlatTree_GetDescendants)->Range(100, 10000)->Complexity();

BENCHMARK(BM_FlatTree_ParallelBatchInsert)->Range(100, 10000)->Complexity();
BENCHMARK(BM_FlatTree_ParallelBatchFind)->Range(100, 10000)->Complexity();

BENCHMARK(BM_OriginalTree_MemoryUsage)->Range(100, 10000)->Complexity();
BENCHMARK(BM_FlatTree_MemoryUsage)->Range(100, 10000)->Complexity();

BENCHMARK_MAIN();
