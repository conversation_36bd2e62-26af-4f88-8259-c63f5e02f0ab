#pragma once

#include <QtTest/QtTest>
#include "../Tree.h"
#include <string>
#include <vector>
#include <algorithm>

using namespace common;

/**
 * @brief 树形结构单元测试类
 */
class TreeTest : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 基本操作测试
    void testInsertRoot();
    void testInsertChild();
    void testInsertDuplicateKey();
    void testInsertChildWithInvalidParent();
    
    // 查找操作测试
    void testFindNode();
    void testFindNonExistentNode();
    void testContains();
    
    // 删除操作测试
    void testRemoveNode();
    void testRemoveRoot();
    void testRemoveNonExistentNode();
    
    // 树属性测试
    void testEmpty();
    void testSize();
    void testClear();
    void testGetDepth();
    void testGetMaxDepth();
    
    // 后代查找测试
    void testGetDescendantsBFS();
    void testGetDescendantsDFS();
    void testGetDescendantsWithInvalidParent();
    
    // 遍历测试
    void testBFSIterator();
    void testDFSIterator();
    void testTraverseBFS();
    void testTraverseDFS();
    
    // 节点操作测试
    void testNodeOperations();
    void testNodeParentChildRelationship();

private:
    Tree<int, std::string>* tree;
    
    // 辅助方法
    void setupSampleTree();
    std::vector<int> collectKeys(const std::vector<typename Tree<int, std::string>::NodePtr>& nodes);
};

/**
 * @brief 测试用例初始化
 */
void TreeTest::initTestCase() {
    // 测试套件初始化
}

/**
 * @brief 测试用例清理
 */
void TreeTest::cleanupTestCase() {
    // 测试套件清理
}

/**
 * @brief 每个测试方法前的初始化
 */
void TreeTest::init() {
    tree = new Tree<int, std::string>();
}

/**
 * @brief 每个测试方法后的清理
 */
void TreeTest::cleanup() {
    delete tree;
    tree = nullptr;
}

/**
 * @brief 设置示例树结构
 * 树结构：
 *       1
 *      / \
 *     2   3
 *    /   / \
 *   4   5   6
 */
void TreeTest::setupSampleTree() {
    tree->insertRoot(1, "root");
    tree->insertChild(1, 2, "child1");
    tree->insertChild(1, 3, "child2");
    tree->insertChild(2, 4, "grandchild1");
    tree->insertChild(3, 5, "grandchild2");
    tree->insertChild(3, 6, "grandchild3");
}

/**
 * @brief 从节点指针列表中提取键值
 */
std::vector<int> TreeTest::collectKeys(const std::vector<typename Tree<int, std::string>::NodePtr>& nodes) {
    std::vector<int> keys;
    for (const auto& node : nodes) {
        keys.push_back(node->getKey());
    }
    return keys;
}

/**
 * @brief 测试插入根节点
 */
void TreeTest::testInsertRoot() {
    auto root = tree->insertRoot(1, "root");
    
    QVERIFY(root != nullptr);
    QCOMPARE(root->getKey(), 1);
    QCOMPARE(root->getData(), std::string("root"));
    QVERIFY(root->isRoot());
    QVERIFY(root->isLeaf());
    QCOMPARE(tree->size(), static_cast<size_t>(1));
    QVERIFY(!tree->empty());
}

/**
 * @brief 测试插入子节点
 */
void TreeTest::testInsertChild() {
    tree->insertRoot(1, "root");
    auto child = tree->insertChild(1, 2, "child");
    
    QVERIFY(child != nullptr);
    QCOMPARE(child->getKey(), 2);
    QCOMPARE(child->getData(), std::string("child"));
    QVERIFY(!child->isRoot());
    QVERIFY(child->isLeaf());
    QCOMPARE(tree->size(), static_cast<size_t>(2));
    
    auto root = tree->getRoot();
    QVERIFY(!root->isLeaf());
    QCOMPARE(root->getChildrenCount(), static_cast<size_t>(1));
}

/**
 * @brief 测试插入重复键
 */
void TreeTest::testInsertDuplicateKey() {
    tree->insertRoot(1, "root");
    
    QVERIFY_EXCEPTION_THROWN(tree->insertRoot(2, "another_root"), std::runtime_error);
    QVERIFY_EXCEPTION_THROWN(tree->insertChild(1, 1, "duplicate"), std::runtime_error);
}

/**
 * @brief 测试插入子节点到不存在的父节点
 */
void TreeTest::testInsertChildWithInvalidParent() {
    QVERIFY_EXCEPTION_THROWN(tree->insertChild(999, 1, "child"), std::runtime_error);
}

/**
 * @brief 测试查找节点
 */
void TreeTest::testFindNode() {
    setupSampleTree();
    
    auto node = tree->findNode(3);
    QVERIFY(node != nullptr);
    QCOMPARE(node->getKey(), 3);
    QCOMPARE(node->getData(), std::string("child2"));
}

/**
 * @brief 测试查找不存在的节点
 */
void TreeTest::testFindNonExistentNode() {
    setupSampleTree();
    
    auto node = tree->findNode(999);
    QVERIFY(node == nullptr);
}

/**
 * @brief 测试包含检查
 */
void TreeTest::testContains() {
    setupSampleTree();
    
    QVERIFY(tree->contains(1));
    QVERIFY(tree->contains(6));
    QVERIFY(!tree->contains(999));
}

/**
 * @brief 测试删除节点
 */
void TreeTest::testRemoveNode() {
    setupSampleTree();
    size_t originalSize = tree->size();
    
    // 删除叶子节点
    QVERIFY(tree->removeNode(4));
    QCOMPARE(tree->size(), originalSize - 1);
    QVERIFY(!tree->contains(4));
    
    // 删除有子节点的节点（应该删除整个子树）
    QVERIFY(tree->removeNode(3));
    QVERIFY(!tree->contains(3));
    QVERIFY(!tree->contains(5));
    QVERIFY(!tree->contains(6));
}

/**
 * @brief 测试删除根节点
 */
void TreeTest::testRemoveRoot() {
    setupSampleTree();
    
    QVERIFY(tree->removeNode(1));
    QVERIFY(tree->empty());
    QCOMPARE(tree->size(), static_cast<size_t>(0));
}

/**
 * @brief 测试删除不存在的节点
 */
void TreeTest::testRemoveNonExistentNode() {
    setupSampleTree();
    
    QVERIFY(!tree->removeNode(999));
}

/**
 * @brief 测试空树检查
 */
void TreeTest::testEmpty() {
    QVERIFY(tree->empty());
    
    tree->insertRoot(1, "root");
    QVERIFY(!tree->empty());
    
    tree->clear();
    QVERIFY(tree->empty());
}

/**
 * @brief 测试树大小
 */
void TreeTest::testSize() {
    QCOMPARE(tree->size(), static_cast<size_t>(0));
    
    setupSampleTree();
    QCOMPARE(tree->size(), static_cast<size_t>(6));
}

/**
 * @brief 测试清空树
 */
void TreeTest::testClear() {
    setupSampleTree();
    QVERIFY(!tree->empty());
    
    tree->clear();
    QVERIFY(tree->empty());
    QCOMPARE(tree->size(), static_cast<size_t>(0));
    QVERIFY(tree->getRoot() == nullptr);
}

/**
 * @brief 测试获取节点深度
 */
void TreeTest::testGetDepth() {
    setupSampleTree();
    
    QCOMPARE(tree->getDepth(1), 0);  // root
    QCOMPARE(tree->getDepth(2), 1);  // child
    QCOMPARE(tree->getDepth(4), 2);  // grandchild
    QCOMPARE(tree->getDepth(999), -1);  // non-existent
}

/**
 * @brief 测试获取最大深度
 */
void TreeTest::testGetMaxDepth() {
    QCOMPARE(tree->getMaxDepth(), -1);  // empty tree
    
    tree->insertRoot(1, "root");
    QCOMPARE(tree->getMaxDepth(), 0);   // only root
    
    setupSampleTree();
    QCOMPARE(tree->getMaxDepth(), 2);   // root -> child -> grandchild
}

/**
 * @brief 测试广度优先后代查找
 */
void TreeTest::testGetDescendantsBFS() {
    setupSampleTree();

    auto descendants = tree->getDescendantsBFS(1);
    std::vector<int> expected = {2, 3, 4, 5, 6};
    QCOMPARE(descendants, expected);

    descendants = tree->getDescendantsBFS(3);
    expected = {5, 6};
    QCOMPARE(descendants, expected);

    descendants = tree->getDescendantsBFS(4);
    QVERIFY(descendants.empty());

    descendants = tree->getDescendantsBFS(999);
    QVERIFY(descendants.empty());
}

/**
 * @brief 测试深度优先后代查找
 */
void TreeTest::testGetDescendantsDFS() {
    setupSampleTree();

    auto descendants = tree->getDescendantsDFS(1);
    std::vector<int> expected = {2, 4, 3, 5, 6};
    QCOMPARE(descendants, expected);

    descendants = tree->getDescendantsDFS(3);
    expected = {5, 6};
    QCOMPARE(descendants, expected);
}

/**
 * @brief 测试无效父节点的后代查找
 */
void TreeTest::testGetDescendantsWithInvalidParent() {
    setupSampleTree();

    auto descendants = tree->getDescendantsBFS(999);
    QVERIFY(descendants.empty());

    descendants = tree->getDescendantsDFS(999);
    QVERIFY(descendants.empty());
}

/**
 * @brief 测试广度优先迭代器
 */
void TreeTest::testBFSIterator() {
    setupSampleTree();

    std::vector<int> keys;
    for (auto it = tree->beginBFS(); it != tree->endBFS(); ++it) {
        keys.push_back((*it)->getKey());
    }

    std::vector<int> expected = {1, 2, 3, 4, 5, 6};
    QCOMPARE(keys, expected);
}

/**
 * @brief 测试深度优先迭代器
 */
void TreeTest::testDFSIterator() {
    setupSampleTree();

    std::vector<int> keys;
    for (auto it = tree->beginDFS(); it != tree->endDFS(); ++it) {
        keys.push_back((*it)->getKey());
    }

    std::vector<int> expected = {1, 2, 4, 3, 5, 6};
    QCOMPARE(keys, expected);
}

/**
 * @brief 测试广度优先遍历
 */
void TreeTest::testTraverseBFS() {
    setupSampleTree();

    std::vector<int> keys;
    tree->traverseBFS([&keys](const auto& node) {
        keys.push_back(node->getKey());
    });

    std::vector<int> expected = {1, 2, 3, 4, 5, 6};
    QCOMPARE(keys, expected);
}

/**
 * @brief 测试深度优先遍历
 */
void TreeTest::testTraverseDFS() {
    setupSampleTree();

    std::vector<int> keys;
    tree->traverseDFS([&keys](const auto& node) {
        keys.push_back(node->getKey());
    });

    std::vector<int> expected = {1, 2, 4, 3, 5, 6};
    QCOMPARE(keys, expected);
}

/**
 * @brief 测试节点操作
 */
void TreeTest::testNodeOperations() {
    auto root = tree->insertRoot(1, "root");
    auto child = tree->insertChild(1, 2, "child");

    // 测试节点属性
    QCOMPARE(root->getKey(), 1);
    QCOMPARE(root->getData(), std::string("root"));
    QVERIFY(root->isRoot());
    QVERIFY(!root->isLeaf());

    QCOMPARE(child->getKey(), 2);
    QCOMPARE(child->getData(), std::string("child"));
    QVERIFY(!child->isRoot());
    QVERIFY(child->isLeaf());

    // 测试数据修改
    child->setData("modified_child");
    QCOMPARE(child->getData(), std::string("modified_child"));
}

/**
 * @brief 测试节点父子关系
 */
void TreeTest::testNodeParentChildRelationship() {
    setupSampleTree();

    auto root = tree->findNode(1);
    auto child2 = tree->findNode(2);
    auto child3 = tree->findNode(3);
    auto grandchild4 = tree->findNode(4);

    // 测试父子关系
    QVERIFY(root->getParent() == nullptr);
    QCOMPARE(child2->getParent(), root);
    QCOMPARE(child3->getParent(), root);
    QCOMPARE(grandchild4->getParent(), child2);

    // 测试子节点查找
    QCOMPARE(root->findChild(2), child2);
    QCOMPARE(root->findChild(3), child3);
    QCOMPARE(child2->findChild(4), grandchild4);
    QVERIFY(root->findChild(999) == nullptr);

    // 测试子节点数量
    QCOMPARE(root->getChildrenCount(), static_cast<size_t>(2));
    QCOMPARE(child2->getChildrenCount(), static_cast<size_t>(1));
    QCOMPARE(child3->getChildrenCount(), static_cast<size_t>(2));
    QCOMPARE(grandchild4->getChildrenCount(), static_cast<size_t>(0));
}
