{"version": 3, "configurePresets": [{"name": "macos", "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "<PERSON>"}, "cacheVariables": {"n--CMAKE_C_COMPILER": "/opt/homebrew/bin/gcc-15", "n--CMAKE_CXX_COMPILER": "/opt/homebrew/bin/g++-15", "CMAKE_C_COMPILER_LAUNCHER": "ccache", "CMAKE_CXX_COMPILER_LAUNCHER": "ccache", "CMAKE_BUILD_TYPE": "RelWithDebInfo", "CMAKE_OSX_ARCHITECTURES": "arm64", "BUILD_SHARED_LIBS": "ON", "CMAKE_RUNTIME_OUTPUT_DIRECTORY": "/Users/<USER>/git/wiztop/3d/wizdesignernew/src/source/ui/wizDesignerApp/cmake-build-macos", "CMAKE_LIBRARY_OUTPUT_DIRECTORY": "/Users/<USER>/git/wiztop/3d/wizdesignernew/src/source/ui/wizDesignerApp/cmake-build-macos", "CMAKE_TOOLCHAIN_FILE": "$env{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake", "CMAKE_OSX_SYSROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk", "n--VCPKG_TARGET_TRIPLET": "arm64-osx-gcc-15"}, "environment": {"VCPKG_ROOT": "/Users/<USER>/devtools/vcpkg", "Qt5_DIR": "/opt/homebrew/Cellar/qt@5/5.15.17/lib/cmake/Qt5"}}, {"name": "windows", "generator": "Visual Studio 17 2022", "architecture": "x64", "binaryDir": "${sourceDir}/build/Win64", "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}, "cacheVariables": {"CMAKE_SYSTEM_VERSION": "10.0", "CMAKE_PREFIX_PATH": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64", "BUILD_SHARED_LIBS": "ON"}}, {"name": "linux-release", "generator": "Unix Makefiles", "displayName": "Linux GCC X64 Release", "binaryDir": "${sourceDir}/build/Linux64", "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Linux"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "CMAKE_PREFIX_PATH": "~/Qt/6.2.4/gcc_64/", "BUILD_SHARED_LIBS": "ON"}}, {"name": "linux-relwithdebinfo", "inherits": "linux-release", "displayName": "Linux GCC X64 RelWithDebInfo", "binaryDir": "${sourceDir}/build/Linux64RelWithDebInfo", "cacheVariables": {"CMAKE_BUILD_TYPE": "RelWithDebInfo"}}, {"name": "linux-debug", "inherits": "linux-release", "displayName": "Linux GCC X64 Debug", "binaryDir": "${sourceDir}/build/Linux64Debug", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}]}