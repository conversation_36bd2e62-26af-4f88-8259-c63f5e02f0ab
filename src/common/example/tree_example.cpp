#include <iostream>
#include <string>
#include <vector>
#include "../Tree.h"

using namespace common;

/**
 * @brief 树形结构使用示例
 */
int main() {
    std::cout << "=== 树形结构使用示例 ===" << std::endl;
    
    // 创建一个以int为键，string为数据的树
    Tree<int, std::string> tree;
    
    try {
        // 1. 构建树结构
        std::cout << "\n1. 构建树结构:" << std::endl;
        tree.insertRoot(1, "根节点");
        tree.insertChild(1, 2, "子节点1");
        tree.insertChild(1, 3, "子节点2");
        tree.insertChild(2, 4, "孙节点1");
        tree.insertChild(2, 5, "孙节点2");
        tree.insertChild(3, 6, "孙节点3");
        tree.insertChild(3, 7, "孙节点4");
        tree.insertChild(4, 8, "曾孙节点1");
        
        std::cout << "树构建完成，共有 " << tree.size() << " 个节点" << std::endl;
        std::cout << "树的最大深度: " << tree.getMaxDepth() << std::endl;
        
        // 2. 广度优先遍历
        std::cout << "\n2. 广度优先遍历 (BFS):" << std::endl;
        tree.traverseBFS([](const auto& node) {
            std::cout << "节点 " << node->getKey() << ": " << node->getData() 
                      << " (深度: " << (node->isRoot() ? 0 : 1) << ")" << std::endl;
        });
        
        // 3. 深度优先遍历
        std::cout << "\n3. 深度优先遍历 (DFS):" << std::endl;
        tree.traverseDFS([](const auto& node) {
            std::cout << "节点 " << node->getKey() << ": " << node->getData() << std::endl;
        });
        
        // 4. 使用迭代器进行遍历
        std::cout << "\n4. 使用BFS迭代器遍历:" << std::endl;
        for (auto it = tree.beginBFS(); it != tree.endBFS(); ++it) {
            auto node = *it;
            std::cout << "键: " << node->getKey() << ", 数据: " << node->getData() << std::endl;
        }
        
        // 5. 查找节点的所有后代（非递归）
        std::cout << "\n5. 查找节点2的所有后代 (BFS):" << std::endl;
        auto descendants = tree.getDescendantsBFS(2);
        std::cout << "节点2的后代: ";
        for (int key : descendants) {
            std::cout << key << " ";
        }
        std::cout << std::endl;
        
        std::cout << "\n6. 查找节点1的所有后代 (DFS):" << std::endl;
        descendants = tree.getDescendantsDFS(1);
        std::cout << "节点1的后代 (DFS顺序): ";
        for (int key : descendants) {
            std::cout << key << " ";
        }
        std::cout << std::endl;
        
        // 7. 节点查找和属性检查
        std::cout << "\n7. 节点查找和属性检查:" << std::endl;
        auto node = tree.findNode(4);
        if (node) {
            std::cout << "找到节点4: " << node->getData() << std::endl;
            std::cout << "是否为根节点: " << (node->isRoot() ? "是" : "否") << std::endl;
            std::cout << "是否为叶子节点: " << (node->isLeaf() ? "是" : "否") << std::endl;
            std::cout << "子节点数量: " << node->getChildrenCount() << std::endl;
            std::cout << "节点深度: " << tree.getDepth(4) << std::endl;
            
            auto parent = node->getParent();
            if (parent) {
                std::cout << "父节点: " << parent->getKey() << " (" << parent->getData() << ")" << std::endl;
            }
        }
        
        // 8. 删除节点测试
        std::cout << "\n8. 删除节点测试:" << std::endl;
        std::cout << "删除前树的大小: " << tree.size() << std::endl;
        
        // 删除节点3及其所有后代
        if (tree.removeNode(3)) {
            std::cout << "成功删除节点3及其后代" << std::endl;
            std::cout << "删除后树的大小: " << tree.size() << std::endl;
            
            std::cout << "删除后的树结构 (BFS):" << std::endl;
            tree.traverseBFS([](const auto& node) {
                std::cout << "节点 " << node->getKey() << ": " << node->getData() << std::endl;
            });
        }
        
        // 9. 演示不同数据类型的树
        std::cout << "\n9. 字符串键的树示例:" << std::endl;
        Tree<std::string, int> stringTree;
        stringTree.insertRoot("root", 100);
        stringTree.insertChild("root", "child1", 200);
        stringTree.insertChild("root", "child2", 300);
        stringTree.insertChild("child1", "grandchild", 400);
        
        std::cout << "字符串键树的遍历:" << std::endl;
        stringTree.traverseBFS([](const auto& node) {
            std::cout << "键: \"" << node->getKey() << "\", 值: " << node->getData() << std::endl;
        });
        
        // 10. 性能测试（构建大树）
        std::cout << "\n10. 性能测试 - 构建大树:" << std::endl;
        Tree<int, int> bigTree;
        bigTree.insertRoot(0, 0);
        
        // 构建一个有1000个节点的树
        for (int i = 1; i < 1000; ++i) {
            int parent = (i - 1) / 2;  // 构建近似平衡的二叉树
            bigTree.insertChild(parent, i, i * 10);
        }
        
        std::cout << "大树构建完成，节点数: " << bigTree.size() << std::endl;
        std::cout << "大树最大深度: " << bigTree.getMaxDepth() << std::endl;
        
        // 测试大树的后代查找性能
        auto bigDescendants = bigTree.getDescendantsBFS(0);
        std::cout << "根节点的后代数量: " << bigDescendants.size() << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n=== 示例程序结束 ===" << std::endl;
    return 0;
}
