#pragma once

#include <memory>
#include <vector>
#include <unordered_map>

namespace common {

/**
 * @brief 树节点模板类
 * @tparam KeyType 节点键的类型
 * @tparam DataType 节点数据的类型
 */
template<typename KeyType, typename DataType = void*>
class TreeNode : public std::enable_shared_from_this<TreeNode<KeyType, DataType>> {
public:
    using NodePtr = std::shared_ptr<TreeNode<KeyType, DataType>>;
    using WeakNodePtr = std::weak_ptr<TreeNode<KeyType, DataType>>;
    using ChildrenContainer = std::vector<NodePtr>;

    /**
     * @brief 构造函数
     * @param key 节点键
     * @param data 节点数据
     */
    explicit TreeNode(const KeyType& key, const DataType& data = DataType{})
        : key_(key), data_(data) {}

    /**
     * @brief 获取节点键
     * @return 节点键的常量引用
     */
    const KeyType& getKey() const { return key_; }

    /**
     * @brief 获取节点数据
     * @return 节点数据的引用
     */
    DataType& getData() { return data_; }
    const DataType& getData() const { return data_; }

    /**
     * @brief 设置节点数据
     * @param data 新的节点数据
     */
    void setData(const DataType& data) { data_ = data; }

    /**
     * @brief 获取父节点
     * @return 父节点的共享指针，如果是根节点则返回nullptr
     */
    NodePtr getParent() const { return parent_.lock(); }

    /**
     * @brief 设置父节点
     * @param parent 父节点的共享指针
     */
    void setParent(const NodePtr& parent) { parent_ = parent; }

    /**
     * @brief 获取子节点列表
     * @return 子节点列表的常量引用
     */
    const ChildrenContainer& getChildren() const { return children_; }

    /**
     * @brief 添加子节点
     * @param child 要添加的子节点
     */
    void addChild(const NodePtr& child) {
        if (child) {
            children_.push_back(child);
            child->setParent(this->shared_from_this());
        }
    }

    /**
     * @brief 移除子节点
     * @param key 要移除的子节点的键
     * @return 是否成功移除
     */
    bool removeChild(const KeyType& key) {
        auto it = std::find_if(children_.begin(), children_.end(),
            [&key](const NodePtr& child) {
                return child && child->getKey() == key;
            });
        
        if (it != children_.end()) {
            (*it)->setParent(nullptr);
            children_.erase(it);
            return true;
        }
        return false;
    }

    /**
     * @brief 查找子节点
     * @param key 要查找的子节点的键
     * @return 找到的子节点，如果未找到则返回nullptr
     */
    NodePtr findChild(const KeyType& key) const {
        auto it = std::find_if(children_.begin(), children_.end(),
            [&key](const NodePtr& child) {
                return child && child->getKey() == key;
            });
        
        return (it != children_.end()) ? *it : nullptr;
    }

    /**
     * @brief 检查是否为叶子节点
     * @return 如果是叶子节点返回true，否则返回false
     */
    bool isLeaf() const { return children_.empty(); }

    /**
     * @brief 检查是否为根节点
     * @return 如果是根节点返回true，否则返回false
     */
    bool isRoot() const { return parent_.expired(); }

    /**
     * @brief 获取子节点数量
     * @return 子节点数量
     */
    size_t getChildrenCount() const { return children_.size(); }

    /**
     * @brief 清空所有子节点
     */
    void clearChildren() {
        for (auto& child : children_) {
            if (child) {
                child->setParent(nullptr);
            }
        }
        children_.clear();
    }

private:
    KeyType key_;                    ///< 节点键
    DataType data_;                  ///< 节点数据
    WeakNodePtr parent_;             ///< 父节点（弱引用避免循环引用）
    ChildrenContainer children_;     ///< 子节点列表
};

} // namespace common
