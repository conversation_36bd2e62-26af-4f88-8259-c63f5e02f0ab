# 树形结构模块 (TreeModule)

一个基于C++17标准的高效、模板化树形结构实现，支持任意键类型和非递归遍历操作。

## 特性

- **模板化设计**: 支持任意键类型和数据类型
- **C++17标准**: 使用现代C++特性
- **非递归实现**: 所有遍历和查找操作都采用非递归方式
- **高效遍历**: 支持广度优先(BFS)和深度优先(DFS)遍历
- **迭代器支持**: 提供STL风格的迭代器接口
- **内存安全**: 使用智能指针管理内存，避免内存泄漏
- **完整测试**: 包含全面的单元测试

## 核心组件

### TreeNode<KeyType, DataType>
树节点模板类，包含：
- 节点键值和数据
- 父子关系管理
- 节点属性查询（是否为根节点、叶子节点等）

### Tree<KeyType, DataType>
树形结构主类，提供：
- 节点插入、删除、查找
- 非递归后代查找
- 树属性查询（大小、深度等）
- 遍历操作

### TreeIterator
迭代器类，支持：
- 广度优先遍历迭代器 (TreeBFSIterator)
- 深度优先遍历迭代器 (TreeDFSIterator)

## 快速开始

### 基本用法

```cpp
#include "Tree.h"
#include <string>

using namespace common;

// 创建一个以int为键，string为数据的树
Tree<int, std::string> tree;

// 插入根节点
tree.insertRoot(1, "根节点");

// 插入子节点
tree.insertChild(1, 2, "子节点1");
tree.insertChild(1, 3, "子节点2");
tree.insertChild(2, 4, "孙节点");

// 查找节点
auto node = tree.findNode(2);
if (node) {
    std::cout << "找到节点: " << node->getData() << std::endl;
}
```

### 遍历操作

```cpp
// 广度优先遍历
tree.traverseBFS([](const auto& node) {
    std::cout << "节点: " << node->getKey() << std::endl;
});

// 深度优先遍历
tree.traverseDFS([](const auto& node) {
    std::cout << "节点: " << node->getKey() << std::endl;
});

// 使用迭代器
for (auto it = tree.beginBFS(); it != tree.endBFS(); ++it) {
    std::cout << "键: " << (*it)->getKey() << std::endl;
}
```

### 非递归后代查找

```cpp
// 获取节点1的所有后代（广度优先）
auto descendants = tree.getDescendantsBFS(1);
for (int key : descendants) {
    std::cout << "后代: " << key << std::endl;
}

// 获取节点1的所有后代（深度优先）
auto descendantsDFS = tree.getDescendantsDFS(1);
```

## 构建和测试

### 独立构建

```bash
cd src/common
mkdir build
cd build
cmake ..
make
```

### 运行测试

```bash
# 构建测试
make TreeModuleTests

# 运行测试
./TreeModuleTests

# 或使用CTest
ctest
```

### 运行示例

```bash
# 构建示例程序
make TreeExample

# 运行示例
./example/TreeExample
```

### 构建选项

- `BUILD_TREE_TESTS`: 构建测试程序 (默认: ON)
- `BUILD_TREE_EXAMPLES`: 构建示例程序 (默认: ON)

```bash
# 禁用测试构建
cmake -DBUILD_TREE_TESTS=OFF ..

# 禁用示例构建
cmake -DBUILD_TREE_EXAMPLES=OFF ..
```

## API 参考

### Tree类主要方法

#### 节点操作
- `insertRoot(key, data)`: 插入根节点
- `insertChild(parentKey, key, data)`: 插入子节点
- `removeNode(key)`: 删除节点及其后代
- `findNode(key)`: 查找节点

#### 树属性
- `empty()`: 检查树是否为空
- `size()`: 获取节点总数
- `getDepth(key)`: 获取节点深度
- `getMaxDepth()`: 获取树的最大深度
- `contains(key)`: 检查节点是否存在

#### 遍历操作
- `beginBFS()` / `endBFS()`: BFS迭代器
- `beginDFS()` / `endDFS()`: DFS迭代器
- `traverseBFS(func)`: BFS遍历
- `traverseDFS(func)`: DFS遍历

#### 后代查找
- `getDescendantsBFS(parentKey)`: 获取后代键列表(BFS)
- `getDescendantsDFS(parentKey)`: 获取后代键列表(DFS)
- `getDescendantNodesBFS(parentKey)`: 获取后代节点(BFS)
- `getDescendantNodesDFS(parentKey)`: 获取后代节点(DFS)

### TreeNode类主要方法

- `getKey()`: 获取节点键
- `getData()` / `setData()`: 获取/设置节点数据
- `getParent()`: 获取父节点
- `getChildren()`: 获取子节点列表
- `isRoot()`: 是否为根节点
- `isLeaf()`: 是否为叶子节点
- `getChildrenCount()`: 获取子节点数量

## 性能特点

- **时间复杂度**:
  - 插入/删除/查找: O(1) 平均情况
  - 遍历: O(n)
  - 后代查找: O(k) 其中k为后代数量

- **空间复杂度**: O(n) 其中n为节点总数

- **非递归实现**: 避免栈溢出，支持深层树结构

## 依赖项

- C++17 编译器
- Qt5 Core (用于测试框架)
- CMake 3.16+

## 许可证

本模块作为BaseWidget项目的一部分，遵循项目的许可证条款。
