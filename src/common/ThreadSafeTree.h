#pragma once

#include "TreeNode.h"
#include "TreeIterator.h"
#include <unordered_map>
#include <queue>
#include <stack>
#include <vector>
#include <functional>
#include <stdexcept>
#include <shared_mutex>
#include <atomic>
#include <thread>
#include <mutex>

namespace common {

/**
 * @brief 线程安全的树形结构模板类
 * @tparam KeyType 节点键的类型
 * @tparam DataType 节点数据的类型
 */
template<typename KeyType, typename DataType = void*>
class ThreadSafeTree {
public:
    using NodeType = TreeNode<KeyType, DataType>;
    using NodePtr = typename NodeType::NodePtr;
    using NodeMap = std::unordered_map<KeyType, NodePtr>;
    
    /**
     * @brief 默认构造函数
     */
    ThreadSafeTree() = default;
    
    /**
     * @brief 析构函数
     */
    ~ThreadSafeTree() = default;
    
    /**
     * @brief 插入根节点（线程安全）
     */
    bool insertRoot(const KeyType& key, const DataType& data = DataType{}) {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        if (root_) {
            return false;  // 根节点已存在
        }
        
        root_ = std::make_shared<NodeType>(key, data);
        nodes_[key] = root_;
        return true;
    }
    
    /**
     * @brief 插入子节点（线程安全）
     */
    bool insertChild(const KeyType& parentKey, const KeyType& key, 
                    const DataType& data = DataType{}) {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        // 查找父节点
        auto parentIt = nodes_.find(parentKey);
        if (parentIt == nodes_.end()) {
            return false;
        }
        
        // 检查子节点是否已存在
        if (nodes_.find(key) != nodes_.end()) {
            return false;
        }
        
        // 创建新节点
        auto newNode = std::make_shared<NodeType>(key, data);
        newNode->setParent(parentIt->second);
        parentIt->second->addChild(newNode);
        
        nodes_[key] = newNode;
        return true;
    }
    
    /**
     * @brief 查找节点（线程安全）
     */
    NodePtr findNode(const KeyType& key) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        auto it = nodes_.find(key);
        return (it != nodes_.end()) ? it->second : nullptr;
    }
    
    /**
     * @brief 检查节点是否存在（线程安全）
     */
    bool contains(const KeyType& key) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return nodes_.find(key) != nodes_.end();
    }
    
    /**
     * @brief 删除节点（线程安全）
     */
    bool removeNode(const KeyType& key) {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        auto it = nodes_.find(key);
        if (it == nodes_.end()) {
            return false;
        }
        
        NodePtr nodeToRemove = it->second;
        
        // 不能删除根节点
        if (nodeToRemove == root_) {
            return false;
        }
        
        // 将子节点重新连接到父节点
        auto parent = nodeToRemove->getParent().lock();
        if (parent) {
            parent->removeChild(nodeToRemove);
            
            // 将被删除节点的子节点添加到父节点
            auto children = nodeToRemove->getChildren();
            for (auto& child : children) {
                if (auto childPtr = child.lock()) {
                    childPtr->setParent(parent);
                    parent->addChild(childPtr);
                }
            }
        }
        
        // 从映射中移除
        nodes_.erase(it);
        return true;
    }
    
    /**
     * @brief 获取树大小（线程安全）
     */
    size_t size() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return nodes_.size();
    }
    
    /**
     * @brief 检查树是否为空（线程安全）
     */
    bool empty() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return nodes_.empty();
    }
    
    /**
     * @brief BFS遍历（线程安全）
     */
    void traverseBFS(std::function<void(const NodePtr&)> func) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        if (!root_) return;
        
        std::queue<NodePtr> queue;
        queue.push(root_);
        
        while (!queue.empty()) {
            NodePtr current = queue.front();
            queue.pop();
            
            func(current);
            
            auto children = current->getChildren();
            for (auto& child : children) {
                if (auto childPtr = child.lock()) {
                    queue.push(childPtr);
                }
            }
        }
    }
    
    /**
     * @brief DFS遍历（线程安全）
     */
    void traverseDFS(std::function<void(const NodePtr&)> func) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        if (!root_) return;
        
        std::stack<NodePtr> stack;
        stack.push(root_);
        
        while (!stack.empty()) {
            NodePtr current = stack.top();
            stack.pop();
            
            func(current);
            
            auto children = current->getChildren();
            // 反向插入以保持顺序
            for (auto it = children.rbegin(); it != children.rend(); ++it) {
                if (auto childPtr = it->lock()) {
                    stack.push(childPtr);
                }
            }
        }
    }
    
    /**
     * @brief BFS查找后代（线程安全）
     */
    std::vector<KeyType> getDescendantsBFS(const KeyType& parentKey) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        std::vector<KeyType> descendants;
        
        auto parentIt = nodes_.find(parentKey);
        if (parentIt == nodes_.end()) {
            return descendants;
        }
        
        std::queue<NodePtr> queue;
        auto children = parentIt->second->getChildren();
        
        for (auto& child : children) {
            if (auto childPtr = child.lock()) {
                queue.push(childPtr);
            }
        }
        
        while (!queue.empty()) {
            NodePtr current = queue.front();
            queue.pop();
            descendants.push_back(current->getKey());
            
            auto currentChildren = current->getChildren();
            for (auto& child : currentChildren) {
                if (auto childPtr = child.lock()) {
                    queue.push(childPtr);
                }
            }
        }
        
        return descendants;
    }
    
    /**
     * @brief DFS查找后代（线程安全）
     */
    std::vector<KeyType> getDescendantsDFS(const KeyType& parentKey) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        std::vector<KeyType> descendants;
        
        auto parentIt = nodes_.find(parentKey);
        if (parentIt == nodes_.end()) {
            return descendants;
        }
        
        std::stack<NodePtr> stack;
        auto children = parentIt->second->getChildren();
        
        for (auto it = children.rbegin(); it != children.rend(); ++it) {
            if (auto childPtr = it->lock()) {
                stack.push(childPtr);
            }
        }
        
        while (!stack.empty()) {
            NodePtr current = stack.top();
            stack.pop();
            descendants.push_back(current->getKey());
            
            auto currentChildren = current->getChildren();
            for (auto it = currentChildren.rbegin(); it != currentChildren.rend(); ++it) {
                if (auto childPtr = it->lock()) {
                    stack.push(childPtr);
                }
            }
        }
        
        return descendants;
    }
    
    /**
     * @brief 并行批量插入
     */
    size_t parallelBatchInsert(const std::vector<std::tuple<KeyType, KeyType, DataType>>& nodes) {
        std::atomic<size_t> success_count{0};
        
        const size_t num_threads = std::min(static_cast<size_t>(std::thread::hardware_concurrency()), nodes.size());
        if (num_threads <= 1) {
            for (const auto& node_info : nodes) {
                const auto& [parent_key, key, data] = node_info;
                if (insertChild(parent_key, key, data)) {
                    success_count.fetch_add(1);
                }
            }
        } else {
            std::vector<std::thread> threads;
            const size_t chunk_size = nodes.size() / num_threads;
            
            for (size_t t = 0; t < num_threads; ++t) {
                size_t start = t * chunk_size;
                size_t end = (t == num_threads - 1) ? nodes.size() : (t + 1) * chunk_size;
                
                threads.emplace_back([this, &nodes, &success_count, start, end]() {
                    for (size_t i = start; i < end; ++i) {
                        const auto& [parent_key, key, data] = nodes[i];
                        if (insertChild(parent_key, key, data)) {
                            success_count.fetch_add(1);
                        }
                    }
                });
            }
            
            for (auto& thread : threads) {
                thread.join();
            }
        }
        
        return success_count.load();
    }
    
    /**
     * @brief 获取根节点（线程安全）
     */
    NodePtr getRoot() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return root_;
    }

private:
    NodePtr root_;                           ///< 根节点
    NodeMap nodes_;                          ///< 节点映射表
    mutable std::shared_mutex mutex_;        ///< 读写锁
};

} // namespace common
