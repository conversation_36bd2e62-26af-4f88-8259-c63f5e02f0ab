#pragma once

#include "TreeNode.h"
#include <queue>
#include <stack>
#include <iterator>

namespace common {

/**
 * @brief 树的广度优先遍历迭代器
 * @tparam KeyType 节点键的类型
 * @tparam DataType 节点数据的类型
 */
template<typename KeyType, typename DataType = void*>
class TreeBFSIterator {
public:
    using NodeType = TreeNode<KeyType, DataType>;
    using NodePtr = typename NodeType::NodePtr;
    using iterator_category = std::forward_iterator_tag;
    using value_type = NodePtr;
    using difference_type = std::ptrdiff_t;
    using pointer = NodePtr*;
    using reference = NodePtr&;

    /**
     * @brief 构造函数
     * @param root 根节点
     */
    explicit TreeBFSIterator(const NodePtr& root = nullptr) {
        if (root) {
            queue_.push(root);
            current_ = root;
        }
    }

    /**
     * @brief 解引用操作符
     * @return 当前节点
     */
    NodePtr operator*() const {
        return current_;
    }

    /**
     * @brief 箭头操作符
     * @return 当前节点指针
     */
    NodePtr operator->() const {
        return current_;
    }

    /**
     * @brief 前置递增操作符
     * @return 递增后的迭代器引用
     */
    TreeBFSIterator& operator++() {
        if (!queue_.empty()) {
            NodePtr node = queue_.front();
            queue_.pop();

            // 将当前节点的所有子节点加入队列
            for (const auto& child : node->getChildren()) {
                queue_.push(child);
            }

            // 设置下一个节点
            if (!queue_.empty()) {
                current_ = queue_.front();
            } else {
                current_ = nullptr;
            }
        } else {
            current_ = nullptr;
        }
        return *this;
    }

    /**
     * @brief 后置递增操作符
     * @return 递增前的迭代器副本
     */
    TreeBFSIterator operator++(int) {
        TreeBFSIterator temp = *this;
        ++(*this);
        return temp;
    }

    /**
     * @brief 相等比较操作符
     * @param other 另一个迭代器
     * @return 是否相等
     */
    bool operator==(const TreeBFSIterator& other) const {
        return current_ == other.current_;
    }

    /**
     * @brief 不等比较操作符
     * @param other 另一个迭代器
     * @return 是否不等
     */
    bool operator!=(const TreeBFSIterator& other) const {
        return !(*this == other);
    }

private:
    std::queue<NodePtr> queue_;     ///< BFS队列
    NodePtr current_;               ///< 当前节点
};

/**
 * @brief 树的深度优先遍历迭代器
 * @tparam KeyType 节点键的类型
 * @tparam DataType 节点数据的类型
 */
template<typename KeyType, typename DataType = void*>
class TreeDFSIterator {
public:
    using NodeType = TreeNode<KeyType, DataType>;
    using NodePtr = typename NodeType::NodePtr;
    using iterator_category = std::forward_iterator_tag;
    using value_type = NodePtr;
    using difference_type = std::ptrdiff_t;
    using pointer = NodePtr*;
    using reference = NodePtr&;

    /**
     * @brief 构造函数
     * @param root 根节点
     */
    explicit TreeDFSIterator(const NodePtr& root = nullptr) {
        if (root) {
            stack_.push(root);
            current_ = root;
        }
    }

    /**
     * @brief 解引用操作符
     * @return 当前节点
     */
    NodePtr operator*() const {
        return current_;
    }

    /**
     * @brief 箭头操作符
     * @return 当前节点指针
     */
    NodePtr operator->() const {
        return current_;
    }

    /**
     * @brief 前置递增操作符
     * @return 递增后的迭代器引用
     */
    TreeDFSIterator& operator++() {
        if (!stack_.empty()) {
            NodePtr node = stack_.top();
            stack_.pop();

            // 将当前节点的所有子节点以逆序加入栈中（保证正确的DFS顺序）
            const auto& children = node->getChildren();
            for (auto it = children.rbegin(); it != children.rend(); ++it) {
                stack_.push(*it);
            }

            // 设置下一个节点
            if (!stack_.empty()) {
                current_ = stack_.top();
            } else {
                current_ = nullptr;
            }
        } else {
            current_ = nullptr;
        }
        return *this;
    }

    /**
     * @brief 后置递增操作符
     * @return 递增前的迭代器副本
     */
    TreeDFSIterator operator++(int) {
        TreeDFSIterator temp = *this;
        ++(*this);
        return temp;
    }

    /**
     * @brief 相等比较操作符
     * @param other 另一个迭代器
     * @return 是否相等
     */
    bool operator==(const TreeDFSIterator& other) const {
        return current_ == other.current_;
    }

    /**
     * @brief 不等比较操作符
     * @param other 另一个迭代器
     * @return 是否不等
     */
    bool operator!=(const TreeDFSIterator& other) const {
        return !(*this == other);
    }

private:
    std::stack<NodePtr> stack_;     ///< DFS栈
    NodePtr current_;               ///< 当前节点
};

} // namespace common
