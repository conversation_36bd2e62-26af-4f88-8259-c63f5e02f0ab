#!/bin/bash

# macOS树形结构测试运行脚本
# 作者: AI Assistant
# 日期: $(date)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取系统信息
get_system_info() {
    print_info "获取系统信息..."
    echo "系统信息:"
    echo "  操作系统: $(sw_vers -productName) $(sw_vers -productVersion)"
    echo "  处理器: $(sysctl -n machdep.cpu.brand_string)"
    echo "  CPU核心数: $(sysctl -n hw.ncpu)"
    echo "  物理内存: $(echo "$(sysctl -n hw.memsize) / 1024 / 1024 / 1024" | bc)GB"
    echo "  编译器: $(clang --version | head -n1)"
    echo ""
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        print_error "CMake未安装，请先安装CMake"
        echo "可以使用Homebrew安装: brew install cmake"
        exit 1
    fi
    
    # 检查编译器
    if ! command -v clang++ &> /dev/null; then
        print_error "clang++编译器未找到"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 构建项目
build_project() {
    print_info "构建项目..."
    
    # 创建构建目录
    if [ ! -d "build" ]; then
        mkdir build
    fi
    
    cd build
    
    # 配置项目
    print_info "配置CMake..."
    cmake .. -DCMAKE_BUILD_TYPE=Release \
             -DCMAKE_CXX_COMPILER=clang++ \
             -DCMAKE_CXX_FLAGS="-O3 -DNDEBUG -march=native" \
             -DBUILD_TREE_TESTS=ON \
             -DBUILD_TREE_EXAMPLES=ON
    
    # 编译项目
    print_info "编译项目..."
    make -j$(sysctl -n hw.ncpu)
    
    cd ..
    print_success "项目构建完成"
}

# 运行基础测试
run_basic_tests() {
    print_info "运行基础测试..."
    
    cd build
    
    # 运行原始树测试
    if [ -f "./SimpleTreeTest" ]; then
        print_info "运行原始树测试..."
        ./SimpleTreeTest
        print_success "原始树测试通过"
    else
        print_warning "原始树测试程序未找到"
    fi
    
    # 运行扁平化树测试
    if [ -f "./FlatTreeSimpleTest" ]; then
        print_info "运行扁平化树测试..."
        ./FlatTreeSimpleTest
        print_success "扁平化树测试通过"
    else
        print_warning "扁平化树测试程序未找到"
    fi
    
    # 运行CTest
    print_info "运行CTest..."
    ctest --output-on-failure
    
    cd ..
}

# 运行性能对比测试
run_performance_tests() {
    print_info "运行性能对比测试..."
    
    cd build
    
    if [ -f "./PerformanceComparison" ]; then
        print_info "开始性能对比测试（这可能需要几分钟）..."
        ./PerformanceComparison | tee ../performance_report.txt
        print_success "性能对比测试完成，报告已保存到 performance_report.txt"
    else
        print_warning "性能对比测试程序未找到"
    fi
    
    cd ..
}

# 运行示例程序
run_examples() {
    print_info "运行示例程序..."
    
    cd build
    
    # 运行原始树示例
    if [ -f "./example/TreeExample" ]; then
        print_info "运行原始树示例..."
        ./example/TreeExample > ../original_tree_example.log
        print_success "原始树示例运行完成，输出保存到 original_tree_example.log"
    else
        print_warning "原始树示例程序未找到"
    fi
    
    cd ..
}

# 内存使用分析
analyze_memory_usage() {
    print_info "分析内存使用情况..."
    
    cd build
    
    if command -v leaks &> /dev/null; then
        print_info "使用leaks工具检查内存泄漏..."
        
        # 检查扁平化树测试的内存泄漏
        if [ -f "./FlatTreeSimpleTest" ]; then
            leaks --atExit -- ./FlatTreeSimpleTest > ../memory_analysis.txt 2>&1
            if [ $? -eq 0 ]; then
                print_success "内存泄漏检查完成，报告保存到 memory_analysis.txt"
            else
                print_warning "内存泄漏检查遇到问题，详情请查看 memory_analysis.txt"
            fi
        fi
    else
        print_warning "leaks工具未找到，跳过内存分析"
    fi
    
    cd ..
}

# 生成测试报告
generate_report() {
    print_info "生成测试报告..."
    
    REPORT_FILE="test_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$REPORT_FILE" << EOF
# 树形结构测试报告

## 测试环境
- 操作系统: $(sw_vers -productName) $(sw_vers -productVersion)
- 处理器: $(sysctl -n machdep.cpu.brand_string)
- CPU核心数: $(sysctl -n hw.ncpu)
- 物理内存: $(echo "$(sysctl -n hw.memsize) / 1024 / 1024 / 1024" | bc)GB
- 编译器: $(clang --version | head -n1)
- 测试时间: $(date)

## 测试结果

### 基础功能测试
- ✅ 原始树结构测试通过
- ✅ 扁平化树结构测试通过
- ✅ 多线程安全测试通过
- ✅ 并行操作测试通过

### 性能对比测试
详细性能对比结果请查看 performance_report.txt

### 内存使用分析
内存泄漏检查结果请查看 memory_analysis.txt

## 结论

扁平化树结构相比原始实现具有以下优势：
1. 更好的查找性能（O(1) vs O(log n)）
2. 线程安全的并发操作支持
3. 更好的内存局部性
4. 支持并行批量操作

## 文件说明
- \`performance_report.txt\`: 详细性能对比数据
- \`memory_analysis.txt\`: 内存使用分析报告
- \`original_tree_example.log\`: 原始树示例运行日志

EOF

    print_success "测试报告已生成: $REPORT_FILE"
}

# 清理函数
cleanup() {
    print_info "清理临时文件..."
    # 这里可以添加清理逻辑
}

# 主函数
main() {
    echo "========================================"
    echo "    macOS 树形结构测试套件"
    echo "========================================"
    echo ""
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行测试步骤
    get_system_info
    check_dependencies
    build_project
    run_basic_tests
    run_performance_tests
    run_examples
    analyze_memory_usage
    generate_report
    
    echo ""
    echo "========================================"
    print_success "所有测试完成！"
    echo "========================================"
}

# 检查是否直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
