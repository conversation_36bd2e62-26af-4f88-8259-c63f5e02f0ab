cmake_minimum_required(VERSION 3.16)
project(TreeModule)

# 设置C++17标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 启用Qt的MOC、RCC和UIC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 查找Qt5组件（可选）
find_package(Qt5 COMPONENTS
    Core
    Test
    QUIET)

if(NOT Qt5_FOUND)
    message(WARNING "Qt5 not found. Qt-based tests and examples will be disabled.")
    # 仍然可以构建简单测试
endif()

# 查找Google Test和Benchmark
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(GTEST QUIET gtest)
    pkg_check_modules(BENCHMARK QUIET benchmark)
endif()

# 如果没有找到，尝试使用系统安装的版本
if(NOT GTEST_FOUND)
    find_package(GTest QUIET)
    if(GTest_FOUND)
        set(GTEST_FOUND TRUE)
    endif()
endif()

if(NOT BENCHMARK_FOUND)
    find_package(benchmark QUIET)
    if(benchmark_FOUND)
        set(BENCHMARK_FOUND TRUE)
    endif()
endif()

# 如果仍然没有找到，给出警告
if(NOT GTEST_FOUND)
    message(WARNING "Google Test not found. Advanced tests will be disabled.")
endif()

if(NOT BENCHMARK_FOUND)
    message(WARNING "Google Benchmark not found. Performance benchmarks will be disabled.")
endif()

# 树形结构头文件
set(TREE_HEADERS
    TreeNode.h
    Tree.h
    TreeIterator.h
    FlatTreeNode.h
    FlatTree.h
)

# 创建头文件库（Header-only library）
add_library(TreeModule INTERFACE)

# 设置头文件包含目录
target_include_directories(TreeModule INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include/common>
)

# 链接Qt5库（如果可用）
if(Qt5_FOUND)
    target_link_libraries(TreeModule INTERFACE
        Qt5::Core
    )
endif()

# 设置编译器特性
target_compile_features(TreeModule INTERFACE cxx_std_17)

# 测试和示例相关设置
option(BUILD_TREE_TESTS "Build tree module tests" ON)
option(BUILD_TREE_EXAMPLES "Build tree module examples" ON)

if(BUILD_TREE_TESTS)
    # 启用测试
    enable_testing()

    if(Qt5_FOUND)
        # Qt测试源文件
        set(TEST_SOURCES
            tests/TreeTest.h
            tests/test_runner.cpp
        )

        # 创建Qt测试可执行文件
        add_executable(TreeModuleTests
            ${TEST_SOURCES}
        )

        # 链接库
        target_link_libraries(TreeModuleTests
            TreeModule
            Qt5::Core
            Qt5::Test
        )

        # 设置包含目录
        target_include_directories(TreeModuleTests PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_CURRENT_SOURCE_DIR}/tests
        )

        # 添加测试
        add_test(NAME TreeModuleUnitTests COMMAND TreeModuleTests)

        # 设置测试属性
        set_tests_properties(TreeModuleUnitTests PROPERTIES
            TIMEOUT 60
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        )
    endif()

    # 简单测试（不依赖Qt）
    add_executable(SimpleTreeTest
        tests/simple_test.cpp
    )

    # 链接库
    target_link_libraries(SimpleTreeTest
        TreeModule
    )

    # 设置包含目录
    target_include_directories(SimpleTreeTest PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # 添加简单测试
    add_test(NAME SimpleTreeTests COMMAND SimpleTreeTest)

    # 设置测试属性
    set_tests_properties(SimpleTreeTests PROPERTIES
        TIMEOUT 30
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    )

    # 扁平化树简单测试（不依赖外部库）
    add_executable(FlatTreeSimpleTest
        tests/flat_tree_simple_test.cpp
    )

    # 链接库
    target_link_libraries(FlatTreeSimpleTest
        TreeModule
        pthread
    )

    # 设置包含目录
    target_include_directories(FlatTreeSimpleTest PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # 添加扁平化树测试
    add_test(NAME FlatTreeSimpleTests COMMAND FlatTreeSimpleTest)

    # 设置测试属性
    set_tests_properties(FlatTreeSimpleTests PROPERTIES
        TIMEOUT 60
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    )

    # 性能对比测试
    add_executable(PerformanceComparison
        tests/performance_comparison.cpp
    )

    # 链接库
    target_link_libraries(PerformanceComparison
        TreeModule
        pthread
    )

    # 设置包含目录
    target_include_directories(PerformanceComparison PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # 添加性能对比测试（不加入CTest，因为运行时间较长）
    # add_test(NAME PerformanceComparisonTest COMMAND PerformanceComparison)

    # Google Test 和 Benchmark 测试
    if(GTEST_FOUND)
        # 扁平化树的Google Test测试
        add_executable(FlatTreeGTest
            tests/flat_tree_gtest.cpp
        )

        target_link_libraries(FlatTreeGTest
            TreeModule
            gtest
            gtest_main
            pthread
        )

        target_include_directories(FlatTreeGTest PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}
        )

        add_test(NAME FlatTreeGoogleTests COMMAND FlatTreeGTest)

        set_tests_properties(FlatTreeGoogleTests PROPERTIES
            TIMEOUT 60
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        )
    endif()

    if(BENCHMARK_FOUND)
        # 性能基准测试
        add_executable(TreeBenchmark
            tests/tree_benchmark.cpp
        )

        target_link_libraries(TreeBenchmark
            TreeModule
            benchmark
            benchmark_main
            pthread
        )

        target_include_directories(TreeBenchmark PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}
        )

        # 不添加到CTest，因为benchmark需要单独运行
    endif()
endif()

# 示例程序
if(BUILD_TREE_EXAMPLES)
    add_subdirectory(example)
endif()

# 安装设置（可选）
if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
    set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install" CACHE PATH "Install prefix" FORCE)
endif()

# 安装头文件
install(FILES ${TREE_HEADERS}
    DESTINATION include/common
    COMPONENT Development
)

# 安装CMake配置文件
install(TARGETS TreeModule
    EXPORT TreeModuleTargets
    INCLUDES DESTINATION include
)

install(EXPORT TreeModuleTargets
    FILE TreeModuleTargets.cmake
    NAMESPACE TreeModule::
    DESTINATION lib/cmake/TreeModule
)

# 创建配置文件
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/TreeModuleConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/TreeModuleConfig.cmake"
    INSTALL_DESTINATION lib/cmake/TreeModule
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/TreeModuleConfigVersion.cmake"
    VERSION 1.0.0
    COMPATIBILITY SameMajorVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/TreeModuleConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/TreeModuleConfigVersion.cmake"
    DESTINATION lib/cmake/TreeModule
)

# 打印配置信息
message(STATUS "TreeModule Configuration:")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Tests: ${BUILD_TREE_TESTS}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")

# 添加自定义目标用于代码格式化（可选）
find_program(CLANG_FORMAT_EXECUTABLE clang-format)
if(CLANG_FORMAT_EXECUTABLE)
    add_custom_target(format-tree
        COMMAND ${CLANG_FORMAT_EXECUTABLE} -i ${TREE_HEADERS}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Formatting tree module source files"
    )
endif()

# 添加文档生成目标（可选）
find_package(Doxygen QUIET)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
    
    if(EXISTS ${DOXYGEN_IN})
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(doc-tree
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()
