#pragma once

#include <map>
#include <set>
#include <vector>
#include <queue>
#include <stack>
#include <functional>
#include <optional>
#include <shared_mutex>
#include <atomic>
#include <thread>
#include <algorithm>
#include <mutex>

namespace common {

/**
 * @brief 有序扁平化树节点数据
 */
template<typename KeyType, typename DataType>
struct OrderedFlatTreeNodeData {
    KeyType key;
    DataType data;
    std::atomic<bool> valid{true};  ///< 软删除标记
    
    OrderedFlatTreeNodeData() = default;
    OrderedFlatTreeNodeData(const KeyType& k, const DataType& d) 
        : key(k), data(d) {}
    
    // 拷贝构造函数
    OrderedFlatTreeNodeData(const OrderedFlatTreeNodeData& other) 
        : key(other.key), data(other.data), valid(other.valid.load()) {}
    
    // 赋值操作符
    OrderedFlatTreeNodeData& operator=(const OrderedFlatTreeNodeData& other) {
        if (this != &other) {
            key = other.key;
            data = other.data;
            valid.store(other.valid.load());
        }
        return *this;
    }
};

/**
 * @brief 有序扁平化树关系管理
 */
template<typename KeyType>
class OrderedFlatTreeRelations {
public:
    using ChildrenMap = std::map<KeyType, std::vector<KeyType>>;
    using ParentMap = std::map<KeyType, KeyType>;
    
    /**
     * @brief 添加父子关系
     */
    void addRelation(const KeyType& parent, const KeyType& child) {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        children_[parent].push_back(child);
        parents_[child] = parent;
    }
    
    /**
     * @brief 移除节点的所有关系
     */
    void removeNode(const KeyType& key) {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        // 移除作为父节点的关系
        children_.erase(key);
        
        // 移除作为子节点的关系
        auto parentIt = parents_.find(key);
        if (parentIt != parents_.end()) {
            const KeyType& parent = parentIt->second;
            auto& siblings = children_[parent];
            siblings.erase(std::remove(siblings.begin(), siblings.end(), key), siblings.end());
            parents_.erase(parentIt);
        }
    }
    
    /**
     * @brief 获取子节点列表
     */
    std::vector<KeyType> getChildren(const KeyType& parent) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        auto it = children_.find(parent);
        return (it != children_.end()) ? it->second : std::vector<KeyType>{};
    }
    
    /**
     * @brief 获取父节点
     */
    std::optional<KeyType> getParent(const KeyType& child) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        auto it = parents_.find(child);
        return (it != parents_.end()) ? std::make_optional(it->second) : std::nullopt;
    }
    
    /**
     * @brief 获取所有根节点
     */
    std::set<KeyType> getRoots() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        std::set<KeyType> roots;
        
        for (const auto& [parent, children] : children_) {
            if (parents_.find(parent) == parents_.end()) {
                roots.insert(parent);
            }
        }
        
        return roots;
    }
    
    /**
     * @brief 检查是否有子节点
     */
    bool hasChildren(const KeyType& parent) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        auto it = children_.find(parent);
        return it != children_.end() && !it->second.empty();
    }
    
    /**
     * @brief 获取统计信息
     */
    struct Stats {
        size_t total_relations;
        size_t parent_child_pairs;
        size_t root_nodes;
    };
    
    Stats getStats() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        Stats stats;
        stats.total_relations = children_.size();
        stats.parent_child_pairs = parents_.size();
        stats.root_nodes = 0;
        
        for (const auto& [parent, children] : children_) {
            if (parents_.find(parent) == parents_.end()) {
                stats.root_nodes++;
            }
        }
        
        return stats;
    }

private:
    ChildrenMap children_;               ///< 父->子关系映射
    ParentMap parents_;                  ///< 子->父关系映射
    mutable std::shared_mutex mutex_;    ///< 读写锁
};

/**
 * @brief 基于std::map的有序扁平化树
 * 
 * 特点：
 * 1. 使用std::map保证键的有序性
 * 2. 支持范围查询和有序遍历
 * 3. 查找复杂度为O(log n)
 * 4. 线程安全的并发操作
 */
template<typename KeyType, typename DataType = void*>
class OrderedFlatTree {
public:
    using NodeData = OrderedFlatTreeNodeData<KeyType, DataType>;
    using NodesMap = std::map<KeyType, NodeData>;
    using Relations = OrderedFlatTreeRelations<KeyType>;
    
    /**
     * @brief 构造函数
     */
    OrderedFlatTree() = default;
    
    /**
     * @brief 析构函数
     */
    ~OrderedFlatTree() = default;
    
    /**
     * @brief 插入根节点
     */
    bool insertRoot(const KeyType& key, const DataType& data = DataType{}) {
        std::unique_lock<std::shared_mutex> lock(nodes_mutex_);
        
        if (nodes_.find(key) != nodes_.end()) {
            return false;  // 节点已存在
        }
        
        nodes_.emplace(key, NodeData(key, data));
        return true;
    }
    
    /**
     * @brief 插入子节点
     */
    bool insertChild(const KeyType& parentKey, const KeyType& key, 
                    const DataType& data = DataType{}) {
        std::unique_lock<std::shared_mutex> lock(nodes_mutex_);
        
        // 检查父节点是否存在且有效
        auto parentIt = nodes_.find(parentKey);
        if (parentIt == nodes_.end() || !parentIt->second.valid.load()) {
            return false;
        }
        
        // 检查子节点是否已存在
        if (nodes_.find(key) != nodes_.end()) {
            return false;
        }
        
        // 插入节点
        nodes_.emplace(key, NodeData(key, data));
        lock.unlock();
        
        // 添加关系
        relations_.addRelation(parentKey, key);
        
        return true;
    }
    
    /**
     * @brief 查找节点
     */
    std::optional<DataType> findNode(const KeyType& key) const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        auto it = nodes_.find(key);
        if (it != nodes_.end() && it->second.valid.load()) {
            return it->second.data;
        }
        
        return std::nullopt;
    }
    
    /**
     * @brief 检查节点是否存在
     */
    bool contains(const KeyType& key) const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        auto it = nodes_.find(key);
        return it != nodes_.end() && it->second.valid.load();
    }
    
    /**
     * @brief 软删除节点
     */
    bool removeNode(const KeyType& key) {
        std::unique_lock<std::shared_mutex> lock(nodes_mutex_);
        
        auto it = nodes_.find(key);
        if (it == nodes_.end() || !it->second.valid.load()) {
            return false;
        }
        
        it->second.valid.store(false);
        lock.unlock();
        
        relations_.removeNode(key);
        return true;
    }
    
    /**
     * @brief 获取树大小
     */
    size_t size() const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        size_t count = 0;
        for (const auto& [key, node] : nodes_) {
            if (node.valid.load()) {
                count++;
            }
        }
        
        return count;
    }
    
    /**
     * @brief 检查树是否为空
     */
    bool empty() const {
        return size() == 0;
    }
    
    /**
     * @brief BFS遍历
     */
    void traverseBFS(std::function<void(const KeyType&, const DataType&)> func) const {
        auto roots = relations_.getRoots();
        
        for (const auto& root : roots) {
            if (!contains(root)) continue;
            
            std::queue<KeyType> queue;
            queue.push(root);
            
            while (!queue.empty()) {
                KeyType current = queue.front();
                queue.pop();
                
                if (auto data = findNode(current)) {
                    func(current, *data);
                }
                
                auto children = relations_.getChildren(current);
                for (const auto& child : children) {
                    if (contains(child)) {
                        queue.push(child);
                    }
                }
            }
        }
    }
    
    /**
     * @brief DFS遍历
     */
    void traverseDFS(std::function<void(const KeyType&, const DataType&)> func) const {
        auto roots = relations_.getRoots();
        
        for (const auto& root : roots) {
            if (!contains(root)) continue;
            
            std::stack<KeyType> stack;
            stack.push(root);
            
            while (!stack.empty()) {
                KeyType current = stack.top();
                stack.pop();
                
                if (auto data = findNode(current)) {
                    func(current, *data);
                }
                
                auto children = relations_.getChildren(current);
                // 反向插入以保持顺序
                for (auto it = children.rbegin(); it != children.rend(); ++it) {
                    if (contains(*it)) {
                        stack.push(*it);
                    }
                }
            }
        }
    }
    
    /**
     * @brief 有序遍历（利用std::map的有序性）
     */
    void traverseOrdered(std::function<void(const KeyType&, const DataType&)> func) const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        for (const auto& [key, node] : nodes_) {
            if (node.valid.load()) {
                func(key, node.data);
            }
        }
    }
    
    /**
     * @brief 范围查询（std::map特有功能）
     */
    void rangeQuery(const KeyType& start, const KeyType& end,
                   std::function<void(const KeyType&, const DataType&)> func) const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        auto startIt = nodes_.lower_bound(start);
        auto endIt = nodes_.upper_bound(end);
        
        for (auto it = startIt; it != endIt; ++it) {
            if (it->second.valid.load()) {
                func(it->first, it->second.data);
            }
        }
    }
    
    /**
     * @brief BFS查找后代
     */
    std::vector<KeyType> getDescendantsBFS(const KeyType& parentKey) const {
        std::vector<KeyType> descendants;
        
        if (!contains(parentKey)) {
            return descendants;
        }
        
        std::queue<KeyType> queue;
        auto children = relations_.getChildren(parentKey);
        
        for (const auto& child : children) {
            if (contains(child)) {
                queue.push(child);
            }
        }
        
        while (!queue.empty()) {
            KeyType current = queue.front();
            queue.pop();
            descendants.push_back(current);
            
            auto currentChildren = relations_.getChildren(current);
            for (const auto& child : currentChildren) {
                if (contains(child)) {
                    queue.push(child);
                }
            }
        }
        
        return descendants;
    }
    
    /**
     * @brief DFS查找后代
     */
    std::vector<KeyType> getDescendantsDFS(const KeyType& parentKey) const {
        std::vector<KeyType> descendants;
        
        if (!contains(parentKey)) {
            return descendants;
        }
        
        std::stack<KeyType> stack;
        auto children = relations_.getChildren(parentKey);
        
        for (auto it = children.rbegin(); it != children.rend(); ++it) {
            if (contains(*it)) {
                stack.push(*it);
            }
        }
        
        while (!stack.empty()) {
            KeyType current = stack.top();
            stack.pop();
            descendants.push_back(current);
            
            auto currentChildren = relations_.getChildren(current);
            for (auto it = currentChildren.rbegin(); it != currentChildren.rend(); ++it) {
                if (contains(*it)) {
                    stack.push(*it);
                }
            }
        }
        
        return descendants;
    }
    
    /**
     * @brief 并行批量插入
     */
    size_t parallelBatchInsert(const std::vector<std::tuple<KeyType, KeyType, DataType>>& nodes) {
        std::atomic<size_t> success_count{0};
        
        const size_t num_threads = std::min(static_cast<size_t>(std::thread::hardware_concurrency()), nodes.size());
        if (num_threads <= 1) {
            for (const auto& node_info : nodes) {
                const auto& [parent_key, key, data] = node_info;
                if (insertChild(parent_key, key, data)) {
                    success_count.fetch_add(1);
                }
            }
        } else {
            std::vector<std::thread> threads;
            const size_t chunk_size = nodes.size() / num_threads;
            
            for (size_t t = 0; t < num_threads; ++t) {
                size_t start = t * chunk_size;
                size_t end = (t == num_threads - 1) ? nodes.size() : (t + 1) * chunk_size;
                
                threads.emplace_back([this, &nodes, &success_count, start, end]() {
                    for (size_t i = start; i < end; ++i) {
                        const auto& [parent_key, key, data] = nodes[i];
                        if (insertChild(parent_key, key, data)) {
                            success_count.fetch_add(1);
                        }
                    }
                });
            }
            
            for (auto& thread : threads) {
                thread.join();
            }
        }
        
        return success_count.load();
    }
    
    /**
     * @brief 获取统计信息
     */
    struct Stats {
        size_t total_nodes;
        size_t valid_nodes;
        size_t root_nodes;
        size_t leaf_nodes;
        size_t internal_nodes;
    };
    
    Stats getStats() const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        Stats stats{};
        stats.total_nodes = nodes_.size();
        
        for (const auto& [key, node] : nodes_) {
            if (node.valid.load()) {
                stats.valid_nodes++;
                
                bool isRoot = !relations_.getParent(key).has_value();
                bool hasChildren = relations_.hasChildren(key);
                
                if (isRoot) {
                    stats.root_nodes++;
                }
                
                if (!hasChildren) {
                    stats.leaf_nodes++;
                } else {
                    stats.internal_nodes++;
                }
            }
        }
        
        return stats;
    }
    
    /**
     * @brief 获取最大深度
     */
    size_t getMaxDepth() const {
        auto roots = relations_.getRoots();
        size_t maxDepth = 0;
        
        for (const auto& root : roots) {
            if (!contains(root)) continue;
            
            std::function<size_t(const KeyType&, size_t)> calculateDepth = 
                [&](const KeyType& node, size_t currentDepth) -> size_t {
                    size_t maxChildDepth = currentDepth;
                    auto children = relations_.getChildren(node);
                    
                    for (const auto& child : children) {
                        if (contains(child)) {
                            maxChildDepth = std::max(maxChildDepth, 
                                                   calculateDepth(child, currentDepth + 1));
                        }
                    }
                    
                    return maxChildDepth;
                };
            
            maxDepth = std::max(maxDepth, calculateDepth(root, 0));
        }
        
        return maxDepth;
    }

protected:
    NodesMap nodes_;                         ///< 节点数据存储（有序）
    Relations relations_;                    ///< 关系索引
    mutable std::shared_mutex nodes_mutex_;  ///< 节点数据的读写锁
};

} // namespace common
