#pragma once

#include "FlatTree.h"
#include <dispatch/dispatch.h>  // macOS Grand Central Dispatch
#include <os/lock.h>            // macOS低级锁
#include <mach/mach.h>          // macOS系统调用
#include <sys/sysctl.h>         // 系统信息

namespace common {

/**
 * @brief macOS优化版本的扁平化树
 * 利用macOS特有的API进行性能优化
 */
template<typename KeyType, typename DataType = void*>
class FlatTreeMacOS : public FlatTree<KeyType, DataType> {
public:
    using BaseType = FlatTree<KeyType, DataType>;
    using NodeData = typename BaseType::NodeData;
    
    /**
     * @brief 构造函数，初始化macOS特定的资源
     */
    FlatTreeMacOS() {
        // 创建GCD队列用于并行操作
        concurrent_queue_ = dispatch_queue_create("com.flattree.concurrent", 
                                                 DISPATCH_QUEUE_CONCURRENT);
        serial_queue_ = dispatch_queue_create("com.flattree.serial", 
                                            DISPATCH_QUEUE_SERIAL);
        
        // 初始化低级锁
        os_unfair_lock_init(&fast_lock_);
        
        // 获取系统信息
        size_t size = sizeof(cpu_count_);
        sysctlbyname("hw.ncpu", &cpu_count_, &size, NULL, 0);
        
        size = sizeof(cache_line_size_);
        sysctlbyname("hw.cachelinesize", &cache_line_size_, &size, NULL, 0);
    }
    
    /**
     * @brief 析构函数，清理macOS资源
     */
    ~FlatTreeMacOS() {
        if (concurrent_queue_) {
            dispatch_release(concurrent_queue_);
        }
        if (serial_queue_) {
            dispatch_release(serial_queue_);
        }
    }
    
    /**
     * @brief 使用GCD的并行批量插入
     */
    size_t gcdParallelBatchInsert(const std::vector<std::tuple<KeyType, KeyType, DataType>>& nodes) {
        __block std::atomic<size_t> success_count{0};
        
        dispatch_apply(nodes.size(), concurrent_queue_, ^(size_t index) {
            const auto& [parent_key, key, data] = nodes[index];
            if (this->insertChild(parent_key, key, data)) {
                success_count.fetch_add(1);
            }
        });
        
        return success_count.load();
    }
    
    /**
     * @brief 使用GCD的并行批量查找
     */
    std::unordered_map<KeyType, DataType> gcdParallelBatchFind(const std::vector<KeyType>& keys) const {
        __block std::unordered_map<KeyType, DataType> results;
        __block os_unfair_lock results_lock = OS_UNFAIR_LOCK_INIT;
        
        dispatch_apply(keys.size(), concurrent_queue_, ^(size_t index) {
            const KeyType& key = keys[index];
            if (auto data = this->findNode(key)) {
                os_unfair_lock_lock(&results_lock);
                results[key] = *data;
                os_unfair_lock_unlock(&results_lock);
            }
        });
        
        return results;
    }
    
    /**
     * @brief 使用GCD的并行遍历
     */
    void gcdParallelTraverse(std::function<void(const KeyType&, const DataType&)> func) const {
        // 获取所有根节点
        auto roots = this->relations_.getRoots();
        
        dispatch_apply(roots.size(), concurrent_queue_, ^(size_t index) {
            auto it = roots.begin();
            std::advance(it, index);
            const KeyType& root = *it;
            
            if (!this->contains(root)) return;
            
            // 每个线程处理一个根节点的子树
            std::queue<KeyType> queue;
            queue.push(root);
            
            while (!queue.empty()) {
                KeyType current = queue.front();
                queue.pop();
                
                if (auto data = this->findNode(current)) {
                    func(current, *data);
                }
                
                auto children = this->relations_.getChildren(current);
                for (const auto& child : children) {
                    if (this->contains(child)) {
                        queue.push(child);
                    }
                }
            }
        });
    }
    
    /**
     * @brief 快速插入（使用低级锁）
     */
    bool fastInsertChild(const KeyType& parentKey, const KeyType& key, 
                        const DataType& data = DataType{}) {
        os_unfair_lock_lock(&fast_lock_);
        
        // 快速路径检查
        if (this->nodes_.find(key) != this->nodes_.end()) {
            os_unfair_lock_unlock(&fast_lock_);
            return false;
        }
        
        auto parentIt = this->nodes_.find(parentKey);
        if (parentIt == this->nodes_.end() || !parentIt->second.valid.load()) {
            os_unfair_lock_unlock(&fast_lock_);
            return false;
        }
        
        // 插入节点
        this->nodes_.emplace(key, NodeData(key, data));
        
        os_unfair_lock_unlock(&fast_lock_);
        
        // 添加关系（这部分仍使用原有的线程安全机制）
        this->relations_.addRelation(parentKey, key);
        
        return true;
    }
    
    /**
     * @brief 内存预分配优化
     */
    void reserveCapacity(size_t expected_nodes) {
        std::unique_lock<std::shared_mutex> lock(this->nodes_mutex_);
        this->nodes_.reserve(expected_nodes);
    }
    
    /**
     * @brief 获取macOS特定的性能统计
     */
    struct MacOSStats {
        size_t cpu_count;
        size_t cache_line_size;
        size_t memory_usage_bytes;
        double cpu_usage_percent;
    };
    
    MacOSStats getMacOSStats() const {
        MacOSStats stats;
        stats.cpu_count = cpu_count_;
        stats.cache_line_size = cache_line_size_;
        
        // 估算内存使用
        stats.memory_usage_bytes = this->nodes_.size() * 
            (sizeof(KeyType) + sizeof(NodeData) + sizeof(void*) * 2);  // hash table overhead
        
        // 获取CPU使用率（简化版本）
        stats.cpu_usage_percent = 0.0;  // 实际实现需要更复杂的系统调用
        
        return stats;
    }
    
    /**
     * @brief 使用内存映射的大数据集优化
     */
    bool enableMemoryMapping(const std::string& backing_file) {
        // 这里可以实现内存映射优化
        // 对于非常大的数据集，可以使用mmap
        return false;  // 简化实现
    }
    
    /**
     * @brief 缓存友好的批量操作
     */
    template<typename Iterator>
    size_t cacheOptimizedBatchInsert(Iterator begin, Iterator end) {
        size_t success_count = 0;
        
        // 按缓存行大小分组处理
        const size_t batch_size = cache_line_size_ / sizeof(KeyType);
        
        auto current = begin;
        while (current != end) {
            auto batch_end = current;
            for (size_t i = 0; i < batch_size && batch_end != end; ++i, ++batch_end) {}
            
            // 处理当前批次
            for (auto it = current; it != batch_end; ++it) {
                const auto& [parent_key, key, data] = *it;
                if (this->insertChild(parent_key, key, data)) {
                    success_count++;
                }
            }
            
            current = batch_end;
        }
        
        return success_count;
    }
    
    /**
     * @brief 使用dispatch_group的异步操作
     */
    void asyncBatchOperation(const std::vector<std::tuple<KeyType, KeyType, DataType>>& nodes,
                           std::function<void(size_t)> completion_handler) {
        dispatch_group_t group = dispatch_group_create();
        __block std::atomic<size_t> success_count{0};
        
        const size_t chunk_size = nodes.size() / cpu_count_;
        
        for (size_t i = 0; i < cpu_count_; ++i) {
            size_t start = i * chunk_size;
            size_t end = (i == cpu_count_ - 1) ? nodes.size() : (i + 1) * chunk_size;
            
            dispatch_group_async(group, concurrent_queue_, ^{
                for (size_t j = start; j < end; ++j) {
                    const auto& [parent_key, key, data] = nodes[j];
                    if (this->insertChild(parent_key, key, data)) {
                        success_count.fetch_add(1);
                    }
                }
            });
        }
        
        dispatch_group_notify(group, dispatch_get_main_queue(), ^{
            completion_handler(success_count.load());
            dispatch_release(group);
        });
    }

private:
    dispatch_queue_t concurrent_queue_;  ///< GCD并发队列
    dispatch_queue_t serial_queue_;      ///< GCD串行队列
    os_unfair_lock_t fast_lock_;         ///< 低级锁
    size_t cpu_count_;                   ///< CPU核心数
    size_t cache_line_size_;             ///< 缓存行大小
};

} // namespace common
