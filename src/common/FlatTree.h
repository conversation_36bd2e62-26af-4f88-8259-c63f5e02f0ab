#pragma once

#include "FlatTreeNode.h"
#include <queue>
#include <stack>
#include <vector>
#include <functional>
#include <stdexcept>
#include <optional>
#include <thread>
#include <future>
#include <algorithm>
#include <mutex>

namespace common {

/**
 * @brief 扁平化树形结构模板类
 * @tparam KeyType 节点键的类型
 * @tparam DataType 节点数据的类型
 */
template<typename KeyType, typename DataType = void*>
class FlatTree {
public:
    using NodeData = FlatTreeNodeData<KeyType, DataType>;
    using NodesMap = std::unordered_map<KeyType, NodeData>;
    using Relations = FlatTreeRelations<KeyType>;

    /**
     * @brief 默认构造函数
     */
    FlatTree() = default;

    /**
     * @brief 析构函数
     */
    ~FlatTree() = default;

    /**
     * @brief 插入根节点
     * @param key 节点键
     * @param data 节点数据
     * @return 是否插入成功
     */
    bool insertRoot(const KeyType& key, const DataType& data = DataType{}) {
        std::unique_lock<std::shared_mutex> lock(nodes_mutex_);
        
        if (nodes_.find(key) != nodes_.end()) {
            return false;  // 节点已存在
        }
        
        nodes_.emplace(key, NodeData(key, data));
        lock.unlock();
        
        // 根节点没有父子关系，但需要标记为根节点
        relations_.addRelation(key, key);  // 临时添加自引用
        relations_.removeNodeRelations(key);  // 立即移除，但保持根节点状态
        
        return true;
    }

    /**
     * @brief 插入子节点
     * @param parentKey 父节点键
     * @param key 新节点键
     * @param data 新节点数据
     * @return 是否插入成功
     */
    bool insertChild(const KeyType& parentKey, const KeyType& key, 
                    const DataType& data = DataType{}) {
        std::unique_lock<std::shared_mutex> lock(nodes_mutex_);
        
        // 检查父节点是否存在
        auto parentIt = nodes_.find(parentKey);
        if (parentIt == nodes_.end() || !parentIt->second.valid.load()) {
            return false;
        }
        
        // 检查新节点是否已存在
        if (nodes_.find(key) != nodes_.end()) {
            return false;
        }
        
        nodes_.emplace(key, NodeData(key, data));
        lock.unlock();
        
        // 添加父子关系
        relations_.addRelation(parentKey, key);
        
        return true;
    }

    /**
     * @brief 删除节点及其所有后代
     * @param key 要删除的节点键
     * @return 是否删除成功
     */
    bool removeNode(const KeyType& key) {
        std::unique_lock<std::shared_mutex> lock(nodes_mutex_);
        
        auto it = nodes_.find(key);
        if (it == nodes_.end() || !it->second.valid.load()) {
            return false;
        }
        
        // 获取所有后代节点
        auto descendants = getDescendantsBFS(key);
        descendants.push_back(key);  // 包含自己
        
        // 标记所有节点为无效（软删除）
        for (const auto& nodeKey : descendants) {
            auto nodeIt = nodes_.find(nodeKey);
            if (nodeIt != nodes_.end()) {
                nodeIt->second.valid.store(false);
            }
        }
        
        lock.unlock();
        
        // 移除关系
        for (const auto& nodeKey : descendants) {
            relations_.removeNodeRelations(nodeKey);
        }
        
        return true;
    }

    /**
     * @brief 查找节点数据
     * @param key 节点键
     * @return 节点数据的可选值
     */
    std::optional<DataType> findNode(const KeyType& key) const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        auto it = nodes_.find(key);
        if (it != nodes_.end() && it->second.valid.load()) {
            return it->second.data;
        }
        return std::nullopt;
    }

    /**
     * @brief 检查节点是否存在
     * @param key 节点键
     * @return 是否存在
     */
    bool contains(const KeyType& key) const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        auto it = nodes_.find(key);
        return it != nodes_.end() && it->second.valid.load();
    }

    /**
     * @brief 获取树中有效节点总数
     * @return 节点总数
     */
    size_t size() const {
        std::shared_lock<std::shared_mutex> lock(nodes_mutex_);
        
        return std::count_if(nodes_.begin(), nodes_.end(),
            [](const auto& pair) { return pair.second.valid.load(); });
    }

    /**
     * @brief 检查树是否为空
     * @return 是否为空
     */
    bool empty() const {
        return size() == 0;
    }

    /**
     * @brief 清空整个树
     */
    void clear() {
        std::unique_lock<std::shared_mutex> lock(nodes_mutex_);
        nodes_.clear();
        lock.unlock();
        
        relations_.clear();
    }

    /**
     * @brief 非递归方式获取指定节点的所有后代节点（广度优先）
     * @param parentKey 父节点键
     * @return 所有后代节点的键列表
     */
    std::vector<KeyType> getDescendantsBFS(const KeyType& parentKey) const {
        std::vector<KeyType> descendants;
        
        if (!contains(parentKey)) {
            return descendants;
        }

        std::queue<KeyType> queue;
        queue.push(parentKey);

        while (!queue.empty()) {
            KeyType current = queue.front();
            queue.pop();

            auto children = relations_.getChildren(current);
            for (const auto& child : children) {
                if (contains(child)) {  // 只处理有效节点
                    descendants.push_back(child);
                    queue.push(child);
                }
            }
        }

        return descendants;
    }

    /**
     * @brief 非递归方式获取指定节点的所有后代节点（深度优先）
     * @param parentKey 父节点键
     * @return 所有后代节点的键列表
     */
    std::vector<KeyType> getDescendantsDFS(const KeyType& parentKey) const {
        std::vector<KeyType> descendants;
        
        if (!contains(parentKey)) {
            return descendants;
        }

        std::stack<KeyType> stack;
        auto children = relations_.getChildren(parentKey);
        
        // 以逆序压入栈中（保证正确的DFS顺序）
        for (auto it = children.rbegin(); it != children.rend(); ++it) {
            if (contains(*it)) {
                stack.push(*it);
            }
        }

        while (!stack.empty()) {
            KeyType current = stack.top();
            stack.pop();
            
            descendants.push_back(current);

            auto currentChildren = relations_.getChildren(current);
            for (auto it = currentChildren.rbegin(); it != currentChildren.rend(); ++it) {
                if (contains(*it)) {
                    stack.push(*it);
                }
            }
        }

        return descendants;
    }

    /**
     * @brief 广度优先遍历整个树
     * @param func 对每个节点执行的函数
     */
    void traverseBFS(std::function<void(const KeyType&, const DataType&)> func) const {
        auto roots = relations_.getRoots();
        
        for (const auto& root : roots) {
            if (!contains(root)) continue;
            
            std::queue<KeyType> queue;
            queue.push(root);
            
            while (!queue.empty()) {
                KeyType current = queue.front();
                queue.pop();
                
                if (auto data = findNode(current)) {
                    func(current, *data);
                }
                
                auto children = relations_.getChildren(current);
                for (const auto& child : children) {
                    if (contains(child)) {
                        queue.push(child);
                    }
                }
            }
        }
    }

    /**
     * @brief 深度优先遍历整个树
     * @param func 对每个节点执行的函数
     */
    void traverseDFS(std::function<void(const KeyType&, const DataType&)> func) const {
        auto roots = relations_.getRoots();
        
        for (const auto& root : roots) {
            if (!contains(root)) continue;
            
            std::stack<KeyType> stack;
            stack.push(root);
            
            while (!stack.empty()) {
                KeyType current = stack.top();
                stack.pop();
                
                if (auto data = findNode(current)) {
                    func(current, *data);
                }
                
                auto children = relations_.getChildren(current);
                for (auto it = children.rbegin(); it != children.rend(); ++it) {
                    if (contains(*it)) {
                        stack.push(*it);
                    }
                }
            }
        }
    }

    /**
     * @brief 并行广度优先遍历（多线程版本）
     * @param func 对每个节点执行的函数
     * @param num_threads 线程数量，默认为硬件并发数
     */
    void parallelTraverseBFS(std::function<void(const KeyType&, const DataType&)> func,
                            size_t num_threads = std::thread::hardware_concurrency()) const {
        auto roots = relations_.getRoots();
        if (roots.empty()) return;

        // 将根节点分配给不同线程
        std::vector<std::thread> threads;
        std::vector<KeyType> rootsVec(roots.begin(), roots.end());

        size_t roots_per_thread = std::max(size_t(1), rootsVec.size() / num_threads);

        for (size_t i = 0; i < num_threads && i * roots_per_thread < rootsVec.size(); ++i) {
            size_t start = i * roots_per_thread;
            size_t end = std::min((i + 1) * roots_per_thread, rootsVec.size());

            threads.emplace_back([this, func, &rootsVec, start, end]() {
                for (size_t j = start; j < end; ++j) {
                    const auto& root = rootsVec[j];
                    if (!contains(root)) continue;

                    std::queue<KeyType> queue;
                    queue.push(root);

                    while (!queue.empty()) {
                        KeyType current = queue.front();
                        queue.pop();

                        if (auto data = findNode(current)) {
                            func(current, *data);
                        }

                        auto children = relations_.getChildren(current);
                        for (const auto& child : children) {
                            if (contains(child)) {
                                queue.push(child);
                            }
                        }
                    }
                }
            });
        }

        // 等待所有线程完成
        for (auto& thread : threads) {
            thread.join();
        }
    }

    /**
     * @brief 并行批量插入节点
     * @param nodes 要插入的节点列表 (parent_key, key, data)
     * @return 成功插入的节点数量
     */
    size_t parallelBatchInsert(const std::vector<std::tuple<KeyType, KeyType, DataType>>& nodes) {
        std::atomic<size_t> success_count{0};

        // 使用多线程处理批量插入
        const size_t num_threads = std::min(static_cast<size_t>(std::thread::hardware_concurrency()), nodes.size());
        if (num_threads <= 1) {
            // 单线程处理
            for (const auto& node_info : nodes) {
                const auto& [parent_key, key, data] = node_info;
                if (insertChild(parent_key, key, data)) {
                    success_count.fetch_add(1);
                }
            }
        } else {
            // 多线程处理
            std::vector<std::thread> threads;
            const size_t chunk_size = nodes.size() / num_threads;

            for (size_t t = 0; t < num_threads; ++t) {
                size_t start = t * chunk_size;
                size_t end = (t == num_threads - 1) ? nodes.size() : (t + 1) * chunk_size;

                threads.emplace_back([this, &nodes, &success_count, start, end]() {
                    for (size_t i = start; i < end; ++i) {
                        const auto& [parent_key, key, data] = nodes[i];
                        if (insertChild(parent_key, key, data)) {
                            success_count.fetch_add(1);
                        }
                    }
                });
            }

            for (auto& thread : threads) {
                thread.join();
            }
        }

        return success_count.load();
    }

    /**
     * @brief 并行批量查找节点
     * @param keys 要查找的键列表
     * @return 找到的节点数据映射
     */
    std::unordered_map<KeyType, DataType> parallelBatchFind(const std::vector<KeyType>& keys) const {
        std::unordered_map<KeyType, DataType> results;
        std::mutex results_mutex;

        // 使用多线程处理批量查找
        const size_t num_threads = std::min(static_cast<size_t>(std::thread::hardware_concurrency()), keys.size());
        if (num_threads <= 1) {
            // 单线程处理
            for (const KeyType& key : keys) {
                if (auto data = findNode(key)) {
                    results[key] = *data;
                }
            }
        } else {
            // 多线程处理
            std::vector<std::thread> threads;
            const size_t chunk_size = keys.size() / num_threads;

            for (size_t t = 0; t < num_threads; ++t) {
                size_t start = t * chunk_size;
                size_t end = (t == num_threads - 1) ? keys.size() : (t + 1) * chunk_size;

                threads.emplace_back([this, &keys, &results, &results_mutex, start, end]() {
                    std::unordered_map<KeyType, DataType> local_results;

                    for (size_t i = start; i < end; ++i) {
                        if (auto data = findNode(keys[i])) {
                            local_results[keys[i]] = *data;
                        }
                    }

                    // 合并结果
                    std::lock_guard<std::mutex> lock(results_mutex);
                    results.insert(local_results.begin(), local_results.end());
                });
            }

            for (auto& thread : threads) {
                thread.join();
            }
        }

        return results;
    }

    /**
     * @brief 获取树的统计信息
     * @return 统计信息结构
     */
    typename Relations::Stats getStats() const {
        return relations_.getStats();
    }

    /**
     * @brief 获取节点的深度
     * @param key 节点键
     * @return 节点深度，如果节点不存在返回-1
     */
    int getDepth(const KeyType& key) const {
        if (!contains(key)) return -1;

        int depth = 0;
        auto current = key;

        while (auto parent = relations_.getParent(current)) {
            if (*parent == current) break;  // 避免循环
            current = *parent;
            depth++;
        }

        return depth;
    }

    /**
     * @brief 获取树的最大深度
     * @return 最大深度
     */
    int getMaxDepth() const {
        auto roots = relations_.getRoots();
        int max_depth = -1;

        for (const auto& root : roots) {
            if (!contains(root)) continue;

            std::queue<std::pair<KeyType, int>> queue;
            queue.push({root, 0});

            while (!queue.empty()) {
                auto [current, depth] = queue.front();
                queue.pop();

                max_depth = std::max(max_depth, depth);

                auto children = relations_.getChildren(current);
                for (const auto& child : children) {
                    if (contains(child)) {
                        queue.push({child, depth + 1});
                    }
                }
            }
        }

        return max_depth;
    }

    /**
     * @brief 压缩存储（移除已删除的节点）
     * @return 清理的节点数量
     */
    size_t compact() {
        std::unique_lock<std::shared_mutex> lock(nodes_mutex_);

        size_t removed_count = 0;
        auto it = nodes_.begin();
        while (it != nodes_.end()) {
            if (!it->second.valid.load()) {
                it = nodes_.erase(it);
                removed_count++;
            } else {
                ++it;
            }
        }

        return removed_count;
    }

private:
    mutable std::shared_mutex nodes_mutex_;  ///< 节点数据的读写锁
    NodesMap nodes_;                         ///< 节点数据存储
    Relations relations_;                    ///< 关系索引
};

} // namespace common
