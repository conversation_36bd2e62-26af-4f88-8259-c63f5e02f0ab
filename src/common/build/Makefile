# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/opt/homebrew/bin/ctest $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Development\" \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/opt/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/opt/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles /Users/<USER>/CLionProjects/BaseWidget/src/common/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named SimpleTreeTest

# Build rule for target.
SimpleTreeTest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 SimpleTreeTest
.PHONY : SimpleTreeTest

# fast build rule for target.
SimpleTreeTest/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleTreeTest.dir/build.make CMakeFiles/SimpleTreeTest.dir/build
.PHONY : SimpleTreeTest/fast

#=============================================================================
# Target rules for targets named FlatTreeSimpleTest

# Build rule for target.
FlatTreeSimpleTest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 FlatTreeSimpleTest
.PHONY : FlatTreeSimpleTest

# fast build rule for target.
FlatTreeSimpleTest/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FlatTreeSimpleTest.dir/build.make CMakeFiles/FlatTreeSimpleTest.dir/build
.PHONY : FlatTreeSimpleTest/fast

#=============================================================================
# Target rules for targets named PerformanceComparison

# Build rule for target.
PerformanceComparison: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 PerformanceComparison
.PHONY : PerformanceComparison

# fast build rule for target.
PerformanceComparison/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PerformanceComparison.dir/build.make CMakeFiles/PerformanceComparison.dir/build
.PHONY : PerformanceComparison/fast

#=============================================================================
# Target rules for targets named MacOSOptimizedTest

# Build rule for target.
MacOSOptimizedTest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 MacOSOptimizedTest
.PHONY : MacOSOptimizedTest

# fast build rule for target.
MacOSOptimizedTest/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOSOptimizedTest.dir/build.make CMakeFiles/MacOSOptimizedTest.dir/build
.PHONY : MacOSOptimizedTest/fast

#=============================================================================
# Target rules for targets named OrderedFlatTreeTest

# Build rule for target.
OrderedFlatTreeTest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 OrderedFlatTreeTest
.PHONY : OrderedFlatTreeTest

# fast build rule for target.
OrderedFlatTreeTest/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OrderedFlatTreeTest.dir/build.make CMakeFiles/OrderedFlatTreeTest.dir/build
.PHONY : OrderedFlatTreeTest/fast

#=============================================================================
# Target rules for targets named ThreeTreeComparison

# Build rule for target.
ThreeTreeComparison: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ThreeTreeComparison
.PHONY : ThreeTreeComparison

# fast build rule for target.
ThreeTreeComparison/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ThreeTreeComparison.dir/build.make CMakeFiles/ThreeTreeComparison.dir/build
.PHONY : ThreeTreeComparison/fast

#=============================================================================
# Target rules for targets named TreeExample

# Build rule for target.
TreeExample: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 TreeExample
.PHONY : TreeExample

# fast build rule for target.
TreeExample/fast:
	$(MAKE) $(MAKESILENT) -f example/CMakeFiles/TreeExample.dir/build.make example/CMakeFiles/TreeExample.dir/build
.PHONY : TreeExample/fast

tests/flat_tree_simple_test.o: tests/flat_tree_simple_test.cpp.o
.PHONY : tests/flat_tree_simple_test.o

# target to build an object file
tests/flat_tree_simple_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FlatTreeSimpleTest.dir/build.make CMakeFiles/FlatTreeSimpleTest.dir/tests/flat_tree_simple_test.cpp.o
.PHONY : tests/flat_tree_simple_test.cpp.o

tests/flat_tree_simple_test.i: tests/flat_tree_simple_test.cpp.i
.PHONY : tests/flat_tree_simple_test.i

# target to preprocess a source file
tests/flat_tree_simple_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FlatTreeSimpleTest.dir/build.make CMakeFiles/FlatTreeSimpleTest.dir/tests/flat_tree_simple_test.cpp.i
.PHONY : tests/flat_tree_simple_test.cpp.i

tests/flat_tree_simple_test.s: tests/flat_tree_simple_test.cpp.s
.PHONY : tests/flat_tree_simple_test.s

# target to generate assembly for a file
tests/flat_tree_simple_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FlatTreeSimpleTest.dir/build.make CMakeFiles/FlatTreeSimpleTest.dir/tests/flat_tree_simple_test.cpp.s
.PHONY : tests/flat_tree_simple_test.cpp.s

tests/macos_optimized_test.o: tests/macos_optimized_test.cpp.o
.PHONY : tests/macos_optimized_test.o

# target to build an object file
tests/macos_optimized_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOSOptimizedTest.dir/build.make CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o
.PHONY : tests/macos_optimized_test.cpp.o

tests/macos_optimized_test.i: tests/macos_optimized_test.cpp.i
.PHONY : tests/macos_optimized_test.i

# target to preprocess a source file
tests/macos_optimized_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOSOptimizedTest.dir/build.make CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.i
.PHONY : tests/macos_optimized_test.cpp.i

tests/macos_optimized_test.s: tests/macos_optimized_test.cpp.s
.PHONY : tests/macos_optimized_test.s

# target to generate assembly for a file
tests/macos_optimized_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOSOptimizedTest.dir/build.make CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.s
.PHONY : tests/macos_optimized_test.cpp.s

tests/ordered_flat_tree_test.o: tests/ordered_flat_tree_test.cpp.o
.PHONY : tests/ordered_flat_tree_test.o

# target to build an object file
tests/ordered_flat_tree_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OrderedFlatTreeTest.dir/build.make CMakeFiles/OrderedFlatTreeTest.dir/tests/ordered_flat_tree_test.cpp.o
.PHONY : tests/ordered_flat_tree_test.cpp.o

tests/ordered_flat_tree_test.i: tests/ordered_flat_tree_test.cpp.i
.PHONY : tests/ordered_flat_tree_test.i

# target to preprocess a source file
tests/ordered_flat_tree_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OrderedFlatTreeTest.dir/build.make CMakeFiles/OrderedFlatTreeTest.dir/tests/ordered_flat_tree_test.cpp.i
.PHONY : tests/ordered_flat_tree_test.cpp.i

tests/ordered_flat_tree_test.s: tests/ordered_flat_tree_test.cpp.s
.PHONY : tests/ordered_flat_tree_test.s

# target to generate assembly for a file
tests/ordered_flat_tree_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OrderedFlatTreeTest.dir/build.make CMakeFiles/OrderedFlatTreeTest.dir/tests/ordered_flat_tree_test.cpp.s
.PHONY : tests/ordered_flat_tree_test.cpp.s

tests/performance_comparison.o: tests/performance_comparison.cpp.o
.PHONY : tests/performance_comparison.o

# target to build an object file
tests/performance_comparison.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PerformanceComparison.dir/build.make CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o
.PHONY : tests/performance_comparison.cpp.o

tests/performance_comparison.i: tests/performance_comparison.cpp.i
.PHONY : tests/performance_comparison.i

# target to preprocess a source file
tests/performance_comparison.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PerformanceComparison.dir/build.make CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.i
.PHONY : tests/performance_comparison.cpp.i

tests/performance_comparison.s: tests/performance_comparison.cpp.s
.PHONY : tests/performance_comparison.s

# target to generate assembly for a file
tests/performance_comparison.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PerformanceComparison.dir/build.make CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.s
.PHONY : tests/performance_comparison.cpp.s

tests/simple_test.o: tests/simple_test.cpp.o
.PHONY : tests/simple_test.o

# target to build an object file
tests/simple_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleTreeTest.dir/build.make CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o
.PHONY : tests/simple_test.cpp.o

tests/simple_test.i: tests/simple_test.cpp.i
.PHONY : tests/simple_test.i

# target to preprocess a source file
tests/simple_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleTreeTest.dir/build.make CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.i
.PHONY : tests/simple_test.cpp.i

tests/simple_test.s: tests/simple_test.cpp.s
.PHONY : tests/simple_test.s

# target to generate assembly for a file
tests/simple_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleTreeTest.dir/build.make CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.s
.PHONY : tests/simple_test.cpp.s

tests/three_tree_comparison.o: tests/three_tree_comparison.cpp.o
.PHONY : tests/three_tree_comparison.o

# target to build an object file
tests/three_tree_comparison.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ThreeTreeComparison.dir/build.make CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o
.PHONY : tests/three_tree_comparison.cpp.o

tests/three_tree_comparison.i: tests/three_tree_comparison.cpp.i
.PHONY : tests/three_tree_comparison.i

# target to preprocess a source file
tests/three_tree_comparison.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ThreeTreeComparison.dir/build.make CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.i
.PHONY : tests/three_tree_comparison.cpp.i

tests/three_tree_comparison.s: tests/three_tree_comparison.cpp.s
.PHONY : tests/three_tree_comparison.s

# target to generate assembly for a file
tests/three_tree_comparison.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ThreeTreeComparison.dir/build.make CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.s
.PHONY : tests/three_tree_comparison.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... FlatTreeSimpleTest"
	@echo "... MacOSOptimizedTest"
	@echo "... OrderedFlatTreeTest"
	@echo "... PerformanceComparison"
	@echo "... SimpleTreeTest"
	@echo "... ThreeTreeComparison"
	@echo "... TreeExample"
	@echo "... tests/flat_tree_simple_test.o"
	@echo "... tests/flat_tree_simple_test.i"
	@echo "... tests/flat_tree_simple_test.s"
	@echo "... tests/macos_optimized_test.o"
	@echo "... tests/macos_optimized_test.i"
	@echo "... tests/macos_optimized_test.s"
	@echo "... tests/ordered_flat_tree_test.o"
	@echo "... tests/ordered_flat_tree_test.i"
	@echo "... tests/ordered_flat_tree_test.s"
	@echo "... tests/performance_comparison.o"
	@echo "... tests/performance_comparison.i"
	@echo "... tests/performance_comparison.s"
	@echo "... tests/simple_test.o"
	@echo "... tests/simple_test.i"
	@echo "... tests/simple_test.s"
	@echo "... tests/three_tree_comparison.o"
	@echo "... tests/three_tree_comparison.i"
	@echo "... tests/three_tree_comparison.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

