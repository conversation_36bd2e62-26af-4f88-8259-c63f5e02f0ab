Start testing: Sep 16 10:00 CST
----------------------------------------------------------
1/1 Testing: SimpleTreeTests
1/1 Test: SimpleTreeTests
Command: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/SimpleTreeTest"
Directory: /Users/<USER>/CLionProjects/BaseWidget/src/common/build
"SimpleTreeTests" start time: Sep 16 10:00 CST
Output:
----------------------------------------------------------
开始运行树形结构测试...
测试基本操作...
基本操作测试通过
测试遍历操作...
遍历操作测试通过
测试后代查找...
后代查找测试通过
测试迭代器...
迭代器测试通过
测试节点操作...
节点操作测试通过
所有测试通过！

=== 所有测试成功完成 ===
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"SimpleTreeTests" end time: Sep 16 10:00 CST
"SimpleTreeTests" time elapsed: 00:00:00
----------------------------------------------------------

End testing: Sep 16 10:00 CST
