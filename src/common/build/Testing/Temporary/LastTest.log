Start testing: Sep 16 10:45 CST
----------------------------------------------------------
1/3 Testing: SimpleTreeTests
1/3 Test: SimpleTreeTests
Command: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/SimpleTreeTest"
Directory: /Users/<USER>/CLionProjects/BaseWidget/src/common/build
"SimpleTreeTests" start time: Sep 16 10:45 CST
Output:
----------------------------------------------------------
开始运行树形结构测试...
测试基本操作...
基本操作测试通过
测试遍历操作...
遍历操作测试通过
测试后代查找...
后代查找测试通过
测试迭代器...
迭代器测试通过
测试节点操作...
节点操作测试通过
所有测试通过！

=== 所有测试成功完成 ===
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"SimpleTreeTests" end time: Sep 16 10:45 CST
"SimpleTreeTests" time elapsed: 00:00:00
----------------------------------------------------------

2/3 Testing: FlatTreeSimpleTests
2/3 Test: FlatTreeSimpleTests
Command: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/FlatTreeSimpleTest"
Directory: /Users/<USER>/CLionProjects/BaseWidget/src/common/build
"FlatTreeSimpleTests" start time: Sep 16 10:45 CST
Output:
----------------------------------------------------------
开始运行扁平化树测试...
测试基本操作...
基本操作测试通过
测试遍历操作...
遍历操作测试通过
测试后代查找...
后代查找测试通过
测试多线程安全...
多线程安全测试通过
测试并行操作...
并行操作测试通过
测试性能...
插入 10000 个节点耗时: 11ms
遍历 10000 个节点耗时: 5ms
查找根节点的所有后代耗时: 4ms
树统计信息:
  总节点数: 10000
  根节点数: 1
  叶子节点数: 5000
  内部节点数: 4999
  最大深度: 13
性能测试通过
所有扁平化树测试通过！

=== 所有扁平化树测试成功完成 ===
<end of output>
Test time =   0.04 sec
----------------------------------------------------------
Test Passed.
"FlatTreeSimpleTests" end time: Sep 16 10:45 CST
"FlatTreeSimpleTests" time elapsed: 00:00:00
----------------------------------------------------------

3/3 Testing: OrderedFlatTreeTests
3/3 Test: OrderedFlatTreeTests
Command: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/OrderedFlatTreeTest"
Directory: /Users/<USER>/CLionProjects/BaseWidget/src/common/build
"OrderedFlatTreeTests" start time: Sep 16 10:45 CST
Output:
----------------------------------------------------------
开始运行有序扁平化树测试...
测试基本操作...
基本操作测试通过
测试有序遍历...
有序遍历测试通过
测试范围查询...
范围查询测试通过
测试后代查找...
后代查找测试通过
测试多线程安全...
多线程安全测试通过
测试性能...
插入 10000 个节点耗时: 19ms
有序遍历 10000 个节点耗时: 0ms
范围查询 5001 个节点耗时: 0ms
查找根节点的所有后代耗时: 7ms
树统计信息:
  总节点数: 10000
  有效节点数: 10000
  根节点数: 1
  叶子节点数: 5000
  内部节点数: 5000
  最大深度: 13
性能测试通过
所有有序扁平化树测试通过！

=== 所有有序扁平化树测试成功完成 ===
<end of output>
Test time =   0.05 sec
----------------------------------------------------------
Test Passed.
"OrderedFlatTreeTests" end time: Sep 16 10:45 CST
"OrderedFlatTreeTests" time elapsed: 00:00:00
----------------------------------------------------------

End testing: Sep 16 10:45 CST
