Start testing: Sep 16 10:31 CST
----------------------------------------------------------
1/2 Testing: SimpleTreeTests
1/2 Test: SimpleTreeTests
Command: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/SimpleTreeTest"
Directory: /Users/<USER>/CLionProjects/BaseWidget/src/common/build
"SimpleTreeTests" start time: Sep 16 10:31 CST
Output:
----------------------------------------------------------
开始运行树形结构测试...
测试基本操作...
基本操作测试通过
测试遍历操作...
遍历操作测试通过
测试后代查找...
后代查找测试通过
测试迭代器...
迭代器测试通过
测试节点操作...
节点操作测试通过
所有测试通过！

=== 所有测试成功完成 ===
<end of output>
Test time =   0.35 sec
----------------------------------------------------------
Test Passed.
"SimpleTreeTests" end time: Sep 16 10:31 CST
"SimpleTreeTests" time elapsed: 00:00:00
----------------------------------------------------------

2/2 Testing: FlatTreeSimpleTests
2/2 Test: FlatTreeSimpleTests
Command: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/FlatTreeSimpleTest"
Directory: /Users/<USER>/CLionProjects/BaseWidget/src/common/build
"FlatTreeSimpleTests" start time: Sep 16 10:31 CST
Output:
----------------------------------------------------------
开始运行扁平化树测试...
测试基本操作...
基本操作测试通过
测试遍历操作...
遍历操作测试通过
测试后代查找...
后代查找测试通过
测试多线程安全...
多线程安全测试通过
测试并行操作...
并行操作测试通过
测试性能...
插入 10000 个节点耗时: 26ms
遍历 10000 个节点耗时: 5ms
查找根节点的所有后代耗时: 4ms
树统计信息:
  总节点数: 10000
  根节点数: 1
  叶子节点数: 5000
  内部节点数: 4999
  最大深度: 13
性能测试通过
所有扁平化树测试通过！

=== 所有扁平化树测试成功完成 ===
<end of output>
Test time =   0.05 sec
----------------------------------------------------------
Test Passed.
"FlatTreeSimpleTests" end time: Sep 16 10:31 CST
"FlatTreeSimpleTests" time elapsed: 00:00:00
----------------------------------------------------------

End testing: Sep 16 10:31 CST
