# CMake generated Testfile for 
# Source directory: /Users/<USER>/CLionProjects/BaseWidget/src/common
# Build directory: /Users/<USER>/CLionProjects/BaseWidget/src/common/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(SimpleTreeTests "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/SimpleTreeTest")
set_tests_properties(SimpleTreeTests PROPERTIES  TIMEOUT "30" WORKING_DIRECTORY "/Users/<USER>/CLionProjects/BaseWidget/src/common/build" _BACKTRACE_TRIPLES "/Users/<USER>/CLionProjects/BaseWidget/src/common/CMakeLists.txt;109;add_test;/Users/<USER>/CLionProjects/BaseWidget/src/common/CMakeLists.txt;0;")
subdirs("example")
