# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build

# Include any dependencies generated for this target.
include example/CMakeFiles/TreeExample.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include example/CMakeFiles/TreeExample.dir/compiler_depend.make

# Include the progress variables for this target.
include example/CMakeFiles/TreeExample.dir/progress.make

# Include the compile flags for this target's objects.
include example/CMakeFiles/TreeExample.dir/flags.make

example/CMakeFiles/TreeExample.dir/codegen:
.PHONY : example/CMakeFiles/TreeExample.dir/codegen

example/CMakeFiles/TreeExample.dir/tree_example.cpp.o: example/CMakeFiles/TreeExample.dir/flags.make
example/CMakeFiles/TreeExample.dir/tree_example.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/common/example/tree_example.cpp
example/CMakeFiles/TreeExample.dir/tree_example.cpp.o: example/CMakeFiles/TreeExample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object example/CMakeFiles/TreeExample.dir/tree_example.cpp.o"
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build/example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT example/CMakeFiles/TreeExample.dir/tree_example.cpp.o -MF CMakeFiles/TreeExample.dir/tree_example.cpp.o.d -o CMakeFiles/TreeExample.dir/tree_example.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/common/example/tree_example.cpp

example/CMakeFiles/TreeExample.dir/tree_example.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/TreeExample.dir/tree_example.cpp.i"
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build/example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/common/example/tree_example.cpp > CMakeFiles/TreeExample.dir/tree_example.cpp.i

example/CMakeFiles/TreeExample.dir/tree_example.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/TreeExample.dir/tree_example.cpp.s"
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build/example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/common/example/tree_example.cpp -o CMakeFiles/TreeExample.dir/tree_example.cpp.s

# Object files for target TreeExample
TreeExample_OBJECTS = \
"CMakeFiles/TreeExample.dir/tree_example.cpp.o"

# External object files for target TreeExample
TreeExample_EXTERNAL_OBJECTS =

example/TreeExample: example/CMakeFiles/TreeExample.dir/tree_example.cpp.o
example/TreeExample: example/CMakeFiles/TreeExample.dir/build.make
example/TreeExample: example/CMakeFiles/TreeExample.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable TreeExample"
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build/example && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/TreeExample.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
example/CMakeFiles/TreeExample.dir/build: example/TreeExample
.PHONY : example/CMakeFiles/TreeExample.dir/build

example/CMakeFiles/TreeExample.dir/clean:
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build/example && $(CMAKE_COMMAND) -P CMakeFiles/TreeExample.dir/cmake_clean.cmake
.PHONY : example/CMakeFiles/TreeExample.dir/clean

example/CMakeFiles/TreeExample.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common/example /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build/example /Users/<USER>/CLionProjects/BaseWidget/src/common/build/example/CMakeFiles/TreeExample.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : example/CMakeFiles/TreeExample.dir/depend

