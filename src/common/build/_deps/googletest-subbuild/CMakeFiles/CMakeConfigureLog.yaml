
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt:16 (project)"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/uname"
      - "/Users/<USER>/.bun/bin/uname"
      - "/Users/<USER>/.volta/bin/uname"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/uname"
      - "/Users/<USER>/Library/pnpm/uname"
      - "/Users/<USER>/.yarn/bin/uname"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/uname"
      - "/opt/homebrew/opt/bison/bin/uname"
      - "/Users/<USER>/go/bin/uname"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/uname"
      - "/opt/homebrew/anaconda3/bin/uname"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/uname"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/uname"
      - "/opt/homebrew/bin/uname"
      - "/opt/homebrew/sbin/uname"
      - "/usr/local/bin/uname"
      - "/System/Cryptexes/App/usr/bin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:16 (project)"
    message: |
      The system is: Darwin - 24.6.0 - arm64
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake:76 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake:32 (include)"
      - "CMakeLists.txt:16 (project)"
    mode: "program"
    variable: "CMAKE_INSTALL_NAME_TOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "install_name_tool"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/install_name_tool"
      - "/Users/<USER>/.bun/bin/install_name_tool"
      - "/Users/<USER>/.volta/bin/install_name_tool"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/install_name_tool"
      - "/Users/<USER>/Library/pnpm/install_name_tool"
      - "/Users/<USER>/.yarn/bin/install_name_tool"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/install_name_tool"
      - "/opt/homebrew/opt/bison/bin/install_name_tool"
      - "/Users/<USER>/go/bin/install_name_tool"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/install_name_tool"
      - "/opt/homebrew/anaconda3/bin/install_name_tool"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/install_name_tool"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/install_name_tool"
      - "/opt/homebrew/bin/install_name_tool"
      - "/opt/homebrew/sbin/install_name_tool"
      - "/usr/local/bin/install_name_tool"
      - "/System/Cryptexes/App/usr/bin/install_name_tool"
    found: "/usr/bin/install_name_tool"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
...
