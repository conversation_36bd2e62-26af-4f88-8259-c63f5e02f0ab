# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build/_deps/googletest-subbuild

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build/_deps/googletest-subbuild

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/googletest-populate.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/googletest-populate.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/googletest-populate.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/googletest-populate.dir

# All Build rule for target.
CMakeFiles/googletest-populate.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/googletest-populate.dir/build.make CMakeFiles/googletest-populate.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/googletest-populate.dir/build.make CMakeFiles/googletest-populate.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/_deps/googletest-subbuild/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9 "Built target googletest-populate"
.PHONY : CMakeFiles/googletest-populate.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/googletest-populate.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/_deps/googletest-subbuild/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/googletest-populate.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/_deps/googletest-subbuild/CMakeFiles 0
.PHONY : CMakeFiles/googletest-populate.dir/rule

# Convenience name for target.
googletest-populate: CMakeFiles/googletest-populate.dir/rule
.PHONY : googletest-populate

# codegen rule for target.
CMakeFiles/googletest-populate.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/googletest-populate.dir/build.make CMakeFiles/googletest-populate.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/_deps/googletest-subbuild/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9 "Finished codegen for target googletest-populate"
.PHONY : CMakeFiles/googletest-populate.dir/codegen

# clean rule for target.
CMakeFiles/googletest-populate.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/googletest-populate.dir/build.make CMakeFiles/googletest-populate.dir/clean
.PHONY : CMakeFiles/googletest-populate.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

