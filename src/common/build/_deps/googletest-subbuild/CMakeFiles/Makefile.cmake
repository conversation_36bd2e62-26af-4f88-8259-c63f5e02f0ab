# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "CMakeFiles/4.1.1/CMakeSystem.cmake"
  "CMakeLists.txt"
  "googletest-populate-prefix/tmp/googletest-populate-mkdirs.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/homebrew/share/cmake/Modules/ExternalProject.cmake"
  "/opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in"
  "/opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in"
  "/opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in"
  "/opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in"
  "/opt/homebrew/share/cmake/Modules/ExternalProject/gitclone.cmake.in"
  "/opt/homebrew/share/cmake/Modules/ExternalProject/gitupdate.cmake.in"
  "/opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in"
  "/opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "googletest-populate-prefix/tmp/googletest-populate-mkdirs.cmake"
  "googletest-populate-prefix/tmp/googletest-populate-gitclone.cmake"
  "googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-gitinfo.txt"
  "googletest-populate-prefix/tmp/googletest-populate-gitupdate.cmake"
  "googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-update-info.txt"
  "googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch-info.txt"
  "googletest-populate-prefix/tmp/googletest-populate-cfgcmd.txt"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/googletest-populate.dir/DependInfo.cmake"
  )
