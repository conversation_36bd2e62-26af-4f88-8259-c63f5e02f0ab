# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build

# Include any dependencies generated for this target.
include CMakeFiles/MacOSOptimizedTest.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/MacOSOptimizedTest.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/MacOSOptimizedTest.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MacOSOptimizedTest.dir/flags.make

CMakeFiles/MacOSOptimizedTest.dir/codegen:
.PHONY : CMakeFiles/MacOSOptimizedTest.dir/codegen

CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o: CMakeFiles/MacOSOptimizedTest.dir/flags.make
CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/macos_optimized_test.cpp
CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o: CMakeFiles/MacOSOptimizedTest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o -MF CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o.d -o CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/macos_optimized_test.cpp

CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/macos_optimized_test.cpp > CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.i

CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/macos_optimized_test.cpp -o CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.s

# Object files for target MacOSOptimizedTest
MacOSOptimizedTest_OBJECTS = \
"CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o"

# External object files for target MacOSOptimizedTest
MacOSOptimizedTest_EXTERNAL_OBJECTS =

MacOSOptimizedTest: CMakeFiles/MacOSOptimizedTest.dir/tests/macos_optimized_test.cpp.o
MacOSOptimizedTest: CMakeFiles/MacOSOptimizedTest.dir/build.make
MacOSOptimizedTest: CMakeFiles/MacOSOptimizedTest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable MacOSOptimizedTest"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/MacOSOptimizedTest.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/MacOSOptimizedTest.dir/build: MacOSOptimizedTest
.PHONY : CMakeFiles/MacOSOptimizedTest.dir/build

CMakeFiles/MacOSOptimizedTest.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MacOSOptimizedTest.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MacOSOptimizedTest.dir/clean

CMakeFiles/MacOSOptimizedTest.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/MacOSOptimizedTest.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/MacOSOptimizedTest.dir/depend

