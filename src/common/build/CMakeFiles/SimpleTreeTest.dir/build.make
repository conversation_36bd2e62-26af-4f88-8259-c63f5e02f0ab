# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build

# Include any dependencies generated for this target.
include CMakeFiles/SimpleTreeTest.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/SimpleTreeTest.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/SimpleTreeTest.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/SimpleTreeTest.dir/flags.make

CMakeFiles/SimpleTreeTest.dir/codegen:
.PHONY : CMakeFiles/SimpleTreeTest.dir/codegen

CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o: CMakeFiles/SimpleTreeTest.dir/flags.make
CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/simple_test.cpp
CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o: CMakeFiles/SimpleTreeTest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o -MF CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o.d -o CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/simple_test.cpp

CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/simple_test.cpp > CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.i

CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/simple_test.cpp -o CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.s

# Object files for target SimpleTreeTest
SimpleTreeTest_OBJECTS = \
"CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o"

# External object files for target SimpleTreeTest
SimpleTreeTest_EXTERNAL_OBJECTS =

SimpleTreeTest: CMakeFiles/SimpleTreeTest.dir/tests/simple_test.cpp.o
SimpleTreeTest: CMakeFiles/SimpleTreeTest.dir/build.make
SimpleTreeTest: CMakeFiles/SimpleTreeTest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable SimpleTreeTest"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/SimpleTreeTest.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/SimpleTreeTest.dir/build: SimpleTreeTest
.PHONY : CMakeFiles/SimpleTreeTest.dir/build

CMakeFiles/SimpleTreeTest.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/SimpleTreeTest.dir/cmake_clean.cmake
.PHONY : CMakeFiles/SimpleTreeTest.dir/clean

CMakeFiles/SimpleTreeTest.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/SimpleTreeTest.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/SimpleTreeTest.dir/depend

