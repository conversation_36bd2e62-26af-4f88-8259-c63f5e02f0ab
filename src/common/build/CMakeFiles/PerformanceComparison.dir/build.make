# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build

# Include any dependencies generated for this target.
include CMakeFiles/PerformanceComparison.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/PerformanceComparison.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/PerformanceComparison.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/PerformanceComparison.dir/flags.make

CMakeFiles/PerformanceComparison.dir/codegen:
.PHONY : CMakeFiles/PerformanceComparison.dir/codegen

CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o: CMakeFiles/PerformanceComparison.dir/flags.make
CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/performance_comparison.cpp
CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o: CMakeFiles/PerformanceComparison.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o -MF CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o.d -o CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/performance_comparison.cpp

CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/performance_comparison.cpp > CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.i

CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/performance_comparison.cpp -o CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.s

# Object files for target PerformanceComparison
PerformanceComparison_OBJECTS = \
"CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o"

# External object files for target PerformanceComparison
PerformanceComparison_EXTERNAL_OBJECTS =

PerformanceComparison: CMakeFiles/PerformanceComparison.dir/tests/performance_comparison.cpp.o
PerformanceComparison: CMakeFiles/PerformanceComparison.dir/build.make
PerformanceComparison: CMakeFiles/PerformanceComparison.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable PerformanceComparison"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/PerformanceComparison.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/PerformanceComparison.dir/build: PerformanceComparison
.PHONY : CMakeFiles/PerformanceComparison.dir/build

CMakeFiles/PerformanceComparison.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/PerformanceComparison.dir/cmake_clean.cmake
.PHONY : CMakeFiles/PerformanceComparison.dir/clean

CMakeFiles/PerformanceComparison.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/PerformanceComparison.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/PerformanceComparison.dir/depend

