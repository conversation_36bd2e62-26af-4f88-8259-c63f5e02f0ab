# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/Users/<USER>/CLionProjects/BaseWidget/src/common/CMakeLists.txt"
  "/Users/<USER>/CLionProjects/BaseWidget/src/common/TreeModuleConfig.cmake.in"
  "CMakeFiles/4.1.1/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.1/CMakeSystem.cmake"
  "/Users/<USER>/CLionProjects/BaseWidget/src/common/example/CMakeLists.txt"
  "/opt/homebrew/share/cmake/Modules/BasicConfigVersion-SameMajorVersion.cmake.in"
  "/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakePackageConfigHelpers.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake"
  "/opt/homebrew/share/cmake/Modules/FindDoxygen.cmake"
  "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"
  "/opt/homebrew/share/cmake/Modules/WriteBasicConfigVersionFile.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "TreeModuleConfig.cmake"
  "TreeModuleConfigVersion.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "example/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/SimpleTreeTest.dir/DependInfo.cmake"
  "example/CMakeFiles/TreeExample.dir/DependInfo.cmake"
  )
