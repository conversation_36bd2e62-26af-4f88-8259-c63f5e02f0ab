# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build

# Include any dependencies generated for this target.
include CMakeFiles/ThreeTreeComparison.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ThreeTreeComparison.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ThreeTreeComparison.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ThreeTreeComparison.dir/flags.make

CMakeFiles/ThreeTreeComparison.dir/codegen:
.PHONY : CMakeFiles/ThreeTreeComparison.dir/codegen

CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o: CMakeFiles/ThreeTreeComparison.dir/flags.make
CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/three_tree_comparison.cpp
CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o: CMakeFiles/ThreeTreeComparison.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o -MF CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o.d -o CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/three_tree_comparison.cpp

CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/three_tree_comparison.cpp > CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.i

CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/common/tests/three_tree_comparison.cpp -o CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.s

# Object files for target ThreeTreeComparison
ThreeTreeComparison_OBJECTS = \
"CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o"

# External object files for target ThreeTreeComparison
ThreeTreeComparison_EXTERNAL_OBJECTS =

ThreeTreeComparison: CMakeFiles/ThreeTreeComparison.dir/tests/three_tree_comparison.cpp.o
ThreeTreeComparison: CMakeFiles/ThreeTreeComparison.dir/build.make
ThreeTreeComparison: CMakeFiles/ThreeTreeComparison.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ThreeTreeComparison"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ThreeTreeComparison.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ThreeTreeComparison.dir/build: ThreeTreeComparison
.PHONY : CMakeFiles/ThreeTreeComparison.dir/build

CMakeFiles/ThreeTreeComparison.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ThreeTreeComparison.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ThreeTreeComparison.dir/clean

CMakeFiles/ThreeTreeComparison.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/src/common/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/ThreeTreeComparison.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/ThreeTreeComparison.dir/depend

