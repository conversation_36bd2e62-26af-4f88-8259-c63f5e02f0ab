
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/uname"
      - "/Users/<USER>/.bun/bin/uname"
      - "/Users/<USER>/.volta/bin/uname"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/uname"
      - "/Users/<USER>/Library/pnpm/uname"
      - "/Users/<USER>/.yarn/bin/uname"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/uname"
      - "/opt/homebrew/opt/bison/bin/uname"
      - "/Users/<USER>/go/bin/uname"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/uname"
      - "/opt/homebrew/anaconda3/bin/uname"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/uname"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/uname"
      - "/opt/homebrew/bin/uname"
      - "/opt/homebrew/sbin/uname"
      - "/usr/local/bin/uname"
      - "/System/Cryptexes/App/usr/bin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Darwin - 24.6.0 - arm64
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeUnixFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmake"
      - "make"
      - "smake"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/gmake"
      - "/Users/<USER>/.bun/bin/gmake"
      - "/Users/<USER>/.volta/bin/gmake"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/gmake"
      - "/Users/<USER>/Library/pnpm/gmake"
      - "/Users/<USER>/.yarn/bin/gmake"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/gmake"
      - "/opt/homebrew/opt/bison/bin/gmake"
      - "/Users/<USER>/go/bin/gmake"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/gmake"
      - "/opt/homebrew/anaconda3/bin/gmake"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/gmake"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/gmake"
    found: "/opt/homebrew/bin/gmake"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake:73 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:64 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER"
    description: "C compiler"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cc"
      - "gcc"
      - "cl"
      - "bcc"
      - "xlc"
      - "icx"
      - "clang"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/cc"
      - "/Users/<USER>/.bun/bin/cc"
      - "/Users/<USER>/.volta/bin/cc"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/cc"
      - "/Users/<USER>/Library/pnpm/cc"
      - "/Users/<USER>/.yarn/bin/cc"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/cc"
      - "/opt/homebrew/opt/bison/bin/cc"
      - "/Users/<USER>/go/bin/cc"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/cc"
      - "/opt/homebrew/anaconda3/bin/cc"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/cc"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/cc"
      - "/opt/homebrew/bin/cc"
      - "/opt/homebrew/sbin/cc"
      - "/usr/local/bin/cc"
      - "/System/Cryptexes/App/usr/bin/cc"
    found: "/usr/bin/cc"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "/opt/homebrew/share/cmake/Modules/"
    found: "/opt/homebrew/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/4.1.1/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting C compiler apple sysroot: "/usr/bin/cc" "-E" "apple-sdk.c"
        # 1 "apple-sdk.c"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 465 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.c" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 89 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 90 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/TargetConditionals.h" 1 3 4
        # 91 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 207 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 196 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 197 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 33 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 34 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 2 3 4
        # 198 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 1 3 4
        # 34 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 35 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 2 3 4
        # 199 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 208 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.c" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    found: "/usr/bin/ar"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    found: "/usr/bin/ranlib"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    found: "/usr/bin/strip"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    found: "/usr/bin/ld"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    found: "/usr/bin/nm"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    found: "/usr/bin/objdump"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/usr/bin/objcopy"
      - "/Users/<USER>/.codeium/windsurf/bin/objcopy"
      - "/Users/<USER>/.bun/bin/objcopy"
      - "/Users/<USER>/.volta/bin/objcopy"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/objcopy"
      - "/Users/<USER>/Library/pnpm/objcopy"
      - "/Users/<USER>/.yarn/bin/objcopy"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/objcopy"
      - "/opt/homebrew/opt/bison/bin/objcopy"
      - "/Users/<USER>/go/bin/objcopy"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/objcopy"
      - "/opt/homebrew/anaconda3/bin/objcopy"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/objcopy"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/objcopy"
      - "/opt/homebrew/bin/objcopy"
      - "/opt/homebrew/sbin/objcopy"
      - "/usr/local/bin/objcopy"
      - "/System/Cryptexes/App/usr/bin/objcopy"
      - "/bin/objcopy"
      - "/usr/sbin/objcopy"
      - "/sbin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/objcopy"
      - "/opt/X11/bin/objcopy"
      - "/Library/Apple/usr/bin/objcopy"
      - "/Library/TeX/texbin/objcopy"
      - "/Applications/VMware Fusion.app/Contents/Public/objcopy"
      - "/usr/local/share/dotnet/objcopy"
      - "/Users/<USER>/.dotnet/tools/objcopy"
      - "/usr/local/go/bin/objcopy"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/objcopy"
      - "/Users/<USER>/.cargo/bin/objcopy"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/objcopy"
      - "/Users/<USER>/devtools/flutter/bin/objcopy"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/objcopy"
      - "/Users/<USER>/.orbstack/bin/objcopy"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/usr/bin/readelf"
      - "/Users/<USER>/.codeium/windsurf/bin/readelf"
      - "/Users/<USER>/.bun/bin/readelf"
      - "/Users/<USER>/.volta/bin/readelf"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/readelf"
      - "/Users/<USER>/Library/pnpm/readelf"
      - "/Users/<USER>/.yarn/bin/readelf"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/readelf"
      - "/opt/homebrew/opt/bison/bin/readelf"
      - "/Users/<USER>/go/bin/readelf"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/readelf"
      - "/opt/homebrew/anaconda3/bin/readelf"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/readelf"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/readelf"
      - "/opt/homebrew/bin/readelf"
      - "/opt/homebrew/sbin/readelf"
      - "/usr/local/bin/readelf"
      - "/System/Cryptexes/App/usr/bin/readelf"
      - "/bin/readelf"
      - "/usr/sbin/readelf"
      - "/sbin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/readelf"
      - "/opt/X11/bin/readelf"
      - "/Library/Apple/usr/bin/readelf"
      - "/Library/TeX/texbin/readelf"
      - "/Applications/VMware Fusion.app/Contents/Public/readelf"
      - "/usr/local/share/dotnet/readelf"
      - "/Users/<USER>/.dotnet/tools/readelf"
      - "/usr/local/go/bin/readelf"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/readelf"
      - "/Users/<USER>/.cargo/bin/readelf"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/readelf"
      - "/Users/<USER>/devtools/flutter/bin/readelf"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/readelf"
      - "/Users/<USER>/.orbstack/bin/readelf"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/usr/bin/dlltool"
      - "/Users/<USER>/.codeium/windsurf/bin/dlltool"
      - "/Users/<USER>/.bun/bin/dlltool"
      - "/Users/<USER>/.volta/bin/dlltool"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/dlltool"
      - "/Users/<USER>/Library/pnpm/dlltool"
      - "/Users/<USER>/.yarn/bin/dlltool"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/dlltool"
      - "/opt/homebrew/opt/bison/bin/dlltool"
      - "/Users/<USER>/go/bin/dlltool"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/dlltool"
      - "/opt/homebrew/anaconda3/bin/dlltool"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/dlltool"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/dlltool"
      - "/opt/homebrew/bin/dlltool"
      - "/opt/homebrew/sbin/dlltool"
      - "/usr/local/bin/dlltool"
      - "/System/Cryptexes/App/usr/bin/dlltool"
      - "/bin/dlltool"
      - "/usr/sbin/dlltool"
      - "/sbin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/dlltool"
      - "/opt/X11/bin/dlltool"
      - "/Library/Apple/usr/bin/dlltool"
      - "/Library/TeX/texbin/dlltool"
      - "/Applications/VMware Fusion.app/Contents/Public/dlltool"
      - "/usr/local/share/dotnet/dlltool"
      - "/Users/<USER>/.dotnet/tools/dlltool"
      - "/usr/local/go/bin/dlltool"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/dlltool"
      - "/Users/<USER>/.cargo/bin/dlltool"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/dlltool"
      - "/Users/<USER>/devtools/flutter/bin/dlltool"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/dlltool"
      - "/Users/<USER>/.orbstack/bin/dlltool"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/usr/bin/addr2line"
      - "/Users/<USER>/.codeium/windsurf/bin/addr2line"
      - "/Users/<USER>/.bun/bin/addr2line"
      - "/Users/<USER>/.volta/bin/addr2line"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/addr2line"
      - "/Users/<USER>/Library/pnpm/addr2line"
      - "/Users/<USER>/.yarn/bin/addr2line"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/addr2line"
      - "/opt/homebrew/opt/bison/bin/addr2line"
      - "/Users/<USER>/go/bin/addr2line"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/addr2line"
      - "/opt/homebrew/anaconda3/bin/addr2line"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/addr2line"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/addr2line"
      - "/opt/homebrew/bin/addr2line"
      - "/opt/homebrew/sbin/addr2line"
      - "/usr/local/bin/addr2line"
      - "/System/Cryptexes/App/usr/bin/addr2line"
      - "/bin/addr2line"
      - "/usr/sbin/addr2line"
      - "/sbin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/addr2line"
      - "/opt/X11/bin/addr2line"
      - "/Library/Apple/usr/bin/addr2line"
      - "/Library/TeX/texbin/addr2line"
      - "/Applications/VMware Fusion.app/Contents/Public/addr2line"
      - "/usr/local/share/dotnet/addr2line"
      - "/Users/<USER>/.dotnet/tools/addr2line"
      - "/usr/local/go/bin/addr2line"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/addr2line"
      - "/Users/<USER>/.cargo/bin/addr2line"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/addr2line"
      - "/Users/<USER>/devtools/flutter/bin/addr2line"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/addr2line"
      - "/Users/<USER>/.orbstack/bin/addr2line"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/usr/bin/tapi"
      - "/Users/<USER>/.codeium/windsurf/bin/tapi"
      - "/Users/<USER>/.bun/bin/tapi"
      - "/Users/<USER>/.volta/bin/tapi"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/tapi"
      - "/Users/<USER>/Library/pnpm/tapi"
      - "/Users/<USER>/.yarn/bin/tapi"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/tapi"
      - "/opt/homebrew/opt/bison/bin/tapi"
      - "/Users/<USER>/go/bin/tapi"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/tapi"
      - "/opt/homebrew/anaconda3/bin/tapi"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/tapi"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/tapi"
      - "/opt/homebrew/bin/tapi"
      - "/opt/homebrew/sbin/tapi"
      - "/usr/local/bin/tapi"
      - "/System/Cryptexes/App/usr/bin/tapi"
      - "/bin/tapi"
      - "/usr/sbin/tapi"
      - "/sbin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/tapi"
      - "/opt/X11/bin/tapi"
      - "/Library/Apple/usr/bin/tapi"
      - "/Library/TeX/texbin/tapi"
      - "/Applications/VMware Fusion.app/Contents/Public/tapi"
      - "/usr/local/share/dotnet/tapi"
      - "/Users/<USER>/.dotnet/tools/tapi"
      - "/usr/local/go/bin/tapi"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/tapi"
      - "/Users/<USER>/.cargo/bin/tapi"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/tapi"
      - "/Users/<USER>/devtools/flutter/bin/tapi"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/tapi"
      - "/Users/<USER>/.orbstack/bin/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "g++"
      - "cl"
      - "bcc"
      - "icpx"
      - "icx"
      - "clang++"
    candidate_directories:
      - "/usr/bin/"
    found: "/usr/bin/c++"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "/opt/homebrew/share/cmake/Modules/"
    found: "/opt/homebrew/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is AppleClang, found in:
        /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/4.1.1/CompilerIdCXX/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting CXX compiler apple sysroot: "/usr/bin/c++" "-E" "apple-sdk.cpp"
        # 1 "apple-sdk.cpp"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 513 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.cpp" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 89 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 90 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/TargetConditionals.h" 1 3 4
        # 91 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 207 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 196 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 197 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 33 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 34 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 2 3 4
        # 198 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 1 3 4
        # 34 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 35 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 2 3 4
        # 199 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 208 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.cpp" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake:76 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake:32 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_INSTALL_NAME_TOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "install_name_tool"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/install_name_tool"
      - "/Users/<USER>/.bun/bin/install_name_tool"
      - "/Users/<USER>/.volta/bin/install_name_tool"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/install_name_tool"
      - "/Users/<USER>/Library/pnpm/install_name_tool"
      - "/Users/<USER>/.yarn/bin/install_name_tool"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/install_name_tool"
      - "/opt/homebrew/opt/bison/bin/install_name_tool"
      - "/Users/<USER>/go/bin/install_name_tool"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/install_name_tool"
      - "/opt/homebrew/anaconda3/bin/install_name_tool"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/install_name_tool"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/install_name_tool"
      - "/opt/homebrew/bin/install_name_tool"
      - "/opt/homebrew/sbin/install_name_tool"
      - "/usr/local/bin/install_name_tool"
      - "/System/Cryptexes/App/usr/bin/install_name_tool"
    found: "/usr/bin/install_name_tool"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR"
      binary: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR'
        
        Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /opt/homebrew/bin/gmake -f Makefile cmTC_5f839/fast
        /opt/homebrew/bin/gmake  -f CMakeFiles/cmTC_5f839.dir/build.make CMakeFiles/cmTC_5f839.dir/build
        gmake[1]: Entering directory '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR'
        Building C object CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -arch arm64   -v -Wl,-v -MD -MT CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.6.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/SubFrameworks"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_5f839
        /opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5f839.dir/link.txt --verbose=1
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_5f839 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1167.5
        BUILD 01:45:05 Apr 30 2025
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks
        /usr/bin/cc  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -o cmTC_5f839
        gmake[1]: Leaving directory '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /opt/homebrew/bin/gmake -f Makefile cmTC_5f839/fast]
        ignore line: [/opt/homebrew/bin/gmake  -f CMakeFiles/cmTC_5f839.dir/build.make CMakeFiles/cmTC_5f839.dir/build]
        ignore line: [gmake[1]: Entering directory '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR']
        ignore line: [Building C object CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -arch arm64   -v -Wl -v -MD -MT CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-iRMVAR -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.6.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/SubFrameworks"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_5f839]
        ignore line: [/opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5f839.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_5f839 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.5] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_5f839] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_5f839.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1167.5
      BUILD 01:45:05 Apr 30 2025
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS"
      binary: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS'
        
        Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /opt/homebrew/bin/gmake -f Makefile cmTC_f9082/fast
        /opt/homebrew/bin/gmake  -f CMakeFiles/cmTC_f9082.dir/build.make CMakeFiles/cmTC_f9082.dir/build
        gmake[1]: Entering directory '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS'
        Building CXX object CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -arch arm64   -v -Wl,-v -MD -MT CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang++: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1"
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.6.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/SubFrameworks"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking CXX executable cmTC_f9082
        /opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f9082.dir/link.txt --verbose=1
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_f9082 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1167.5
        BUILD 01:45:05 Apr 30 2025
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks
        /usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_f9082
        gmake[1]: Leaving directory '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /opt/homebrew/bin/gmake -f Makefile cmTC_f9082/fast]
        ignore line: [/opt/homebrew/bin/gmake  -f CMakeFiles/cmTC_f9082.dir/build.make CMakeFiles/cmTC_f9082.dir/build]
        ignore line: [gmake[1]: Entering directory '/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS']
        ignore line: [Building CXX object CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -arch arm64   -v -Wl -v -MD -MT CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang++: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1"]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/CMakeScratch/TryCompile-6XUMrS -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.6.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/SubFrameworks"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_f9082]
        ignore line: [/opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f9082.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_f9082 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.5] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_f9082] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_f9082.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        linker tool for 'CXX': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1167.5
      BUILD 01:45:05 Apr 30 2025
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
  -
    kind: "find_package-v1"
    backtrace:
      - "CMakeLists.txt:14 (find_package)"
    name: "Qt5"
    components:
      -
        name: "Core"
        required: true
        found: false
      -
        name: "Test"
        required: true
        found: false
    configs:
      -
        filename: "Qt5Config.cmake"
        kind: "cmake"
      -
        filename: "qt5-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: false
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "Qt5"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/pkgRedirects/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/pkgRedirects/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.codeium/windsurf/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.codeium/windsurf/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.bun/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.bun/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.volta/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.volta/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.sdkman/candidates/java/current/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.sdkman/candidates/java/current/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/pnpm/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/pnpm/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.yarn/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.yarn/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.config/yarn/global/node_modules/.bin/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.config/yarn/global/node_modules/.bin/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/opt/bison/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/opt/bison/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/go/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/go/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Cryptexes/App/usr/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Cryptexes/App/usr/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/X11/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/X11/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Apple/usr/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Apple/usr/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/TeX/texbin/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/TeX/texbin/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/VMware Fusion.app/Contents/Public/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/VMware Fusion.app/Contents/Public/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/share/dotnet/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/share/dotnet/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.dotnet/tools/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.dotnet/tools/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/Mono.framework/Versions/Current/Commands/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/Mono.framework/Versions/Current/Commands/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.cargo/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.cargo/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/devtools/flutter/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/devtools/flutter/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.orbstack/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.orbstack/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/X11R6/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/X11R6/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Library/Frameworks/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Library/Frameworks/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Applications/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Applications/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Applications/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Applications/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Applications/Qt5Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Applications/qt5-config.cmake"
        mode: "config"
        reason: "no_exist"
    found: null
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
...

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:174 (find_program)"
    mode: "program"
    variable: "CLANG_FORMAT_EXECUTABLE"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "clang-format"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/opt/homebrew/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
      - "/sw/bin/"
      - "/sw/sbin/"
      - "/sw/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/opt/local/"
      - "/Users/<USER>/Applications/"
      - "/Applications/"
      - "/Applications/Xcode.app/Contents/Applications/"
      - "/Applications/Xcode.app/Contents/Developer/Applications/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/clang-format"
      - "/Users/<USER>/.bun/bin/clang-format"
      - "/Users/<USER>/.volta/bin/clang-format"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/clang-format"
      - "/Users/<USER>/Library/pnpm/clang-format"
      - "/Users/<USER>/.yarn/bin/clang-format"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/clang-format"
      - "/opt/homebrew/opt/bison/bin/clang-format"
      - "/Users/<USER>/go/bin/clang-format"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/clang-format"
      - "/opt/homebrew/anaconda3/bin/clang-format"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/clang-format"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/clang-format"
      - "/opt/homebrew/bin/clang-format"
      - "/opt/homebrew/sbin/clang-format"
      - "/usr/local/bin/clang-format"
      - "/System/Cryptexes/App/usr/bin/clang-format"
      - "/usr/bin/clang-format"
      - "/bin/clang-format"
      - "/usr/sbin/clang-format"
      - "/sbin/clang-format"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/clang-format"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/clang-format"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/clang-format"
      - "/opt/X11/bin/clang-format"
      - "/Library/Apple/usr/bin/clang-format"
      - "/Library/TeX/texbin/clang-format"
      - "/Applications/VMware Fusion.app/Contents/Public/clang-format"
      - "/usr/local/share/dotnet/clang-format"
      - "/Users/<USER>/.dotnet/tools/clang-format"
      - "/usr/local/go/bin/clang-format"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/clang-format"
      - "/Users/<USER>/.cargo/bin/clang-format"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/clang-format"
      - "/Users/<USER>/devtools/flutter/bin/clang-format"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/clang-format"
      - "/Users/<USER>/.orbstack/bin/clang-format"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/clang-format"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/clang-format"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/clang-format"
      - "/opt/homebrew/bin/clang-format"
      - "/opt/homebrew/sbin/clang-format"
      - "/opt/homebrew/clang-format"
      - "/usr/local/bin/clang-format"
      - "/usr/local/sbin/clang-format"
      - "/usr/local/clang-format"
      - "/usr/bin/clang-format"
      - "/usr/sbin/clang-format"
      - "/usr/clang-format"
      - "/bin/clang-format"
      - "/sbin/clang-format"
      - "/usr/X11R6/bin/clang-format"
      - "/usr/X11R6/sbin/clang-format"
      - "/usr/X11R6/clang-format"
      - "/usr/pkg/bin/clang-format"
      - "/usr/pkg/sbin/clang-format"
      - "/usr/pkg/clang-format"
      - "/opt/bin/clang-format"
      - "/opt/sbin/clang-format"
      - "/opt/clang-format"
      - "/sw/bin/clang-format"
      - "/sw/sbin/clang-format"
      - "/sw/clang-format"
      - "/opt/local/bin/clang-format"
      - "/opt/local/sbin/clang-format"
      - "/opt/local/clang-format"
      - "/Users/<USER>/Applications/clang-format"
      - "/Applications/clang-format"
      - "/Applications/Xcode.app/Contents/Applications/clang-format"
      - "/Applications/Xcode.app/Contents/Developer/Applications/clang-format"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindDoxygen.cmake:753 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/FindDoxygen.cmake:937 (_Doxygen_find_doxygen)"
      - "CMakeLists.txt:184 (find_package)"
    mode: "program"
    variable: "DOXYGEN_EXECUTABLE"
    description: "Doxygen documentation generation tool (https://www.doxygen.nl)"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "doxygen"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/opt/homebrew/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
      - "/sw/bin/"
      - "/sw/sbin/"
      - "/sw/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/opt/local/"
      - "/Users/<USER>/Applications/"
      - "/Applications/"
      - "/Applications/Xcode.app/Contents/Applications/"
      - "/Applications/Xcode.app/Contents/Developer/Applications/"
      - "/Applications/Doxygen.app/Contents/Resources/"
      - "/Applications/Doxygen.app/Contents/MacOS/"
      - "/Applications/Utilities/Doxygen.app/Contents/Resources/"
      - "/Applications/Utilities/Doxygen.app/Contents/MacOS/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/doxygen"
      - "/Users/<USER>/.bun/bin/doxygen"
      - "/Users/<USER>/.volta/bin/doxygen"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/doxygen"
      - "/Users/<USER>/Library/pnpm/doxygen"
      - "/Users/<USER>/.yarn/bin/doxygen"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/doxygen"
      - "/opt/homebrew/opt/bison/bin/doxygen"
      - "/Users/<USER>/go/bin/doxygen"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/doxygen"
      - "/opt/homebrew/anaconda3/bin/doxygen"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/doxygen"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/doxygen"
      - "/opt/homebrew/bin/doxygen"
      - "/opt/homebrew/sbin/doxygen"
      - "/usr/local/bin/doxygen"
      - "/System/Cryptexes/App/usr/bin/doxygen"
      - "/usr/bin/doxygen"
      - "/bin/doxygen"
      - "/usr/sbin/doxygen"
      - "/sbin/doxygen"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/doxygen"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/doxygen"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/doxygen"
      - "/opt/X11/bin/doxygen"
      - "/Library/Apple/usr/bin/doxygen"
      - "/Library/TeX/texbin/doxygen"
      - "/Applications/VMware Fusion.app/Contents/Public/doxygen"
      - "/usr/local/share/dotnet/doxygen"
      - "/Users/<USER>/.dotnet/tools/doxygen"
      - "/usr/local/go/bin/doxygen"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/doxygen"
      - "/Users/<USER>/.cargo/bin/doxygen"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/doxygen"
      - "/Users/<USER>/devtools/flutter/bin/doxygen"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/doxygen"
      - "/Users/<USER>/.orbstack/bin/doxygen"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/doxygen"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/doxygen"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/doxygen"
      - "/opt/homebrew/bin/doxygen"
      - "/opt/homebrew/sbin/doxygen"
      - "/opt/homebrew/doxygen"
      - "/usr/local/bin/doxygen"
      - "/usr/local/sbin/doxygen"
      - "/usr/local/doxygen"
      - "/usr/bin/doxygen"
      - "/usr/sbin/doxygen"
      - "/usr/doxygen"
      - "/bin/doxygen"
      - "/sbin/doxygen"
      - "/usr/X11R6/bin/doxygen"
      - "/usr/X11R6/sbin/doxygen"
      - "/usr/X11R6/doxygen"
      - "/usr/pkg/bin/doxygen"
      - "/usr/pkg/sbin/doxygen"
      - "/usr/pkg/doxygen"
      - "/opt/bin/doxygen"
      - "/opt/sbin/doxygen"
      - "/opt/doxygen"
      - "/sw/bin/doxygen"
      - "/sw/sbin/doxygen"
      - "/sw/doxygen"
      - "/opt/local/bin/doxygen"
      - "/opt/local/sbin/doxygen"
      - "/opt/local/doxygen"
      - "/Users/<USER>/Applications/doxygen"
      - "/Applications/doxygen"
      - "/Applications/Xcode.app/Contents/Applications/doxygen"
      - "/Applications/Xcode.app/Contents/Developer/Applications/doxygen"
      - "/Applications/Doxygen.app/Contents/Resources/doxygen"
      - "/Applications/Doxygen.app/Contents/MacOS/doxygen"
      - "/Applications/Utilities/Doxygen.app/Contents/Resources/doxygen"
      - "/Applications/Utilities/Doxygen.app/Contents/MacOS/doxygen"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindDoxygen.cmake:840 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/FindDoxygen.cmake:941 (_Doxygen_find_dot)"
      - "CMakeLists.txt:184 (find_package)"
    mode: "program"
    variable: "DOXYGEN_DOT_EXECUTABLE"
    description: "Dot tool for use with Doxygen"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dot"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/opt/homebrew/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
      - "/sw/bin/"
      - "/sw/sbin/"
      - "/sw/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/opt/local/"
      - "/Users/<USER>/Applications/"
      - "/Applications/"
      - "/Applications/Xcode.app/Contents/Applications/"
      - "/Applications/Xcode.app/Contents/Developer/Applications/"
      - "/ATT/Graphviz/bin/"
      - "C:/Program Files/ATT/Graphviz/bin/"
      - "/Applications/Graphviz.app/Contents/MacOS/"
      - "/Applications/Utilities/Graphviz.app/Contents/MacOS/"
      - "/Applications/Doxygen.app/Contents/Resources/"
      - "/Applications/Doxygen.app/Contents/MacOS/"
      - "/Applications/Utilities/Doxygen.app/Contents/Resources/"
      - "/Applications/Utilities/Doxygen.app/Contents/MacOS/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/dot"
      - "/Users/<USER>/.bun/bin/dot"
      - "/Users/<USER>/.volta/bin/dot"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/dot"
      - "/Users/<USER>/Library/pnpm/dot"
      - "/Users/<USER>/.yarn/bin/dot"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/dot"
      - "/opt/homebrew/opt/bison/bin/dot"
      - "/Users/<USER>/go/bin/dot"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/dot"
      - "/opt/homebrew/anaconda3/bin/dot"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/dot"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/dot"
    found: "/opt/homebrew/bin/dot"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"
...

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindPkgConfig.cmake:69 (find_program)"
      - "CMakeLists.txt:25 (find_package)"
    mode: "program"
    variable: "PKG_CONFIG_EXECUTABLE"
    description: "pkg-config executable"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "pkg-config"
      - "pkgconf"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/opt/homebrew/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
      - "/sw/bin/"
      - "/sw/sbin/"
      - "/sw/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/opt/local/"
      - "/Users/<USER>/Applications/"
      - "/Applications/"
      - "/Applications/Xcode.app/Contents/Applications/"
      - "/Applications/Xcode.app/Contents/Developer/Applications/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/pkg-config"
      - "/Users/<USER>/.codeium/windsurf/bin/pkgconf"
      - "/Users/<USER>/.bun/bin/pkg-config"
      - "/Users/<USER>/.bun/bin/pkgconf"
      - "/Users/<USER>/.volta/bin/pkg-config"
      - "/Users/<USER>/.volta/bin/pkgconf"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/pkg-config"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/pkgconf"
      - "/Users/<USER>/Library/pnpm/pkg-config"
      - "/Users/<USER>/Library/pnpm/pkgconf"
      - "/Users/<USER>/.yarn/bin/pkg-config"
      - "/Users/<USER>/.yarn/bin/pkgconf"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/pkg-config"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/pkgconf"
      - "/opt/homebrew/opt/bison/bin/pkg-config"
      - "/opt/homebrew/opt/bison/bin/pkgconf"
      - "/Users/<USER>/go/bin/pkg-config"
      - "/Users/<USER>/go/bin/pkgconf"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/pkg-config"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/pkgconf"
      - "/opt/homebrew/anaconda3/bin/pkg-config"
      - "/opt/homebrew/anaconda3/bin/pkgconf"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/pkg-config"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/pkgconf"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/pkg-config"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/pkgconf"
    found: "/opt/homebrew/bin/pkg-config"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGit.cmake:86 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:1874 (find_package)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:1609 (__FetchContent_populateSubbuild)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:2145:EVAL:2 (__FetchContent_doPopulation)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:2145 (cmake_language)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:2384 (__FetchContent_Populate)"
      - "CMakeLists.txt:54 (FetchContent_MakeAvailable)"
    mode: "program"
    variable: "GIT_EXECUTABLE"
    description: "Git command line client"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/opt/homebrew/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
      - "/sw/bin/"
      - "/sw/sbin/"
      - "/sw/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/opt/local/"
      - "/Users/<USER>/Applications/"
      - "/Applications/"
      - "/Applications/Xcode.app/Contents/Applications/"
      - "/Applications/Xcode.app/Contents/Developer/Applications/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/git"
      - "/Users/<USER>/.bun/bin/git"
      - "/Users/<USER>/.volta/bin/git"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/git"
      - "/Users/<USER>/Library/pnpm/git"
      - "/Users/<USER>/.yarn/bin/git"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/git"
      - "/opt/homebrew/opt/bison/bin/git"
      - "/Users/<USER>/go/bin/git"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/git"
      - "/opt/homebrew/anaconda3/bin/git"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/git"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/git"
      - "/opt/homebrew/bin/git"
      - "/opt/homebrew/sbin/git"
      - "/usr/local/bin/git"
      - "/System/Cryptexes/App/usr/bin/git"
    found: "/usr/bin/git"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindPkgConfig.cmake:69 (find_program)"
      - "CMakeLists.txt:25 (find_package)"
    mode: "program"
    variable: "PKG_CONFIG_EXECUTABLE"
    description: "pkg-config executable"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "pkg-config"
      - "pkgconf"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/opt/homebrew/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
      - "/sw/bin/"
      - "/sw/sbin/"
      - "/sw/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/opt/local/"
      - "/Users/<USER>/Applications/"
      - "/Applications/"
      - "/Applications/Xcode.app/Contents/Applications/"
      - "/Applications/Xcode.app/Contents/Developer/Applications/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/pkg-config"
      - "/Users/<USER>/.codeium/windsurf/bin/pkgconf"
      - "/Users/<USER>/.bun/bin/pkg-config"
      - "/Users/<USER>/.bun/bin/pkgconf"
      - "/Users/<USER>/.volta/bin/pkg-config"
      - "/Users/<USER>/.volta/bin/pkgconf"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/pkg-config"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/pkgconf"
      - "/Users/<USER>/Library/pnpm/pkg-config"
      - "/Users/<USER>/Library/pnpm/pkgconf"
      - "/Users/<USER>/.yarn/bin/pkg-config"
      - "/Users/<USER>/.yarn/bin/pkgconf"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/pkg-config"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/pkgconf"
      - "/opt/homebrew/opt/bison/bin/pkg-config"
      - "/opt/homebrew/opt/bison/bin/pkgconf"
      - "/Users/<USER>/go/bin/pkg-config"
      - "/Users/<USER>/go/bin/pkgconf"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/pkg-config"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/pkgconf"
      - "/opt/homebrew/anaconda3/bin/pkg-config"
      - "/opt/homebrew/anaconda3/bin/pkgconf"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/pkg-config"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/pkgconf"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/pkg-config"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/pkgconf"
    found: "/opt/homebrew/bin/pkg-config"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGit.cmake:86 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:1874 (find_package)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:1609 (__FetchContent_populateSubbuild)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:2145:EVAL:2 (__FetchContent_doPopulation)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:2145 (cmake_language)"
      - "/opt/homebrew/share/cmake/Modules/FetchContent.cmake:2384 (__FetchContent_Populate)"
      - "CMakeLists.txt:54 (FetchContent_MakeAvailable)"
    mode: "program"
    variable: "GIT_EXECUTABLE"
    description: "Git command line client"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/opt/homebrew/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
      - "/sw/bin/"
      - "/sw/sbin/"
      - "/sw/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/opt/local/"
      - "/Users/<USER>/Applications/"
      - "/Applications/"
      - "/Applications/Xcode.app/Contents/Applications/"
      - "/Applications/Xcode.app/Contents/Developer/Applications/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/git"
      - "/Users/<USER>/.bun/bin/git"
      - "/Users/<USER>/.volta/bin/git"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/git"
      - "/Users/<USER>/Library/pnpm/git"
      - "/Users/<USER>/.yarn/bin/git"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/git"
      - "/opt/homebrew/opt/bison/bin/git"
      - "/Users/<USER>/go/bin/git"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/git"
      - "/opt/homebrew/anaconda3/bin/git"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/git"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/git"
      - "/opt/homebrew/bin/git"
      - "/opt/homebrew/sbin/git"
      - "/usr/local/bin/git"
      - "/System/Cryptexes/App/usr/bin/git"
    found: "/usr/bin/git"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindPkgConfig.cmake:69 (find_program)"
      - "CMakeLists.txt:25 (find_package)"
    mode: "program"
    variable: "PKG_CONFIG_EXECUTABLE"
    description: "pkg-config executable"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "pkg-config"
      - "pkgconf"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/sbin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/opt/homebrew/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
      - "/sw/bin/"
      - "/sw/sbin/"
      - "/sw/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/opt/local/"
      - "/Users/<USER>/Applications/"
      - "/Applications/"
      - "/Applications/Xcode.app/Contents/Applications/"
      - "/Applications/Xcode.app/Contents/Developer/Applications/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/pkg-config"
      - "/Users/<USER>/.codeium/windsurf/bin/pkgconf"
      - "/Users/<USER>/.bun/bin/pkg-config"
      - "/Users/<USER>/.bun/bin/pkgconf"
      - "/Users/<USER>/.volta/bin/pkg-config"
      - "/Users/<USER>/.volta/bin/pkgconf"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/pkg-config"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/pkgconf"
      - "/Users/<USER>/Library/pnpm/pkg-config"
      - "/Users/<USER>/Library/pnpm/pkgconf"
      - "/Users/<USER>/.yarn/bin/pkg-config"
      - "/Users/<USER>/.yarn/bin/pkgconf"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/pkg-config"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/pkgconf"
      - "/opt/homebrew/opt/bison/bin/pkg-config"
      - "/opt/homebrew/opt/bison/bin/pkgconf"
      - "/Users/<USER>/go/bin/pkg-config"
      - "/Users/<USER>/go/bin/pkgconf"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/pkg-config"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/pkgconf"
      - "/opt/homebrew/anaconda3/bin/pkg-config"
      - "/opt/homebrew/anaconda3/bin/pkgconf"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/pkg-config"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/pkgconf"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/pkg-config"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/pkgconf"
    found: "/opt/homebrew/bin/pkg-config"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:337 (find_package)"
      - "CMakeLists.txt:33 (find_package)"
    name: "GTest"
    configs:
      -
        filename: "GTestConfig.cmake"
        kind: "cmake"
      -
        filename: "gtest-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "optional"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "GTest"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/pkgRedirects/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/pkgRedirects/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.codeium/windsurf/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.codeium/windsurf/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.bun/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.bun/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.volta/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.volta/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.sdkman/candidates/java/current/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.sdkman/candidates/java/current/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/pnpm/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/pnpm/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.yarn/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.yarn/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.config/yarn/global/node_modules/.bin/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.config/yarn/global/node_modules/.bin/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/opt/bison/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/opt/bison/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/go/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/go/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Cryptexes/App/usr/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Cryptexes/App/usr/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/X11/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/X11/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Apple/usr/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Apple/usr/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/TeX/texbin/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/TeX/texbin/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/VMware Fusion.app/Contents/Public/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/VMware Fusion.app/Contents/Public/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/share/dotnet/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/share/dotnet/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.dotnet/tools/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.dotnet/tools/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/Mono.framework/Versions/Current/Commands/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/Mono.framework/Versions/Current/Commands/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.cargo/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.cargo/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/devtools/flutter/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/devtools/flutter/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.orbstack/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.orbstack/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/X11R6/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/X11R6/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Library/Frameworks/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Library/Frameworks/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Applications/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Applications/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Applications/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Applications/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Applications/GTestConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Applications/gtest-config.cmake"
        mode: "config"
        reason: "no_exist"
    found: null
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:384 (find_path)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "path"
    variable: "GTEST_INCLUDE_DIR"
    description: "Path to a file."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gtest/gtest.h"
    candidate_directories:
      - "/include/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/anaconda3/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/usr/local/go/bin/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/include/"
      - "/opt/homebrew/"
      - "/usr/local/include/"
      - "/usr/local/"
      - "/usr/include/"
      - "/usr/"
      - "/include/"
      - "/usr/X11R6/include/"
      - "/usr/X11R6/"
      - "/usr/pkg/include/"
      - "/usr/pkg/"
      - "/opt/include/"
      - "/opt/"
      - "/sw/include/"
      - "/sw/"
      - "/opt/local/include/"
      - "/opt/local/"
      - "/usr/include/X11/"
      - "/Users/<USER>/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/Network/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/include/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.codeium/windsurf/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.bun/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.volta/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/Library/pnpm/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.yarn/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/gtest.framework/Headers/gtest.h"
      - "/opt/homebrew/opt/bison/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/go/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/gtest.framework/Headers/gtest.h"
      - "/opt/homebrew/anaconda3/bin/gtest.framework/Headers/gtest.h"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/gtest.framework/Headers/gtest.h"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/gtest.framework/Headers/gtest.h"
      - "/opt/homebrew/bin/gtest.framework/Headers/gtest.h"
      - "/opt/homebrew/sbin/gtest.framework/Headers/gtest.h"
      - "/usr/local/bin/gtest.framework/Headers/gtest.h"
      - "/System/Cryptexes/App/usr/bin/gtest.framework/Headers/gtest.h"
      - "/usr/bin/gtest.framework/Headers/gtest.h"
      - "/bin/gtest.framework/Headers/gtest.h"
      - "/usr/sbin/gtest.framework/Headers/gtest.h"
      - "/sbin/gtest.framework/Headers/gtest.h"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/gtest.framework/Headers/gtest.h"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/gtest.framework/Headers/gtest.h"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/gtest.framework/Headers/gtest.h"
      - "/opt/X11/bin/gtest.framework/Headers/gtest.h"
      - "/Library/Apple/usr/bin/gtest.framework/Headers/gtest.h"
      - "/Library/TeX/texbin/gtest.framework/Headers/gtest.h"
      - "/Applications/VMware Fusion.app/Contents/Public/gtest.framework/Headers/gtest.h"
      - "/usr/local/share/dotnet/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.dotnet/tools/gtest.framework/Headers/gtest.h"
      - "/usr/local/go/bin/gtest.framework/Headers/gtest.h"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.cargo/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/devtools/flutter/bin/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/.orbstack/bin/gtest.framework/Headers/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/gtest.framework/Headers/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/gtest.framework/Headers/gtest.h"
      - "/opt/homebrew/include/gtest.framework/Headers/gtest.h"
      - "/opt/homebrew/gtest.framework/Headers/gtest.h"
      - "/usr/local/include/gtest.framework/Headers/gtest.h"
      - "/usr/local/gtest.framework/Headers/gtest.h"
      - "/usr/include/gtest.framework/Headers/gtest.h"
      - "/usr/gtest.framework/Headers/gtest.h"
      - "/include/gtest.framework/Headers/gtest.h"
      - "/usr/X11R6/include/gtest.framework/Headers/gtest.h"
      - "/usr/X11R6/gtest.framework/Headers/gtest.h"
      - "/usr/pkg/include/gtest.framework/Headers/gtest.h"
      - "/usr/pkg/gtest.framework/Headers/gtest.h"
      - "/opt/include/gtest.framework/Headers/gtest.h"
      - "/opt/gtest.framework/Headers/gtest.h"
      - "/sw/include/gtest.framework/Headers/gtest.h"
      - "/sw/gtest.framework/Headers/gtest.h"
      - "/opt/local/include/gtest.framework/Headers/gtest.h"
      - "/opt/local/gtest.framework/Headers/gtest.h"
      - "/usr/include/X11/gtest.framework/Headers/gtest.h"
      - "/Users/<USER>/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/Network/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/System/Library/Frameworks/gtest.framework/Headers/gtest.h"
      - "/include/gtest/gtest.h"
      - "/Users/<USER>/.codeium/windsurf/bin/gtest/gtest.h"
      - "/Users/<USER>/.bun/bin/gtest/gtest.h"
      - "/Users/<USER>/.volta/bin/gtest/gtest.h"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/gtest/gtest.h"
      - "/Users/<USER>/Library/pnpm/gtest/gtest.h"
      - "/Users/<USER>/.yarn/bin/gtest/gtest.h"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/gtest/gtest.h"
      - "/opt/homebrew/opt/bison/bin/gtest/gtest.h"
      - "/Users/<USER>/go/bin/gtest/gtest.h"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/gtest/gtest.h"
      - "/opt/homebrew/anaconda3/bin/gtest/gtest.h"
      - "/Library/Frameworks/Python.framework/Versions/3.10/bin/gtest/gtest.h"
      - "/Library/Frameworks/Python.framework/Versions/3.12/bin/gtest/gtest.h"
      - "/opt/homebrew/bin/gtest/gtest.h"
      - "/opt/homebrew/sbin/gtest/gtest.h"
      - "/usr/local/bin/gtest/gtest.h"
      - "/System/Cryptexes/App/usr/bin/gtest/gtest.h"
      - "/usr/bin/gtest/gtest.h"
      - "/bin/gtest/gtest.h"
      - "/usr/sbin/gtest/gtest.h"
      - "/sbin/gtest/gtest.h"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/gtest/gtest.h"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/gtest/gtest.h"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/gtest/gtest.h"
      - "/opt/X11/bin/gtest/gtest.h"
      - "/Library/Apple/usr/bin/gtest/gtest.h"
      - "/Library/TeX/texbin/gtest/gtest.h"
      - "/Applications/VMware Fusion.app/Contents/Public/gtest/gtest.h"
      - "/usr/local/share/dotnet/gtest/gtest.h"
      - "/Users/<USER>/.dotnet/tools/gtest/gtest.h"
      - "/usr/local/go/bin/gtest/gtest.h"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/gtest/gtest.h"
      - "/Users/<USER>/.cargo/bin/gtest/gtest.h"
      - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin/gtest/gtest.h"
      - "/Users/<USER>/devtools/flutter/bin/gtest/gtest.h"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/gtest/gtest.h"
      - "/Users/<USER>/.orbstack/bin/gtest/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/gtest/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/gtest/gtest.h"
      - "/opt/homebrew/include/gtest/gtest.h"
      - "/opt/homebrew/gtest/gtest.h"
      - "/usr/local/include/gtest/gtest.h"
      - "/usr/local/gtest/gtest.h"
      - "/usr/include/gtest/gtest.h"
      - "/usr/gtest/gtest.h"
      - "/include/gtest/gtest.h"
      - "/usr/X11R6/include/gtest/gtest.h"
      - "/usr/X11R6/gtest/gtest.h"
      - "/usr/pkg/include/gtest/gtest.h"
      - "/usr/pkg/gtest/gtest.h"
      - "/opt/include/gtest/gtest.h"
      - "/opt/gtest/gtest.h"
      - "/sw/include/gtest/gtest.h"
      - "/sw/gtest/gtest.h"
      - "/opt/local/include/gtest/gtest.h"
      - "/opt/local/gtest/gtest.h"
      - "/usr/include/X11/gtest/gtest.h"
      - "/Users/<USER>/Library/Frameworks/gtest/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks/gtest/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks/gtest/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/gtest/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/gtest/gtest.h"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/gtest/gtest.h"
      - "/Library/Frameworks/gtest/gtest.h"
      - "/Network/Library/Frameworks/gtest/gtest.h"
      - "/System/Library/Frameworks/gtest/gtest.h"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_INCLUDE_PATH:
        - "/usr/include/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:254 (find_library)"
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:403 (__gtest_find_library)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "library"
    variable: "GTEST_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gtest"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:254 (find_library)"
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:404 (__gtest_find_library)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "library"
    variable: "GTEST_LIBRARY_DEBUG"
    description: "Path to a library."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gtestd"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:254 (find_library)"
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:405 (__gtest_find_library)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "library"
    variable: "GTEST_MAIN_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gtest_main"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:254 (find_library)"
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:406 (__gtest_find_library)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "library"
    variable: "GTEST_MAIN_LIBRARY_DEBUG"
    description: "Path to a library."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gtest_maind"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:254 (find_library)"
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:407 (__gtest_find_library)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "library"
    variable: "GMOCK_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmock"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:254 (find_library)"
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:408 (__gtest_find_library)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "library"
    variable: "GMOCK_LIBRARY_DEBUG"
    description: "Path to a library."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmockd"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:254 (find_library)"
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:409 (__gtest_find_library)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "library"
    variable: "GMOCK_MAIN_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmock_main"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:254 (find_library)"
      - "/opt/homebrew/share/cmake/Modules/FindGTest.cmake:410 (__gtest_find_library)"
      - "CMakeLists.txt:33 (find_package)"
    mode: "library"
    variable: "GMOCK_MAIN_LIBRARY_DEBUG"
    description: "Path to a library."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmock_maind"
    candidate_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    searched_directories:
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.bun/bin/"
      - "/Users/<USER>/.volta/bin/"
      - "/Users/<USER>/.sdkman/candidates/java/current/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Users/<USER>/.yarn/bin/"
      - "/Users/<USER>/.config/yarn/global/node_modules/.bin/"
      - "/opt/homebrew/opt/bison/bin/"
      - "/Users/<USER>/go/bin/"
      - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/opt/X11/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Applications/VMware Fusion.app/Contents/Public/"
      - "/usr/local/share/dotnet/"
      - "/Users/<USER>/.dotnet/tools/"
      - "/Library/Frameworks/Mono.framework/Versions/Current/Commands/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/devtools/flutter/bin/"
      - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/lib/"
      - "/opt/homebrew/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/"
      - "/opt/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/"
      - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/"
      - "/Library/Frameworks/"
      - "/System/Library/Frameworks/"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
  -
    kind: "find_package-v1"
    backtrace:
      - "CMakeLists.txt:40 (find_package)"
    name: "benchmark"
    configs:
      -
        filename: "benchmarkConfig.cmake"
        kind: "cmake"
      -
        filename: "benchmark-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "optional"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "benchmark"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/pkgRedirects/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles/pkgRedirects/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.codeium/windsurf/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.codeium/windsurf/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.bun/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.bun/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.volta/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.volta/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.sdkman/candidates/java/current/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.sdkman/candidates/java/current/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/pnpm/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/pnpm/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.yarn/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.yarn/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.config/yarn/global/node_modules/.bin/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.config/yarn/global/node_modules/.bin/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/opt/bison/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/opt/bison/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/go/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/go/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/homebrew/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Cryptexes/App/usr/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Cryptexes/App/usr/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/X11/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/X11/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Apple/usr/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Apple/usr/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/TeX/texbin/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/TeX/texbin/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/VMware Fusion.app/Contents/Public/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/VMware Fusion.app/Contents/Public/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/share/dotnet/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/share/dotnet/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.dotnet/tools/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.dotnet/tools/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/Mono.framework/Versions/Current/Commands/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/Mono.framework/Versions/Current/Commands/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.cargo/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.cargo/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/devtools/flutter/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/devtools/flutter/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.orbstack/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/.orbstack/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/X11R6/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/X11R6/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Library/Frameworks/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Library/Frameworks/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/System/Library/Frameworks/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Applications/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Users/<USER>/Applications/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Applications/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Applications/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Applications/benchmarkConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/Applications/Xcode.app/Contents/Developer/Applications/benchmark-config.cmake"
        mode: "config"
        reason: "no_exist"
    found: null
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.bun/bin"
        - "/Users/<USER>/.volta/bin"
        - "/Users/<USER>/.sdkman/candidates/java/current/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.yarn/bin"
        - "/Users/<USER>/.config/yarn/global/node_modules/.bin"
        - "/opt/homebrew/opt/bison/bin"
        - "/Users/<USER>/go/bin"
        - "/Users/<USER>/.local/state/fnm_multishells/700_1757982578785/bin"
        - "/opt/homebrew/anaconda3/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.10/bin"
        - "/Library/Frameworks/Python.framework/Versions/3.12/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/opt/X11/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Applications/VMware Fusion.app/Contents/Public"
        - "/usr/local/share/dotnet"
        - "~/.dotnet/tools"
        - "/usr/local/go/bin"
        - "/Library/Frameworks/Mono.framework/Versions/Current/Commands"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/devtools/texlive/2021/bin/universal-darwin"
        - "/Users/<USER>/devtools/flutter/bin"
        - "/Users/<USER>/.dotnet/tools"
        - "/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts"
        - "/Users/<USER>/.orbstack/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_FRAMEWORK_PATH:
        - "~/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"
        - "/Applications/Xcode.app/Contents/Developer/Library/Frameworks"
        - "/Library/Frameworks"
        - "/Network/Library/Frameworks"
        - "/System/Library/Frameworks"
...
