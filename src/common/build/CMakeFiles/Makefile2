# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/src/common/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/SimpleTreeTest.dir/all
all: CMakeFiles/FlatTreeSimpleTest.dir/all
all: example/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/SimpleTreeTest.dir/codegen
codegen: CMakeFiles/FlatTreeSimpleTest.dir/codegen
codegen: example/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: example/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/SimpleTreeTest.dir/clean
clean: CMakeFiles/FlatTreeSimpleTest.dir/clean
clean: example/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory example

# Recursive "all" directory target.
example/all: example/CMakeFiles/TreeExample.dir/all
.PHONY : example/all

# Recursive "codegen" directory target.
example/codegen: example/CMakeFiles/TreeExample.dir/codegen
.PHONY : example/codegen

# Recursive "preinstall" directory target.
example/preinstall:
.PHONY : example/preinstall

# Recursive "clean" directory target.
example/clean: example/CMakeFiles/TreeExample.dir/clean
.PHONY : example/clean

#=============================================================================
# Target rules for target CMakeFiles/SimpleTreeTest.dir

# All Build rule for target.
CMakeFiles/SimpleTreeTest.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleTreeTest.dir/build.make CMakeFiles/SimpleTreeTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleTreeTest.dir/build.make CMakeFiles/SimpleTreeTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=3,4 "Built target SimpleTreeTest"
.PHONY : CMakeFiles/SimpleTreeTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SimpleTreeTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/SimpleTreeTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles 0
.PHONY : CMakeFiles/SimpleTreeTest.dir/rule

# Convenience name for target.
SimpleTreeTest: CMakeFiles/SimpleTreeTest.dir/rule
.PHONY : SimpleTreeTest

# codegen rule for target.
CMakeFiles/SimpleTreeTest.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleTreeTest.dir/build.make CMakeFiles/SimpleTreeTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=3,4 "Finished codegen for target SimpleTreeTest"
.PHONY : CMakeFiles/SimpleTreeTest.dir/codegen

# clean rule for target.
CMakeFiles/SimpleTreeTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SimpleTreeTest.dir/build.make CMakeFiles/SimpleTreeTest.dir/clean
.PHONY : CMakeFiles/SimpleTreeTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/FlatTreeSimpleTest.dir

# All Build rule for target.
CMakeFiles/FlatTreeSimpleTest.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FlatTreeSimpleTest.dir/build.make CMakeFiles/FlatTreeSimpleTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FlatTreeSimpleTest.dir/build.make CMakeFiles/FlatTreeSimpleTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=1,2 "Built target FlatTreeSimpleTest"
.PHONY : CMakeFiles/FlatTreeSimpleTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/FlatTreeSimpleTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/FlatTreeSimpleTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles 0
.PHONY : CMakeFiles/FlatTreeSimpleTest.dir/rule

# Convenience name for target.
FlatTreeSimpleTest: CMakeFiles/FlatTreeSimpleTest.dir/rule
.PHONY : FlatTreeSimpleTest

# codegen rule for target.
CMakeFiles/FlatTreeSimpleTest.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FlatTreeSimpleTest.dir/build.make CMakeFiles/FlatTreeSimpleTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=1,2 "Finished codegen for target FlatTreeSimpleTest"
.PHONY : CMakeFiles/FlatTreeSimpleTest.dir/codegen

# clean rule for target.
CMakeFiles/FlatTreeSimpleTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FlatTreeSimpleTest.dir/build.make CMakeFiles/FlatTreeSimpleTest.dir/clean
.PHONY : CMakeFiles/FlatTreeSimpleTest.dir/clean

#=============================================================================
# Target rules for target example/CMakeFiles/TreeExample.dir

# All Build rule for target.
example/CMakeFiles/TreeExample.dir/all:
	$(MAKE) $(MAKESILENT) -f example/CMakeFiles/TreeExample.dir/build.make example/CMakeFiles/TreeExample.dir/depend
	$(MAKE) $(MAKESILENT) -f example/CMakeFiles/TreeExample.dir/build.make example/CMakeFiles/TreeExample.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=5,6 "Built target TreeExample"
.PHONY : example/CMakeFiles/TreeExample.dir/all

# Build rule for subdir invocation for target.
example/CMakeFiles/TreeExample.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/CMakeFiles/TreeExample.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles 0
.PHONY : example/CMakeFiles/TreeExample.dir/rule

# Convenience name for target.
TreeExample: example/CMakeFiles/TreeExample.dir/rule
.PHONY : TreeExample

# codegen rule for target.
example/CMakeFiles/TreeExample.dir/codegen:
	$(MAKE) $(MAKESILENT) -f example/CMakeFiles/TreeExample.dir/build.make example/CMakeFiles/TreeExample.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/src/common/build/CMakeFiles --progress-num=5,6 "Finished codegen for target TreeExample"
.PHONY : example/CMakeFiles/TreeExample.dir/codegen

# clean rule for target.
example/CMakeFiles/TreeExample.dir/clean:
	$(MAKE) $(MAKESILENT) -f example/CMakeFiles/TreeExample.dir/build.make example/CMakeFiles/TreeExample.dir/clean
.PHONY : example/CMakeFiles/TreeExample.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

