# 三种树结构详细性能对比报告

## 测试环境
- CPU核心数: 8
- 编译优化: -O3 -DNDEBUG
- C++标准: C++17

## 详细测试结果

| 测试项目 | 规模 | 原始树(ms) | 扁平树hash(ms) | 扁平树ordered(ms) | 获胜者 | 备注 |
|---------|------|------------|----------------|-------------------|--------|------|
| 插入操作 | 100 | 0.29 | 0.49 | 0.49 | 原始树 |  |
| 随机查找 | 100 | 0.00 | 0.01 | 0.01 | 原始树 |  |
| 顺序查找 | 100 | 0.00 | 0.01 | 0.01 | 原始树 |  |
| BFS遍历 | 100 | 0.16 | 0.22 | 0.31 | 原始树 |  |
| 后代查找 | 100 | 0.08 | 0.14 | 0.20 | 原始树 |  |
| 范围查询 | 100 | N/A | N/A | 0.01 | 扁平树(ordered) | 仅OrderedFlatTree支持 |
| 插入操作 | 500 | 1.44 | 2.26 | 2.09 | 原始树 |  |
| 随机查找 | 500 | 0.01 | 0.02 | 0.03 | 原始树 |  |
| 顺序查找 | 500 | 0.01 | 0.02 | 0.03 | 原始树 |  |
| BFS遍历 | 500 | 0.32 | 0.61 | 1.07 | 原始树 |  |
| 后代查找 | 500 | 0.14 | 0.39 | 0.59 | 原始树 |  |
| 范围查询 | 500 | N/A | N/A | 0.03 | 扁平树(ordered) | 仅OrderedFlatTree支持 |
| 插入操作 | 1000 | 1.81 | 2.27 | 3.02 | 原始树 |  |
| 随机查找 | 1000 | 0.02 | 0.02 | 0.04 | 原始树 |  |
| 顺序查找 | 1000 | 0.02 | 0.02 | 0.04 | 原始树 |  |
| BFS遍历 | 1000 | 0.41 | 0.75 | 1.41 | 原始树 |  |
| 后代查找 | 1000 | 0.17 | 0.50 | 0.81 | 原始树 |  |
| 范围查询 | 1000 | N/A | N/A | 0.04 | 扁平树(ordered) | 仅OrderedFlatTree支持 |
| 插入操作 | 5000 | 5.44 | 6.71 | 10.67 | 原始树 |  |
| 随机查找 | 5000 | 0.11 | 0.10 | 0.15 | 扁平树(hash) |  |
| 顺序查找 | 5000 | 0.07 | 0.08 | 0.14 | 原始树 |  |
| BFS遍历 | 5000 | 1.49 | 2.82 | 5.50 | 原始树 |  |
| 后代查找 | 5000 | 0.68 | 1.99 | 3.51 | 原始树 |  |
| 范围查询 | 5000 | N/A | N/A | 0.15 | 扁平树(ordered) | 仅OrderedFlatTree支持 |
| 插入操作 | 10000 | 9.47 | 12.11 | 21.18 | 原始树 |  |
| 随机查找 | 10000 | 0.26 | 0.20 | 0.34 | 扁平树(hash) |  |
| 顺序查找 | 10000 | 0.23 | 0.18 | 0.29 | 扁平树(hash) |  |
| BFS遍历 | 10000 | 3.24 | 5.44 | 11.20 | 原始树 |  |
| 后代查找 | 10000 | 1.34 | 4.32 | 7.53 | 原始树 |  |
| 范围查询 | 10000 | N/A | N/A | 0.30 | 扁平树(ordered) | 仅OrderedFlatTree支持 |
| 插入操作 | 50000 | 46.59 | 58.76 | 118.05 | 原始树 |  |
| 随机查找 | 50000 | 1.20 | 1.04 | 2.12 | 扁平树(hash) |  |
| 顺序查找 | 50000 | 1.07 | 1.18 | 1.59 | 原始树 |  |
| BFS遍历 | 50000 | 15.07 | 27.52 | 61.01 | 原始树 |  |
| 后代查找 | 50000 | 6.68 | 20.89 | 38.62 | 原始树 |  |
| 范围查询 | 50000 | N/A | N/A | 1.59 | 扁平树(ordered) | 仅OrderedFlatTree支持 |
| 多线程并发 | 10000 | N/A | 41.99 | 53.81 | 扁平树(hash) | 原始树不支持多线程 |
| 内存使用 | 10000 | 0.00 | 0.00 | 0.00 | 扁平树(hash) |  |
