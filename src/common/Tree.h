#pragma once

#include "TreeNode.h"
#include "TreeIterator.h"
#include <unordered_map>
#include <queue>
#include <stack>
#include <vector>
#include <functional>
#include <stdexcept>

namespace common {

/**
 * @brief 树形结构模板类
 * @tparam KeyType 节点键的类型
 * @tparam DataType 节点数据的类型
 */
template<typename KeyType, typename DataType = void*>
class Tree {
public:
    using NodeType = TreeNode<KeyType, DataType>;
    using NodePtr = typename NodeType::NodePtr;
    using NodeMap = std::unordered_map<KeyType, NodePtr>;
    using BFSIterator = TreeBFSIterator<KeyType, DataType>;
    using DFSIterator = TreeDFSIterator<KeyType, DataType>;

    /**
     * @brief 默认构造函数
     */
    Tree() = default;

    /**
     * @brief 析构函数
     */
    ~Tree() = default;

    /**
     * @brief 插入根节点
     * @param key 节点键
     * @param data 节点数据
     * @return 插入的节点指针
     * @throws std::runtime_error 如果根节点已存在
     */
    NodePtr insertRoot(const KeyType& key, const DataType& data = DataType{}) {
        if (root_) {
            throw std::runtime_error("Root node already exists");
        }
        
        root_ = std::make_shared<NodeType>(key, data);
        nodes_[key] = root_;
        return root_;
    }

    /**
     * @brief 插入子节点
     * @param parentKey 父节点键
     * @param key 新节点键
     * @param data 新节点数据
     * @return 插入的节点指针
     * @throws std::runtime_error 如果父节点不存在或新节点键已存在
     */
    NodePtr insertChild(const KeyType& parentKey, const KeyType& key, 
                       const DataType& data = DataType{}) {
        auto parentIt = nodes_.find(parentKey);
        if (parentIt == nodes_.end()) {
            throw std::runtime_error("Parent node not found");
        }
        
        if (nodes_.find(key) != nodes_.end()) {
            throw std::runtime_error("Node with this key already exists");
        }
        
        auto newNode = std::make_shared<NodeType>(key, data);
        parentIt->second->addChild(newNode);
        nodes_[key] = newNode;
        
        return newNode;
    }

    /**
     * @brief 删除节点及其所有后代
     * @param key 要删除的节点键
     * @return 是否成功删除
     */
    bool removeNode(const KeyType& key) {
        auto it = nodes_.find(key);
        if (it == nodes_.end()) {
            return false;
        }
        
        NodePtr node = it->second;
        
        // 如果是根节点
        if (node == root_) {
            clear();
            return true;
        }
        
        // 递归删除所有后代节点
        removeNodeRecursive(node);
        
        // 从父节点中移除
        auto parent = node->getParent();
        if (parent) {
            parent->removeChild(key);
        }
        
        return true;
    }

    /**
     * @brief 查找节点
     * @param key 节点键
     * @return 找到的节点指针，如果未找到则返回nullptr
     */
    NodePtr findNode(const KeyType& key) const {
        auto it = nodes_.find(key);
        return (it != nodes_.end()) ? it->second : nullptr;
    }

    /**
     * @brief 获取根节点
     * @return 根节点指针
     */
    NodePtr getRoot() const { return root_; }

    /**
     * @brief 检查树是否为空
     * @return 如果树为空返回true，否则返回false
     */
    bool empty() const { return root_ == nullptr; }

    /**
     * @brief 获取树中节点总数
     * @return 节点总数
     */
    size_t size() const { return nodes_.size(); }

    /**
     * @brief 清空整个树
     */
    void clear() {
        nodes_.clear();
        root_ = nullptr;
    }

    /**
     * @brief 检查节点是否存在
     * @param key 节点键
     * @return 如果节点存在返回true，否则返回false
     */
    bool contains(const KeyType& key) const {
        return nodes_.find(key) != nodes_.end();
    }

    /**
     * @brief 获取节点的深度（从根节点开始计算，根节点深度为0）
     * @param key 节点键
     * @return 节点深度，如果节点不存在返回-1
     */
    int getDepth(const KeyType& key) const {
        auto node = findNode(key);
        if (!node) return -1;
        
        int depth = 0;
        auto current = node;
        while (current->getParent()) {
            current = current->getParent();
            depth++;
        }
        return depth;
    }

    /**
     * @brief 获取树的最大深度
     * @return 树的最大深度
     */
    int getMaxDepth() const {
        if (!root_) return -1;
        return getMaxDepthRecursive(root_);
    }

    /**
     * @brief 非递归方式获取指定节点的所有后代节点（广度优先）
     * @param parentKey 父节点键
     * @return 所有后代节点的键列表
     */
    std::vector<KeyType> getDescendantsBFS(const KeyType& parentKey) const {
        std::vector<KeyType> descendants;
        auto parentNode = findNode(parentKey);
        if (!parentNode) {
            return descendants;
        }

        std::queue<NodePtr> queue;
        queue.push(parentNode);

        while (!queue.empty()) {
            NodePtr current = queue.front();
            queue.pop();

            // 添加所有子节点到结果和队列中
            for (const auto& child : current->getChildren()) {
                descendants.push_back(child->getKey());
                queue.push(child);
            }
        }

        return descendants;
    }

    /**
     * @brief 非递归方式获取指定节点的所有后代节点（深度优先）
     * @param parentKey 父节点键
     * @return 所有后代节点的键列表
     */
    std::vector<KeyType> getDescendantsDFS(const KeyType& parentKey) const {
        std::vector<KeyType> descendants;
        auto parentNode = findNode(parentKey);
        if (!parentNode) {
            return descendants;
        }

        std::stack<NodePtr> stack;
        // 将父节点的子节点以逆序压入栈中
        const auto& children = parentNode->getChildren();
        for (auto it = children.rbegin(); it != children.rend(); ++it) {
            stack.push(*it);
        }

        while (!stack.empty()) {
            NodePtr current = stack.top();
            stack.pop();

            // 添加当前节点到结果中
            descendants.push_back(current->getKey());

            // 将当前节点的子节点以逆序压入栈中（保证正确的DFS顺序）
            const auto& currentChildren = current->getChildren();
            for (auto it = currentChildren.rbegin(); it != currentChildren.rend(); ++it) {
                stack.push(*it);
            }
        }

        return descendants;
    }

    /**
     * @brief 非递归方式获取指定节点的所有后代节点指针（广度优先）
     * @param parentKey 父节点键
     * @return 所有后代节点的指针列表
     */
    std::vector<NodePtr> getDescendantNodesBFS(const KeyType& parentKey) const {
        std::vector<NodePtr> descendants;
        auto parentNode = findNode(parentKey);
        if (!parentNode) {
            return descendants;
        }

        std::queue<NodePtr> queue;
        queue.push(parentNode);

        while (!queue.empty()) {
            NodePtr current = queue.front();
            queue.pop();

            // 添加所有子节点到结果和队列中
            for (const auto& child : current->getChildren()) {
                descendants.push_back(child);
                queue.push(child);
            }
        }

        return descendants;
    }

    /**
     * @brief 非递归方式获取指定节点的所有后代节点指针（深度优先）
     * @param parentKey 父节点键
     * @return 所有后代节点的指针列表
     */
    std::vector<NodePtr> getDescendantNodesDFS(const KeyType& parentKey) const {
        std::vector<NodePtr> descendants;
        auto parentNode = findNode(parentKey);
        if (!parentNode) {
            return descendants;
        }

        std::stack<NodePtr> stack;
        // 将父节点的子节点以逆序压入栈中
        const auto& children = parentNode->getChildren();
        for (auto it = children.rbegin(); it != children.rend(); ++it) {
            stack.push(*it);
        }

        while (!stack.empty()) {
            NodePtr current = stack.top();
            stack.pop();

            // 添加当前节点到结果中
            descendants.push_back(current);

            // 将当前节点的子节点以逆序压入栈中（保证正确的DFS顺序）
            const auto& currentChildren = current->getChildren();
            for (auto it = currentChildren.rbegin(); it != currentChildren.rend(); ++it) {
                stack.push(*it);
            }
        }

        return descendants;
    }

    /**
     * @brief 获取广度优先遍历的开始迭代器
     * @return BFS开始迭代器
     */
    BFSIterator beginBFS() const {
        return BFSIterator(root_);
    }

    /**
     * @brief 获取广度优先遍历的结束迭代器
     * @return BFS结束迭代器
     */
    BFSIterator endBFS() const {
        return BFSIterator();
    }

    /**
     * @brief 获取深度优先遍历的开始迭代器
     * @return DFS开始迭代器
     */
    DFSIterator beginDFS() const {
        return DFSIterator(root_);
    }

    /**
     * @brief 获取深度优先遍历的结束迭代器
     * @return DFS结束迭代器
     */
    DFSIterator endDFS() const {
        return DFSIterator();
    }

    /**
     * @brief 遍历整个树并对每个节点执行指定操作（广度优先）
     * @param func 要执行的函数，接受NodePtr参数
     */
    void traverseBFS(std::function<void(const NodePtr&)> func) const {
        for (auto it = beginBFS(); it != endBFS(); ++it) {
            func(*it);
        }
    }

    /**
     * @brief 遍历整个树并对每个节点执行指定操作（深度优先）
     * @param func 要执行的函数，接受NodePtr参数
     */
    void traverseDFS(std::function<void(const NodePtr&)> func) const {
        for (auto it = beginDFS(); it != endDFS(); ++it) {
            func(*it);
        }
    }

private:
    NodePtr root_;          ///< 根节点
    NodeMap nodes_;         ///< 节点映射表，用于快速查找

    /**
     * @brief 递归删除节点及其所有后代
     * @param node 要删除的节点
     */
    void removeNodeRecursive(const NodePtr& node) {
        if (!node) return;
        
        // 先删除所有子节点
        auto children = node->getChildren();
        for (const auto& child : children) {
            removeNodeRecursive(child);
        }
        
        // 从映射表中移除当前节点
        nodes_.erase(node->getKey());
    }

    /**
     * @brief 递归计算最大深度
     * @param node 当前节点
     * @return 从当前节点开始的最大深度
     */
    int getMaxDepthRecursive(const NodePtr& node) const {
        if (!node || node->isLeaf()) {
            return 0;
        }
        
        int maxChildDepth = 0;
        for (const auto& child : node->getChildren()) {
            maxChildDepth = std::max(maxChildDepth, getMaxDepthRecursive(child));
        }
        
        return maxChildDepth + 1;
    }
};

} // namespace common
