#pragma once

#include <memory>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <shared_mutex>
#include <atomic>
#include <optional>
#include <algorithm>

namespace common {

/**
 * @brief 扁平化树节点数据结构
 * @tparam KeyType 节点键的类型
 * @tparam DataType 节点数据的类型
 */
template<typename KeyType, typename DataType = void*>
struct FlatTreeNodeData {
    KeyType key;                    ///< 节点键
    DataType data;                  ///< 节点数据
    std::atomic<bool> valid{true};  ///< 节点是否有效（用于软删除）
    
    /**
     * @brief 构造函数
     * @param k 节点键
     * @param d 节点数据
     */
    FlatTreeNodeData(const KeyType& k, const DataType& d = DataType{})
        : key(k), data(d) {}
    
    /**
     * @brief 拷贝构造函数
     */
    FlatTreeNodeData(const FlatTreeNodeData& other)
        : key(other.key), data(other.data), valid(other.valid.load()) {}
    
    /**
     * @brief 赋值操作符
     */
    FlatTreeNodeData& operator=(const FlatTreeNodeData& other) {
        if (this != &other) {
            key = other.key;
            data = other.data;
            valid.store(other.valid.load());
        }
        return *this;
    }
};

/**
 * @brief 扁平化树的关系索引结构
 * @tparam KeyType 节点键的类型
 */
template<typename KeyType>
class FlatTreeRelations {
public:
    using KeySet = std::unordered_set<KeyType>;
    using ChildrenMap = std::unordered_map<KeyType, std::vector<KeyType>>;
    using ParentMap = std::unordered_map<KeyType, KeyType>;
    
    /**
     * @brief 添加父子关系
     * @param parent 父节点键
     * @param child 子节点键
     */
    void addRelation(const KeyType& parent, const KeyType& child) {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        // 添加到子节点映射
        children_[parent].push_back(child);
        
        // 添加到父节点映射
        parents_[child] = parent;
        
        // 更新根节点集合
        roots_.erase(child);  // 子节点不再是根节点
        if (parents_.find(parent) == parents_.end()) {
            roots_.insert(parent);  // 如果父节点没有父节点，则它是根节点
        }
    }
    
    /**
     * @brief 移除节点的所有关系
     * @param key 节点键
     */
    void removeNodeRelations(const KeyType& key) {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        // 移除作为父节点的关系
        if (auto it = children_.find(key); it != children_.end()) {
            for (const auto& child : it->second) {
                parents_.erase(child);
                roots_.insert(child);  // 子节点变成根节点
            }
            children_.erase(it);
        }
        
        // 移除作为子节点的关系
        if (auto it = parents_.find(key); it != parents_.end()) {
            const KeyType& parent = it->second;
            auto& siblings = children_[parent];
            siblings.erase(std::remove(siblings.begin(), siblings.end(), key), siblings.end());
            
            // 如果父节点没有其他子节点且不是根节点，清理空条目
            if (siblings.empty() && parents_.find(parent) != parents_.end()) {
                children_.erase(parent);
            }
            
            parents_.erase(it);
        }
        
        // 从根节点集合中移除
        roots_.erase(key);
    }
    
    /**
     * @brief 获取子节点列表
     * @param parent 父节点键
     * @return 子节点键的向量
     */
    std::vector<KeyType> getChildren(const KeyType& parent) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        if (auto it = children_.find(parent); it != children_.end()) {
            return it->second;
        }
        return {};
    }
    
    /**
     * @brief 获取父节点
     * @param child 子节点键
     * @return 父节点键的可选值
     */
    std::optional<KeyType> getParent(const KeyType& child) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        if (auto it = parents_.find(child); it != parents_.end()) {
            return it->second;
        }
        return std::nullopt;
    }
    
    /**
     * @brief 检查是否为根节点
     * @param key 节点键
     * @return 是否为根节点
     */
    bool isRoot(const KeyType& key) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return roots_.find(key) != roots_.end();
    }
    
    /**
     * @brief 检查是否为叶子节点
     * @param key 节点键
     * @return 是否为叶子节点
     */
    bool isLeaf(const KeyType& key) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        auto it = children_.find(key);
        return it == children_.end() || it->second.empty();
    }
    
    /**
     * @brief 获取所有根节点
     * @return 根节点键的集合
     */
    KeySet getRoots() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return roots_;
    }
    
    /**
     * @brief 清空所有关系
     */
    void clear() {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        children_.clear();
        parents_.clear();
        roots_.clear();
    }
    
    /**
     * @brief 获取关系统计信息
     */
    struct Stats {
        size_t total_nodes = 0;
        size_t root_nodes = 0;
        size_t leaf_nodes = 0;
        size_t internal_nodes = 0;
    };
    
    Stats getStats() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        Stats stats;
        stats.root_nodes = roots_.size();
        
        std::unordered_set<KeyType> all_nodes;
        for (const auto& [parent, children] : children_) {
            all_nodes.insert(parent);
            for (const auto& child : children) {
                all_nodes.insert(child);
            }
        }
        
        stats.total_nodes = all_nodes.size();
        
        for (const auto& node : all_nodes) {
            if (isLeaf(node)) {
                stats.leaf_nodes++;
            } else if (!isRoot(node)) {
                stats.internal_nodes++;
            }
        }
        
        return stats;
    }

private:
    mutable std::shared_mutex mutex_;   ///< 读写锁
    ChildrenMap children_;              ///< 父节点到子节点列表的映射
    ParentMap parents_;                 ///< 子节点到父节点的映射
    KeySet roots_;                      ///< 根节点集合
};

} // namespace common
